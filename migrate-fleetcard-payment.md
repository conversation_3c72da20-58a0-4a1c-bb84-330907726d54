# Fleet Card Payment Feature Migration Guide

## Objective
ย้ายฟีเจอร์ payment ของ Fleet Card (verifyBillPayment, confirmBillPayment) ไปยังแอปพลิเคชันใหม่ โดยต้องการให้สามารถทำงานได้เหมือนเดิม 100% และไม่ตกหล่นเรื่อง log (activity, financial, transaction log)

---

## 1. ฟีเจอร์หลักที่ต้อง migrate

### 1.1 verifyBillPayment (FleetCardValidateService)
- **Endpoint/Method**: `verifyBillPayment`
- **Service**: `fleetCardValidateService.validate(...)`
- **Input**:
  - TopUpVerifyRequest (accountNumber, amount, billerCompCode, etc.)
  - Headers (correlationId, requestUid, etc.)
  - TransactionId
  - BillerTopUpDetail
  - ActivityEvent
  - DepositAccount
- **Process Flow**:
  1. Validate input data (amount, account, etc.)
  2. Check account eligibility
  3. Validate against business rules
  4. Map activity log events
  5. Prepare response
- **Output**: TopUpVerifyResponse
  - transactionId
  - confirmScreenInfo
  - error info (ถ้ามี)

### 1.2 confirmBillPayment (FleetCardConfirmService)
- **Endpoint/Method**: `confirmBillPayment`
- **Service**: `fleetCardConfirmService.ocpPaymentConfirm(...)`
- **Input**:
  - TopUpConfirmRequest (transId, frUuid, etc.)
  - Headers (correlationId, etc.)
  - CRM ID
- **Process Flow**:
  1. Validate confirm request
  2. Process payment transaction
  3. Write logs:
     - Activity Log (event: BILLPAY_FLEETCARD_CONFIRM)
     - Financial Log (amount, currency, etc.)
     - Transaction Log (transId, status, etc.)
  4. Handle response/error
- **Output**: TopUpConfirmResponse
  - transaction info
  - status
  - receipt info
  - error info (ถ้ามี)

## 2. Flow การทำงานเดิม

### 2.1 Overall Request Flow
1. **Request Entry**: รับ request (TopUpVerifyRequest/TopUpConfirmRequest) พร้อม headers (correlationId, crmId, etc.)
2. **Service Selection**: 
   - `BillPaymentSelectService` เช็ค `compCode == "5004"` (TTB Fleet Card) → return `BILLER_PAYMENT_FLEET_CARD`
   - `BillPayTransactionSelectService` route ตาม case `BILLER_PAYMENT_FLEET_CARD`
3. **Business Logic Processing**: ตรวจสอบข้อมูล, ดึงข้อมูล biller/config, ตรวจสอบสิทธิ์, ตรวจสอบวันหมดอายุ, mapping activity log, ดึง account
4. **Fleet Card Processing**: เมื่อเข้า case `BILLER_PAYMENT_FLEET_CARD` จะโยน request ไปยัง service เฉพาะของ fleet card
5. **Response**: Return response กลับไปยัง caller

### 2.2 Fleet Card Verify Flow (Inquiry)
1. **FleetCardInquiryService.inquiry()**:
   - Transform request และจัดการ masked reference fields
   - Setup ETE request (`FleetCardInquiryAndValidateETERequest`) with reference code generation
   - Set card payment amount = "0.00"
   - Call ETE service `/v1.0/internal/fleet/payment/inquiry`
   - Return `PaymentDetailResponse` with default amount details

### 2.3 Fleet Card Validate Flow
1. **FleetCardValidateService.validate()**:
   - Transform request และ prepare Fleet Card ETE request
   - Set card payment amount ตามข้อมูลจริง
   - Extract headers (correlationId, crmId, pre-login flag)
   - Call ETE service → get `FleetCardETEResponse`
   - Fetch customer CRM profile และ validate transaction limits
   - Determine authentication requirements (PIN/common auth)
   - Construct `OCPBillPayment` cache object (fee, reference codes, account info)
   - Update activity event และ log using LogService
   - Return `TopUpVerifyResponse` (amount, fee, transactionId, auth requirements)

### 2.4 Fleet Card Confirm Flow
1. **FleetCardConfirmService.ocpPaymentConfirm()**:
   - Validate confirm request
   - Process payment transaction
   - Initialize logging objects:
     - Activity Log: `ActivityFleetCardConfirmBillPayEvent`
     - Financial Log: `FinancialFleetCard`
     - Transaction Log: `TransactionFleetCardBillPay` (with credit card masking)
   - Write all 3 types of logs
   - Handle response/error cases
   - Return `TopUpConfirmResponse` (transaction info, status, receipt info)

## 3. Logging Requirements
- **Activity Log**: ต้องเขียนทุกครั้งที่มีการ verify/confirm
- **Financial Log**: ต้องเขียนทุกครั้งที่มีการ confirm
- **Transaction Log**: ต้องเขียนทุกครั้งที่มีการ confirm
- **Log Structure**: ต้องตรงตามที่ระบุใน `task.md` (mask sensitive data, field ครบถ้วน)
- **Error Case**: ต้อง log กรณี error ด้วย

## 4. Dependency/Service ที่เกี่ยวข้อง
- **Core Services**:
  - FleetCardValidateService
  - FleetCardConfirmService
  - FleetCardInquiryService
  - FleetCardService (base service)
- **Support Services**:
  - LogService (สำหรับเขียน log)
  - CommonPaymentService (สำหรับ mask credit card, generate transaction ID, etc.)
  - PaymentConfigurationService (สำหรับดึงข้อมูล biller, config, account)
  - BillPaymentValidateTransaction (สำหรับตรวจสอบ transaction limit)
  - CacheService (สำหรับจัดการ cache)
- **External Clients**:
  - BillPayFleetCardFeignClient (สำหรับเรียก ETE Fleet Card service)
  - TopUpFleetCardFeignClient
- **Selection Services**:
  - BillPaymentSelectService (สำหรับเลือก service type)
  - BillPayTransactionSelectService (สำหรับ route request ไป service ที่เหมาะสม)

## 5. Migration Checklist

### 5.1 Core Functionality
- [ ] Endpoint ใหม่รับ input/output schema เหมือนเดิม 100%
- [ ] Flow validate/confirm เหมือนเดิม (รวมถึง error case)
- [ ] Service selection logic (`BILL_COMP_CODE_TTB_FLEET_CARD` → `BILLER_PAYMENT_FLEET_CARD`)
- [ ] Reference code generation algorithm ถูกต้องตาม pattern datetime + 5-digit sequence
- [ ] Request transform logic สำหรับจัดการ masked reference fields

### 5.2 External Integration
- [ ] ETE Fleet Card service integration ผ่าน `BillPayFleetCardFeignClient`
- [ ] Feign client configuration และ endpoint `/v1.0/internal/fleet/payment/inquiry`
- [ ] HTTP headers setup (App ID: A0478, User ID, correlation ID)
- [ ] ETE request structure (`FleetCardInquiryAndValidateETERequest`)
- [ ] ETE response parsing (`FleetCardETEResponse`) และ error handling

### 5.3 Configuration & Constants
- [ ] Application properties สำหรับ ETE Fleet Card service URLs
- [ ] Constants ทั้งหมด (transaction codes, company codes, error codes)
- [ ] Circuit breaker configuration (`FleetCardBillpayVerifyPredicate`)

### 5.4 Logging & Monitoring
- [ ] Log ทั้ง 3 ประเภทถูกเขียนครบถ้วน (activity, financial, transaction)
- [ ] Activity log: `ActivityFleetCardConfirmBillPayEvent` (reference2 = null)
- [ ] Financial log: `FinancialFleetCard` (toAccNo = overrideAccountId, billerRef2 = null)
- [ ] Transaction log: `TransactionFleetCardBillPay` (billerRef1 = masked card, billerRef2 = null)
- [ ] Credit card masking: `commonPaymentService.maskingCreditCardNo()` pattern 123456XXXXXX3456
- [ ] Log field mapping ตามตาราง section 6.5
- [ ] Constants สำหรับ logging ครบถ้วน
- [ ] Error case logging พร้อม exception handling

### 5.5 Data & Cache Management
- [ ] Fleet Card cache structure (`FleetCardCache`) และ field ครบถ้วน
- [ ] Cache key management และ TTL
- [ ] OCPBillPayment object construction สำหรับ confirm flow

### 5.6 Dependencies & Services
- [ ] Integration กับ service อื่น (account, biller, config) ไม่ตกหล่น
- [ ] Transaction limit validation (`BillPaymentValidateTransaction`)
- [ ] Common authentication flow และ PIN validation
- [ ] Credit card masking ผ่าน `CommonPaymentService`

### 5.7 Testing & Documentation
- [ ] Test case ครอบคลุมทุก scenario (success, error, edge case, 5xx circuit breaker)
- [ ] Integration test กับ ETE service
- [ ] Performance test สำหรับ reference code generation
- [ ] Document mapping legacy กับ service ใหม่

## 6. Fleet Card Logging Implementation Details

### 6.1 Activity Log Implementation

**Fleet Card Activity Log เฉพาะ**:
```java
public class ActivityFleetCardConfirmBillPayEvent extends ActivityOCPConfirmBillPayEvent {
    public ActivityFleetCardConfirmBillPayEvent(String correlationId, HttpHeaders headers, String refId, OCPBillPayment ocpCache) {
        super(correlationId, headers, refId, ocpCache);
        
        // Fleet Card specific: ลบ reference2 เสมอ
        this.reference2 = null;
    }
}
```

**Base Activity Log Fields ที่ต้องมี**:
- `activityTypeId`: ได้จาก `getActivityConfirmId(billerGroupType)` 
- `step`: `BILL_PAYMENT_ACTIVITY_CONFIRM_STEP`
- `reference1`: จาก `ocpCache.getPaymentCacheData().getOriginRef1()` (พร้อม format ตาม biller category)
- `reference2`: `null` สำหรับ Fleet Card
- `amount`: จาก `ocpCache.getAmount()` พร้อม comma formatting
- `billerName`: จาก `generateActivityBillerName(nameEn, billerCompCode)`
- `fee`: จาก `ocpCache.getPaymentCacheData().getFeeBillpay()` พร้อม comma formatting
- `flow`: จาก `ocpCache.getPaymentCacheData().getFlow()`
- `refNo`: transaction reference ID
- `fromAccount` หรือ `cardNumber`: ขึ้นอยู่กับ `isCreditCard` flag

### 6.2 Financial Log Implementation

**Fleet Card Financial Log เฉพาะ**:
```java
public class FinancialFleetCard extends FinancialOCP {
    public FinancialFleetCard(String crmId, String refId, OCPBillPayment ocpBillPayment, String correlationId, String transactionDateTime) {
        super(crmId, refId, ocpBillPayment, correlationId, transactionDateTime);
        
        // Fleet Card specific overrides
        String overrideAccountId = ocpBillPayment.getToAccount().getAccountId();
        super.setToAccNo(overrideAccountId);  // ใช้ account ID โดยตรง
        super.setBillerRef2(null);           // ลบ reference2
    }
}
```

**Base Financial Log Fields ที่ต้องมี**:
- `memo`: จาก `ocpBillPayment.getPaymentCacheData().getNote()`
- `fromAccNickName`: จาก `getPaymentCacheData().getFromAccountNickname()`
- `fromAccName`: จาก `getPaymentCacheData().getFromAccountName()`
- `fromAccNo`: Credit Card Number (if credit card) หรือ Account ID
- `fromAccType`: จาก `ocpBillPayment.getFromAccount().getAccountType()`
- `toAccNickName`: จาก `getPaymentCacheData().getToFavoriteNickname()`
- `toAccNo`: Fleet Card ใช้ `overrideAccountId` (account ID เต็ม)
- `toAccType`: คืนค่าจาก `getAccountType(paymentMethod, billerMethod)`
- `txnAmount`: จาก `ocpBillPayment.getAmount()`
- `txnFee`: จาก `getPaymentCacheData().getFeeBillpay()`
- `compCode`: จาก `ocpBillPayment.getCompCode()`
- `billerRef1`: จาก `getPaymentCacheData().getOriginRef1()`
- `billerRef2`: `null` สำหรับ Fleet Card

### 6.3 Transaction Log Implementation

**Fleet Card Transaction Log เฉพาะ**:
```java
public class TransactionFleetCardBillPay extends TransactionOCPBillPay {
    public TransactionFleetCardBillPay(String refId, String crmId, OCPBillPayment ocpBillPayment, String transactionDateTime, String overrideRef1Masking) {
        super(refId, crmId, ocpBillPayment, transactionDateTime);
        
        // Fleet Card specific overrides
        super.setBillerRef1(overrideRef1Masking);  // ใช้ masked credit card number
        super.setBillerRef2(null);                 // ลบ reference2
    }
}
```

**Base Transaction Log Fields ที่ต้องมี**:
- `fromAccountNo`: จาก `shortenAccountId(fromAccount.getAccountId())`
- `fromAccountNickname`: จาก `getPaymentCacheData().getFromAccountNickname()`
- `toAccountNo`: จาก `shortenAccountId(toAccount.getAccountId())`
- `financialTransferAmount`: จาก `ocpBillPayment.getAmount()`
- `financialTransferMemo`: จาก `getPaymentCacheData().getNote()`
- `billerRef1`: Fleet Card ใช้ `overrideRef1Masking` (masked credit card)
- `billerRef2`: `null` สำหรับ Fleet Card
- `activityId`: จาก `getActivityTransConfirmId(billerGroupType)`

### 6.4 Credit Card Masking Process

**Critical Implementation**:
```java
private String maskingCreditCardNo(String creditCardNo) {
    String maskingResult = null;
    try {
        maskingResult = commonPaymentService.maskingCreditCardNo(creditCardNo);
    } catch (TMBCommonException e) {
        logger.info("Error masking creditCard, {}", e);
    }
    return maskingResult;
}
```

**Masking Pattern**: `123456XXXXXX3456` (first 6 + last 4 digits, middle masked with X)

### 6.5 Log Field Sources & Mapping

| Log Type | Fleet Card Specific Behavior | Data Source |
|----------|----------------------------|-------------|
| Activity | `reference2 = null` | Base: `ActivityOCPConfirmBillPayEvent` |
| Financial | `toAccNo = overrideAccountId`, `billerRef2 = null` | Base: `FinancialOCP` |
| Transaction | `billerRef1 = masked credit card`, `billerRef2 = null` | Base: `TransactionOCPBillPay` |

### 6.6 Required Constants for Logging

```java
// Activity Log Constants
ACTIVITY_LOG_BILL_PAY_VIA_CREDIT_CARD_CONFIRM_ID
ACTIVITY_LOG_COMFIRM_TOPUP_CREDIT_CARD
BILLER_GROUP_BILL_PAY
BILL_PAYMENT_ACTIVITY_CONFIRM_STEP

// Account Type Constants
ACCOUNT_TYPE_LOC
D00
PAYMENT_METHOD_TMB_PRODUCT
BILLER_METHOD_TMB_LOAN
```

### 6.7 Log Writing Process

**ใน FleetCardConfirmService**:
1. **Activity Log**: `initActivityEvent()` → `ActivityFleetCardConfirmBillPayEvent`
2. **Financial Log**: `initFinRequest()` → `FinancialFleetCard`
3. **Transaction Log**: `initCalendarEvent()` → `TransactionFleetCardBillPay`

**ลำดับการเรียก**:
1. Mask credit card number (`maskingCreditCardNo`)
2. Create transaction log with masked number
3. Write all logs via `LogService`

### 6.8 Error Case Logging

ต้อง log ในกรณี error ด้วย status และ error details ที่เหมาะสม

## 7. ข้อควรระวัง

- ห้ามเปลี่ยนแปลง business logic เดิม
- ห้ามตกหล่นการเขียน log ทุกประเภท
- ต้อง mask ข้อมูลสำคัญใน log
- ต้อง test ครบทุกกรณี (success, error, edge)

## 8. Configuration Requirements

### 8.1 Application Properties
```properties
# ETE Fleet Card Service Configuration
ete.fleetcard.topup.service.name=ete-fleetcard-service
ete.fleetcard.topup.service.url=https://${ete.domain}:15878
ete.fleetcard.billpay.service.name=ete-fleetcard-fleetcard-service
ete.fleetcard.billpay.service.url=https://${ete.domain}:15942
```

### 8.2 Key Constants
```java
// Fleet Card related constants
BILLER_PAYMENT_FLEET_CARD = "BILLER_PAYMENT_FLEET_CARD"
REQUEST_FLEET_CARD_APP_ID_VALUE = "A0478"
BILL_COMP_CODE_FLEET_CARD = "0012"
BILL_COMP_CODE_TTB_FLEET_CARD = "5004"
BILL_TRAN_CODE_FOR_FLEET_CARD = "8829"
TOP_UP_TRAN_CODE_FOR_FLEET_CARD_VERIFY = "8190"
BILL_TRAN_CODE_FOR_SAVING_FLEET_CARD_CONFIRM = "8629"
BILL_TRAN_CODE_FOR_CURRENT_FLEET_CARD_CONFIRM = "8619"
BILLER_TRAN_CODE_FLEET_CARD_POSTFIX = "9"
```

### 8.3 Error Handling
```java
// Fleet Card specific error response
FLEET_CARD_INQUIRY_ERROR("cardlite_%s", "FleetCard inquiry error", Constants.SERVICE_NAME, null)
```

### 8.4 Feign Client Configuration
```java
@FeignClient(name = "${ete.fleetcard.billpay.service.name}", 
             url = "${ete.fleetcard.billpay.service.url}")
public interface BillPayFleetCardFeignClient {
    @PostMapping(value = "/v1.0/internal/fleet/payment/inquiry", 
                 consumes = "application/json;charset=UTF-8", 
                 produces = "application/json;charset=UTF-8")
    ResponseEntity<FleetCardETEResponse> fleetCardInquiryAndValidate(
        @RequestHeader HttpHeaders headers,
        @RequestBody FleetCardInquiryAndValidateETERequest payload);
}
```

## 9. Critical Implementation Details

### 9.1 Service Selection Logic
Fleet Card payment จะถูกเลือกโดย:
1. **BillPaymentSelectService**: เช็ค `compCode` เป็น `BILL_COMP_CODE_TTB_FLEET_CARD` (5004) → return `BILLER_PAYMENT_FLEET_CARD`
2. **BillPayTransactionSelectService**: เช็ค case `BILLER_PAYMENT_FLEET_CARD` → route ไปยัง `fleetCardInquiryService.inquiry()` หรือ `fleetCardValidateService.validate()`

### 9.2 Reference Code Generation Algorithm
```java
// สร้าง reference code สำหรับ Fleet Card
String transactionId = commonPaymentService.getTransactionId("fleet_card_bill_pay_ref_sequence", 5);
transactionId = transactionId.substring(transactionId.length() - 5); // เอา 5 หลักสุดท้าย
SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
String requestDate = dateFormat.format(new Date(System.currentTimeMillis()));
String referenceCode = requestDate + transactionId; // รวม datetime + 5 digit sequence
```

### 9.3 Request Transform Logic
```java
// จัดการ reference field เมื่อ ref1 ถูก mask และมี ref2
boolean isRef1Masking = StringUtils.lowerCase(transformRequest.getReference1()).contains("x");
boolean ref2NotBlank = StringUtils.isNotBlank(transformRequest.getReference2());
if (ref2NotBlank && isRef1Masking) {
    transformRequest.setReference1(transformRequest.getReference2());
    transformRequest.setReference2(null);
}
```

### 9.4 ETE Request Setup
```java
// Fleet Card ETE request structure
CardPayment cardPayment = new CardPayment();
cardPayment.setReferenceCode(referenceCode);
cardPayment.setCurrency("764"); // THB currency code

FleetCard fleetCard = new FleetCard();
fleetCard.setCardId(paymentDetailRequest.getReference1());
fleetCard.setCardPayment(cardPayment);

FleetCardInquiryAndValidateETERequest request = new FleetCardInquiryAndValidateETERequest();
request.setFleetCard(fleetCard);
```

### 9.5 HTTP Headers for ETE Call
```java
HttpHeaders headers = PaymentServiceUtils.setUpRequestHeader("fleet-card-inquiry");
headers.set(REQUEST_APP_ID, REQUEST_FLEET_CARD_APP_ID_VALUE); // "A0478"
headers.set(REQUEST_USER_ID, OCP_REQUEST_CHANNEL);
```

### 9.6 Fleet Card Cache Structure
```java
FleetCardCache {
    String cardId;
    String accountIdOfCreditCard;
    String amount;
    String fee;
    String referenceCode;
    String compCode;
    String prepaidFlag;
    String accountCardFlag;
}
```

### 9.7 Circuit Breaker Configuration
ใช้ `FleetCardBillpayVerifyPredicate` สำหรับ circuit breaker ที่เช็ค 5xx errors:
```java
public class FleetCardBillpayVerifyPredicate implements Predicate<FeignException> {
    @Override
    public boolean test(FeignException e) {
        return CircuitBreakerUtils.is5xxError(e);
    }
}
```

### 9.8 Key Models and DTOs
ต้องมี models ต่อไปนี้ครบถ้วน:

**Fleet Card Models**:
- `FleetCard` - main fleet card object
- `FleetCardCache` - cache structure
- `FleetCardStatus` - ETE response status
- `FleetCardETEResponse` - ETE service response
- `FleetCardInquiryAndValidateETERequest` - ETE service request
- `CardPayment` - payment details within fleet card

**Log Models**:
- `ActivityFleetCardConfirmBillPayEvent` - activity log for fleet card
- `FinancialFleetCard` - financial log for fleet card
- `TransactionFleetCardBillPay` - transaction log for fleet card

**Standard Models** (ต้องใช้ร่วมกับ Fleet Card):
- `TopUpVerifyRequest` / `TopUpVerifyResponse`
- `TopUpConfirmRequest` / `TopUpConfirmResponse`
- `OCPBillPayment` - cache object สำหรับ confirm
- `BillerTopUpDetailResponse` - biller configuration
- `DepositAccount` - account information
- `ActivityBillPayVerifyEvent` - base activity event

## 10. Original Project Paths for Comparison

**Original Project Path**: `/Users/<USER>/source/one_payment-exp-service`

### 10.1 Core Fleet Card Services
```
src/main/java/com/tmb/oneapp/paymentexpservice/service/
├── FleetCardService.java                          # Base Fleet Card service
├── FleetCardInquiryService.java                   # Inquiry flow implementation  
├── FleetCardValidateService.java                  # Validation flow implementation
├── FleetCardConfirmService.java                   # Confirmation flow implementation
└── BillPayTransactionSelectService.java          # Service routing logic (line 144-146)
```

### 10.2 Fleet Card Models and DTOs
```
src/main/java/com/tmb/oneapp/paymentexpservice/model/
├── fleetcard/
│   ├── FleetCard.java                             # Main Fleet Card object
│   ├── FleetCardCache.java                        # Cache structure
│   ├── FleetCardStatus.java                       # ETE response status
│   ├── FleetCardETEResponse.java                  # ETE service response
│   ├── FleetCardInquiryAndValidateETERequest.java # ETE service request
│   └── CardPayment.java                           # Payment details within fleet card
├── activitylog/
│   ├── ActivityFleetCardConfirmBillPayEvent.java  # Fleet Card activity log
│   └── ActivityOCPConfirmBillPayEvent.java        # Base activity log
├── financiallog/
│   ├── FinancialFleetCard.java                    # Fleet Card financial log
│   └── FinancialOCP.java                          # Base financial log
├── transactionlog/
│   ├── TransactionFleetCardBillPay.java           # Fleet Card transaction log
│   └── TransactionOCPBillPay.java                 # Base transaction log
└── OCPBillPayment.java                            # Main cache object
```

### 10.3 External Integration
```
src/main/java/com/tmb/oneapp/paymentexpservice/client/
├── BillPayFleetCardFeignClient.java               # ETE Fleet Card service client
└── TopUpFleetCardFeignClient.java                 # Top-up Fleet Card client
```

### 10.4 Constants and Configuration
```
src/main/java/com/tmb/oneapp/paymentexpservice/constant/
├── PaymentServiceConstant.java                    # All Fleet Card constants (lines 321, 540-549, 588, 599)
└── ResponseCode.java                              # Error codes (line 72)

src/main/resources/
└── application.properties                         # ETE service URLs (lines 316-320)
```

### 10.5 Validation and Predicates
```
src/main/java/com/tmb/oneapp/paymentexpservice/predicate/
└── FleetCardBillpayVerifyPredicate.java           # Circuit breaker predicate

src/main/java/com/tmb/oneapp/paymentexpservice/service/
├── BillPaymentSelectService.java                  # Service selection logic (line 77-78)
└── CommonPaymentService.java                      # Credit card masking method
```

### 10.6 Test Files for Reference
```
src/test/java/com/tmb/oneapp/paymentexpservice/service/
├── FleetCardInquiryServiceTest.java               # Test examples for inquiry flow
├── FleetCardValidateServiceTest.java              # Test examples for validate flow
├── FleetCardConfirmServiceTest.java               # Test examples for confirm flow
├── BillPayTransactionSelectServiceTest.java      # Service selection tests (line 276)
└── CommonPaymentServiceTest.java                  # Masking test examples
```

### 10.7 Key Line References

**Full Paths for Direct Access:**

```bash
# Core Service Files
/Users/<USER>/source/one_payment-exp-service/src/main/java/com/tmb/oneapp/paymentexpservice/service/FleetCardService.java
- Line 105-112: Reference code generation algorithm
- Line 88-103: Request transform logic for masked references  
- Line 114-140: ETE service call implementation

/Users/<USER>/source/one_payment-exp-service/src/main/java/com/tmb/oneapp/paymentexpservice/service/FleetCardConfirmService.java
- Line 36-38: Activity log initialization
- Line 41-43: Financial log initialization
- Line 46-50: Transaction log initialization
- Line 52-60: Credit card masking implementation

/Users/<USER>/source/one_payment-exp-service/src/main/java/com/tmb/oneapp/paymentexpservice/service/BillPayTransactionSelectService.java
- Line 144-146: Fleet Card service routing
- Line 221: Fleet Card confirm routing  
- Line 370: Fleet Card other routing

# Constants and Configuration
/Users/<USER>/source/one_payment-exp-service/src/main/java/com/tmb/oneapp/paymentexpservice/constant/PaymentServiceConstant.java
- Line 321: REQUEST_FLEET_CARD_APP_ID_VALUE = "A0478"
- Line 540: BILL_COMP_CODE_FLEET_CARD = "0012"
- Line 549: BILL_COMP_CODE_TTB_FLEET_CARD = "5004"
- Line 588: BILLER_PAYMENT_FLEET_CARD = "BILLER_PAYMENT_FLEET_CARD"

/Users/<USER>/source/one_payment-exp-service/src/main/resources/application.properties
- Line 316-320: ETE Fleet Card service configuration
```

### 10.8 Critical Implementation Sections

```bash
# Logging Base Classes
/Users/<USER>/source/one_payment-exp-service/src/main/java/com/tmb/oneapp/paymentexpservice/model/activitylog/ActivityOCPConfirmBillPayEvent.java
- Line 20-44: Base activity log setup method

/Users/<USER>/source/one_payment-exp-service/src/main/java/com/tmb/oneapp/paymentexpservice/model/financiallog/FinancialOCP.java
- Line 21-24: Financial log constructor
- Line 26-90: Complete field setup logic

/Users/<USER>/source/one_payment-exp-service/src/main/java/com/tmb/oneapp/paymentexpservice/model/transactionlog/TransactionOCPBillPay.java
- Line 7-10: Transaction log constructor
- Line 12-25: Base transaction field setup

# Fleet Card Specific Models
/Users/<USER>/source/one_payment-exp-service/src/main/java/com/tmb/oneapp/paymentexpservice/model/fleetcard/FleetCardCache.java
- Line 14-22: All cache fields structure

/Users/<USER>/source/one_payment-exp-service/src/main/java/com/tmb/oneapp/paymentexpservice/model/activitylog/ActivityFleetCardConfirmBillPayEvent.java
- Line 10: Fleet Card specific reference2 = null

/Users/<USER>/source/one_payment-exp-service/src/main/java/com/tmb/oneapp/paymentexpservice/model/financiallog/FinancialFleetCard.java  
- Line 9-12: Fleet Card specific financial log overrides

/Users/<USER>/source/one_payment-exp-service/src/main/java/com/tmb/oneapp/paymentexpservice/model/transactionlog/TransactionFleetCardBillPay.java
- Line 9-10: Fleet Card specific transaction log overrides
```

## 11. Reference

- ดูตัวอย่าง log structure และ schema ที่ `task.md`
- **สำคัญ**: เมื่อต้องการ compare หรือ check parameter/field ให้ดูที่ path ใน section 10 ข้างต้น
- หากมีการเปลี่ยนแปลง schema หรือ business logic ต้องแจ้งทีมที่เกี่ยวข้องและ update เอกสารนี้

## 11. Detailed Service Implementation

### 9.1 FleetCardInquiryService

- **Purpose**: Handles the inquiry flow for Fleet Card payments. It extends the base FleetCardService.

- **Key Methods**:
  
  - `inquiry(TopUpVerifyRequest paymentDetailRequest)`:
    
    - Transforms the incoming TopUpVerifyRequest using `transformRequest()`.
    
    - Sets up a Fleet Card ETE request via `setUpETERequest()` and adjusts the card payment amount to "0.00".
    
    - Prepares HTTP headers for the external ETE call using `setUpEteRequestHeader()`.
    
    - Executes the external call with `callRequestToETE()`, communicating with the payment backend.
    
    - On success, returns a PaymentDetailResponse constructed by `setupResponse()`, containing default amount details.

- **Notes**:
  
  - This service is primarily used for the initial validation of Fleet Card details without proceeding to actual payment.


### 9.2 FleetCardValidateService

- **Purpose**: Implements the validation flow as part of the verifyBillPayment process for Fleet Card payments.

- **Key Methods**:
  
  - `validate(TopUpVerifyRequest topUpVerifyRequest, HttpHeaders headers, String transId, BillerTopUpDetailResponse billerTopUpDetail, ActivityBillPayVerifyEvent activityEvent, DepositAccount depositAccount)`:
    
    - Transforms the TopUpVerifyRequest and prepares the Fleet Card ETE request.
    
    - Sets the card payment amount based on the request data.
    
    - Extracts necessary values from headers (e.g., correlationId, crmId, pre-login flag).
    
    - Calls the external ETE service to retrieve a FleetCardETEResponse.
    
    - Fetches the customer CRM profile and validates transaction limits via `billPaymentValidateTransaction`.
    
    - Determines if PIN or common authentication is required, and gathers common authentication information if needed using `getCommonAuthenticationInformation()`.
    
    - Constructs an OCPBillPayment cache object to store transaction details, including fee, reference codes, and account info.
    
    - Updates the activity event with fee details and flags, and logs the event using the LogService.
    
    - Returns a TopUpVerifyResponse containing the final amount, fee, transaction ID, and authentication requirements.

- **Notes**:
  
  - This service ensures comprehensive validation and caches transaction data for later confirmation.


### 9.3 FleetCardConfirmService

- **Purpose**: Manages the confirmation flow for Fleet Card payments during the confirmBillPayment process.

- **Key Overrides**:
  
  - `initActivityEvent(String correlationId, HttpHeaders headers, OCPBillPayment ocpCache, String refId)`:
    
    - Returns an instance of `ActivityFleetCardConfirmBillPayEvent` tailored for fleet card confirmations.
  
  - `initFinRequest(String crmId, String correlationId, OCPBillPayment ocpCache, String transactionDateTime, String refId)`:
    
    - Creates a financial log entry using `FinancialFleetCard` to capture payment amount, fee, and related financial data.
  
  - `initCalendarEvent(String crmId, OCPBillPayment ocpCache, String transactionDateTime, String refId)`:
    
    - Generates a transaction log event by creating a `TransactionFleetCardBillPay` object. This method masks sensitive details (e.g., credit card number) using `maskingCreditCardNo()`.

- **Additional Method**:
  
  - `maskingCreditCardNo(String creditCardNo)`:
    
    - Invokes `commonPaymentService.maskingCreditCardNo(creditCardNo)` to mask the number, logging any exceptions encountered in the process.

- **Notes**:
  
  - Through these overrides, the service guarantees that fleet card confirmations are logged distinctly with appropriate activity, financial, and transaction logs.
