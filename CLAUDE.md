# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is `one-payment-exp-service`, a Java-based microservice for handling payment-related functionality within the TMB OneApp ecosystem. It's a Spring Boot 3.5.3 application running on Java 17 with <PERSON><PERSON><PERSON> as the build system.

## Build & Development Commands

### Build and Test
```bash
# Build the project
./gradlew build

# Run tests only
./gradlew test

# Run a specific test class
./gradlew test --tests "ClassName"

# Run tests with coverage report
./gradlew test jacocoTestReport

# Build Docker image
./gradlew docker
```

### Code Quality
```bash
# Run SonarQube analysis (requires SonarQube server)
./gradlew sonarqube

# Generate Jacoco coverage report
./gradlew jacocoTestReport
```

### Running the Application
```bash
# Run with Spring Boot
./gradlew bootRun

# Run specific Spring profile
./gradlew bootRun --args='--spring.profiles.active=dev1-oneapp'
```

The application runs on port 80 by default and exposes Swagger UI at `/swagger-ui.html`.

## Architecture Overview

### Core Business Domains
- **Bill Payment**: Various bill payment services (utilities, Fleet Card, auto loans, etc.)
- **Money Transfer**: Account-to-account transfers, PromptPay, cross-border transfers
- **Top-Up**: Mobile/e-wallet top-up services
- **QR Code**: QR payment and scanning functionality
- **Activity Logging**: Comprehensive logging for financial transactions

### Key Architectural Patterns

#### Validate-Confirm Pattern
All payment operations follow a two-phase pattern:
- **Validate/Verify**: Pre-validation of payment details and limits
- **Confirm**: Actual payment execution with comprehensive logging

#### Service Selection Pattern
Payment routing is handled by selection services:
- `BillPaymentSelectService`: Routes by company/biller code
- `BillPayTransactionSelectService`: Routes to specific payment implementations

#### Comprehensive Logging Strategy
Every payment operation generates three types of logs:
- **Activity Log**: User interaction tracking
- **Financial Log**: Financial transaction details
- **Transaction Log**: Technical transaction metadata

### Package Structure

```
src/main/java/com/tmb/oneapp/paymentexpservice/
├── client/          # Feign clients for external service integration
├── config/          # Spring configuration classes
├── constant/        # Application constants and enums
├── controller/      # REST API controllers (versioned: v1/, v2/)
├── data/           # DTOs and data transfer objects
├── model/          # Domain models organized by business area
│   ├── billpay/    # Bill payment models
│   ├── fleetcard/  # Fleet Card specific models
│   ├── activitylog/# Activity logging models
│   ├── financiallog/# Financial logging models
│   └── transactionlog/# Transaction logging models
├── predicate/      # Circuit breaker and condition predicates
├── repo/           # Data access repositories
├── service/        # Business logic services
├── utils/          # Utility classes and helpers
└── validator/      # Request validation logic
```

## External Integration Architecture

### ETE (Enterprise Transaction Engine) Integration
Primary payment processing backend accessed via Feign clients:
- **Fleet Card ETE**: `BillPayFleetCardFeignClient`
- **Transfer ETE**: `TransferETEFeignClient`
- **Bill Payment ETE**: Various specialized clients

### Key Integration Patterns
- Circuit breaker patterns for resilience (Resilience4j)
- Request/response transformation layers
- Comprehensive error handling with custom exceptions
- Correlation ID tracking across service calls

## Fleet Card Payment Architecture

Fleet Card payments are a critical business domain with specific patterns:

### Service Hierarchy
```
FleetCardService (base)
├── FleetCardInquiryService    # Inquiry flow (amount=0.00)
├── FleetCardValidateService   # Validation with real amounts
└── FleetCardConfirmService    # Payment confirmation with logging
```

### Key Implementation Details
- **Reference Code Generation**: DateTime + 5-digit sequence pattern
- **Credit Card Masking**: `123456XXXXXX3456` format via `CommonPaymentService`
- **Specialized Logging**: Custom log classes for Fleet Card transactions
- **ETE Integration**: Specific endpoints and request/response handling

## Testing Strategy

### Test Organization
- Unit tests: `src/test/java/` mirroring main package structure
- Integration tests: Mock external services (ETE, databases)
- Service tests: Focus on business logic validation
- Controller tests: API contract validation

### Key Testing Patterns
- Mock Feign clients for external service isolation
- Comprehensive test coverage for validate/confirm flows
- Error scenario testing (5xx errors, circuit breaker activation)
- Logging verification for all transaction types

## Configuration Management

### Environment-Specific Configuration
- `application.properties`: Base configuration
- `application-{env}-oneapp.properties`: Environment overrides
- Jasypt encryption for sensitive values

### Key Configuration Areas
- **ETE Service URLs**: Environment-specific backend endpoints
- **Feign Client Settings**: Timeouts, connection pools
- **Circuit Breaker**: Resilience4j configuration
- **Logging**: Structured logging configuration
- **Database**: Oracle database connection settings

## Development Guidelines

### Code Conventions
- Use Lombok for reducing boilerplate
- Follow Spring Boot best practices
- Implement comprehensive error handling
- Maintain backward compatibility with legacy systems

### Business Logic Requirements
- All payment operations must maintain 1:1 compatibility with legacy systems
- Comprehensive logging is mandatory for all financial transactions
- Credit card numbers and sensitive data must be masked in logs
- Error handling must provide meaningful user feedback

### Integration Requirements
- All external calls must use Feign clients with proper circuit breakers
- Request/response correlation IDs must be maintained
- Timeout and retry policies must be configured appropriately

## Key Dependencies

### Spring Ecosystem
- Spring Boot 3.5.3 with Spring Cloud 2025.0.0
- Spring Data JPA for database access
- Spring Cloud OpenFeign for service communication
- Spring Kafka for event streaming

### TMB Common Libraries
- `tmb_common_utility`: Core utilities and exception handling
- `oneapp-redis-client-lib`: Redis caching support
- `one-kafka-lib`: Kafka integration
- `oneapp-activity-lib`: Activity logging framework

### External Libraries
- Resilience4j for circuit breaker patterns
- Jasypt for configuration encryption
- Flying Saucer for PDF generation
- ZXing for QR code generation

## Important Notes

### Fleet Card Migration Context
This repository contains Fleet Card payment functionality that may need migration. The `migrate-fleetcard-payment.md` document provides comprehensive migration requirements including:
- Service routing logic preservation
- Logging structure requirements (activity, financial, transaction)
- ETE integration patterns
- Credit card masking requirements

### Security Considerations
- All sensitive data (card numbers, amounts) must be masked in logs
- Configuration values should be encrypted using Jasypt
- SSL certificates are configured for external service communication
- Authentication and authorization handled via OneApp auth service

### Performance Considerations
- Feign client connection pooling configured for high throughput
- Redis caching for frequently accessed data
- Circuit breakers prevent cascade failures
- Prometheus metrics enabled for monitoring