# Copilot Instructions for one_payment-exp-service

## Project Architecture & Big Picture
- This is a backend payment service (Java, Spring Boot, Gradle) focused on payment, bill pay, and transfer flows for TMB/ttb banking products.
- The codebase is organized by business domain: `model/`, `service/`, `client/`, `controller/`, and `test/`.
- Key flows are split into validate (pre-check) and confirm (commit) for all payment types, especially FleetCard.
- Integration with external systems (ETE) is via FeignClient interfaces, with endpoints and payloads defined in config and model classes.
- Logging is a first-class concern: activity, financial, and transaction logs are written for every confirm, with clear JSON structures (see `task.md`).
- All business logic is expected to map 1:1 with legacy `one_payment-exp-service` for compatibility.

## Developer Workflows
- **Build:** Use Gradle (`./gradlew build`) for compilation and packaging. Dockerfile is provided for container builds.
- **Test:** Run unit and integration tests with `./gradlew test`. Tests are organized under `src/test/java/` and use mock clients for ETE.
- **Run:** Local dev uses Spring profiles and `.env`/`application.properties` for config. External dependencies (ETE, DB) are mocked or configured per environment.
- **Debug:** Key flows (validate/confirm) can be debugged by tracing service and client classes. Logs are structured and should be inspected for activity/financial/transaction events.

## Project-Specific Patterns & Conventions
- **MCP (Model-Context-Process) Thinking:** All new features/tasks are broken down by model design, context mapping, and process flow. See `task.md` for detailed checklists and JSON schema examples.
- **Logging:** Always log request, response, and error for every external call. Mask sensitive data (cardId, amount) in logs. Use the JSON log structure from `task.md`.
- **API Design:** REST endpoints are versioned and grouped by business function (e.g., `/api/v1/fleetcard/validate`). Request/response schemas must match legacy system exactly.
- **External Integration:** All calls to ETE or other systems use FeignClient interfaces. Endpoints, headers, and payloads are mapped in config and model classes.
- **Testing:** All business scenarios (success, error, edge) must be covered. Mock ETE for integration tests. Validate that logs are written as expected.
- **Activity/Financial/Transaction Log:** For every confirm, write all three logs with the required fields. See `task.md` for structure.

## Key Files & Directories
- `task.md`: The canonical source for requirements, model schema, logging, and workflow conventions.
- `src/main/java/com/tmb/oneapp/paymentexpservice/model/`: All business and integration models.
- `src/main/java/com/tmb/oneapp/paymentexpservice/service/`: Core business logic for payment flows.
- `src/main/java/com/tmb/oneapp/paymentexpservice/client/`: FeignClient interfaces for ETE and other integrations.
- `src/test/java/`: All tests, including integration and mock scenarios.
- `build.gradle`, `Dockerfile`, `application.properties`: Build, run, and config conventions.

## Examples
- See `task.md` for JSON schema of FleetCard requests, responses, and logs.
- Example: To add a new payment method, follow the MCP breakdown in `task.md`, implement model, service, client, and test, and ensure all logs are written.

---

For any new feature or refactor, always:
- Map to legacy logic 1:1 unless explicitly changing
- Follow the MCP checklist in `task.md`
- Ensure all logs and tests are present
- Document any new conventions in `task.md` or this file
