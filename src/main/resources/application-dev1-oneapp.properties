server.servlet.context-path =/apis/payment
server.port=80
jasypt.encryptor.password=RTHQB4FG92
spring.application.name = payment-exp-service
spring.application.desciption=This service functionality to handle transfer functionality between accounts
kafka.prefix.topic=

springfox.documentation.swagger.v2.path=/v2/api-docs

#OCP Internal domain
oneapp.ocp.domain=dev1-oneapp.svc

#CBO domain *************
cbo.domain=crossborder-gateway-sit.apps.dc2d1.tmbcps.com

#account details service
spring.service.name=customers-product-holdings-get
spring.account.details.url=https://stmetead1.tmbbank.local:15636

#CacheConfig
cache.endpoint=http://cache-service.dev1-oneapp.svc
cache.name=cache-service
feign.httpclient.enabled=true
com.tmb.oneapp.transfer-exp-service.transfer-cache.time=300
custom.cache.expire.TRANS_ID=300

#account validation details service
ete.transfer.service.name=fund-transfer-validation-confirmation
ete.transfer.url=https://stmetead1.tmbbank.local:15408

#Cross Border Api Url service
cbo.transfer.service.name=transfer-cross-border
cbo.transfer.url=https://${cbo.domain}:443
cbo.transfer.service.secret.key=oneapptest
cbo.check-status.thread-seize=10
cbo.check-status.process-time=15
cbo.check-status.record-per-round=100

#get account info
account.info.name=account-service
account.info.endpoint=http://accounts-service.dev1-oneapp.svc

#FinancialLogConfig
fin.transfer.name=financial-service
fin.transfer.endpoint=http://financial-service.dev1-oneapp.svc

#CommonLogConfig
common.transfer.name=common-service
common.transfer.endpoint=http://common-service.dev1-oneapp.svc

#Bank Service
bank.service.name=bank-service
bank.service.endpoint=http://bank-service.${oneapp.ocp.domain}

#report service
report.generate.name=report-service
report.generate.endpoint=http://report-service.${oneapp.ocp.domain}

#oauth service
oauth.name=oneapp-auth-service
oauth.endpoint=http://${oauth.name}.dev1-oneapp.svc

#Accounts Service
feign.accounts.service.url=http://accounts-service.${oneapp.ocp.domain}
feign.accounts.service.name=accounts-service
accounts.service.configuration.url=/v1/accounts-service/configuration

#transfer-service
transfer.service.name=transfer-service
transfer.service.url=http://transfer-service.${oneapp.ocp.domain}


## Oracle settings
spring.datasource.url=*****************************************
spring.datasource.username=custcrmusr
spring.datasource.password=Crm_vit123
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.jpa.show-sql=true
spring.jpa.database=oracle
spring.jpa.properties.hibernate.format_sql=true

#Redis
spring.redis.host=***********
spring.redis.port=6379

utility.common.service.endpoint=http://common-service.dev1-oneapp.svc

#Kafka
spring.kafka.jaas.options.username=appusr
spring.kafka.jaas.options.password=P@ssw0rd@1
spring.kafka.producer.bootstrap-servers=stmoneamqd1.tmbbank.local:9092
spring.kafka.consumer.bootstrap-servers=stmoneamqd1.tmbbank.local:9092
com.tmb.oneapp.paymentexpservice.Threads=10
kafka.groupId=confirmation_generateCSV
kafka.topic=${kafka.prefix.topic}ott_transaction_confirmation
kafka.topic.batch.result.eximBill=${kafka.prefix.topic}ott_batch_result_eximbill
kafka.topic.batch.result.murex=${kafka.prefix.topic}ott_batch_result_murex

#Kafka cashforyou
kafka.cashtransfer.topic=${kafka.prefix.topic}cashtransfer_notification
kafka.cashchillchill.topic=${kafka.prefix.topic}cashchillchill_notification
kafka.cashtransfer.groupId=cashforyou_ct_notification
kafka.cashchillchill.groupId=cashforyou_cc_notification

#account validation details for TD service
ete.transfer.td.service.name=term-deposit-account-withdrawal-inquiry
ete.transfer.td.url=https://stmetead1.tmbbank.local:15788

#promptpay
ete.promptpay.validate.service.name = promptpay-credittransfer-inq
ete.promptpay.validate.url = https://stmetead1.tmbbank.local:15146


#promptpay
ete.promptpay.confirmation.service.name = promptpay-credittransfer-add
ete.promptpay.confirmation.url = https://stmetead1.tmbbank.local:15148

transfer.runningNumber = promptpay_ref_sequence

transaction.statement.service.name=transaction-statement
ete.transaction.statement.url=https://stmetead1.tmbbank.local:15784
transaction.statement.url=/v3.0/internal/deposit/statements/get-statements
transaction.statementdb.url=/apis/finactivity/activities
transaction.txnmemo.url=/apis/finactivity/txnmemo

#RegexforFormattingMasking
mobile_format_regex=(.{3})(.{3})(.{4})$
mobile_format_value=$1-$2-$3
citizen_format_regex=(.{1})(.{4})(.{5})(.{2})(.{1})$
citizen_format_value=$1-$2-$3-$4-$5
account_mask_regex=(.{6})(.{4})$
account_mask_value=xx$2
mobile_mask_regex=(.{6})(.{4})$
mobile_mask_value=xx$2
citizen_mask_value=(.{9})(.{4})$
citizen_mask_regex=xx$2

#ete billpay promptpay
ete.billpay.promptpay.service.validation.url=https://stmetead1.tmbbank.local:15150
ete.billpay.promptpay.service.confirm.url=https://stmetead1.tmbbank.local:15152

#ete billpay legacy
ete.billpay.legacy.service=https://stmetead1.tmbbank.local:15846

#billpay
ete.topup.service.name=ete-topup-service
ete.topup.service.url=https://stmetead1.tmbbank.local:15798

ete.auto.loan.service.name=ete-auto-loan-service
ete.auto.loan.service.url=https://${ete.endpoint}
ete.auto.loan.service.path=/v2/internal/autoloan/payment/verify

#CreditCard-service details
feign.creditcard.service.name=creditcard-service
feign.creditcard.service.url=http://creditcard-service.dev1-oneapp.svc

#credit-card-confirm
ete.credit.card.service.name=ete-credit-card-service
ete.credit.card.service.url=https://stmetead1.tmbbank.local:15870

#DStatement ETE Client 
ete.dstatement.fee.service.name=ete-direct-credit-service
ete.dstatement.fee.url=https://stmetead1.tmbbank.local:15408
ete.dstatement.fee.endpoint=/v1.0/internal/fund-transfer/direct-debit/fee

#ete-fleetcard
ete.fleetcard.topup.service.name=ete-fleetcard-service
ete.fleetcard.topup.service.url=https://stmetead1.tmbbank.local:15878


#ocp-gateway
ocp.gateway.service.name=ocp-gateway
ocp.gateway.service.url=https://stmetead1.tmbbank.local:15628

common.accountnumber.mask = XXX-X-XXXXX-X

pdf.path.statement.withnoteth=classpath:reports/statement_withnote_TH.pdf
pdf.path.statement.withnoteen=classpath:reports/statement_withnote_EN.pdf
pdf.path.statement.withoutnoteth=classpath:reports/statement_withoutnote_TH.pdf
pdf.path.statement.withoutnoteen=classpath:reports/statement_withoutnote_EN.pdf

#customer-exp
customer.exp.service.name=customer-exp
customer.exp.service.url=http://customers-exp-service.dev1-oneapp.svc

#customer-service
customer.service.name=customer-service
customer.service.url=http://customers-service.dev1-oneapp.svc

#payment service
payment.service.name=payment-service
payment.service.endpoint=http://payment-service.${oneapp.ocp.domain}

#Notification Service
notification-service.url=http://notification-service.dev1-oneapp.svc
notification-service.e-noti.send-message.endpoint=/apis/notification/e-noti/sendmessage
notification-service.e-noti.default.channel.th=\u0E17\u0E35\u0E17\u0E35\u0E1A\u0E35 \u0E17\u0E31\u0E0A
notification-service.e-noti.default.channel.en=ttb touch
notification-service.e-noti.default.support.no=1428
notification-service.e-noti.default.info.contactCenter.th=1428 \u0E01\u0E14 5
notification-service.e-noti.default.info.contactCenter.en=1428 press 5
notification-service.e-noti.default.info.overseanumber=+662-241-1428
notification-service.e-noti.default.template.date=dd/MM/yyyy
notification-service.e-noti.default.template.time=HH:mm
notification-service.e-noti.default.tc.url=//**************/users/enotiftp/SIT/MIB/term_and_condition/products/OTT/TC-OTT_product_e1.pdf
notification-service.e-noti.default.code.office=5160-Digital
notification-service.e-noti.default.code.service=OTH-EBK


#IBS Service
ibs.service.url=https://bahub-service.tmbbank.local
ibs.service.token=Basic Df6RGy/eyixN68pHDyn2cp3DXbUpnS/kgyx0o9ck7qiZl6xJ4oulin7VKvOQg3xH70Am793K1vNWWIC/ithC9uqRHW4G8QevovBEv+vgWKxUo7S3v4DZOhJy5bcxTTiR7jC6oKLAjh59KDNdDbbWRHOqJpJfG/Awb1ObcDE+ZFaZ3cz1gJHN43RLffFMyjIOnHflfET7hrlvO26tVrhDScQ9MbPEo8BM7k+CQIcSU9u4uxRuZISK/UfkYZL6PlD/koxF9jwr+IfkPXGaS7trC4jeHU/viU3wa+xkf7mL6fsW9rPQcQWhnSPxJgAQQSIOl+2p3p0qyu41YVW2r1M1kA==


#NCB Bill Payment
ncb.bill.payment.name=ncb-billpayment
ncb.bill.payment.url=https://**************:15628
ncb.bill.payment.validation.path=/ocp-gateway/v3.0/payment/billpay/verify
ncb.bill.payment.confirm.path=/ocp-gateway/v3.0/payment/billpay/confirm

#HP Exp Service
hp-exp-service.service.name=hp-exp-service
hp-exp-service.service.url=http://hp-exp-service.dev1-oneapp.svc

#Service Time
service-endtime=16:00
service-starttime=08:00

#Account condition
account-transfer.active=ACTIVE
account-transfer.inactive=INACTIVE
account-transfer.relationship-code=PRIIND
account-transfer.product-code-one=211
account-transfer.product-code-two=206
account-transfer.allow-transfer-to-other-bank=1

logo-thai-qr-promptpay=classpath:images/logo.png

ott-international-transfer-confirmation-batch-service=ott_transaction_confirmation

ete-verifiy-transaction-url=https://stmetead1.tmbbank.local:15002


#OTT
ott.time.limit=16:00:00

#SFTP
sftp_exim_ftp_user=TMB_fincomgw_uat
sftp_hostname=**************
sftp_port=22
sftp_exim_upload_Path=/users/TMB_fincomgw_uat/OTT/DGW_TO_EXIMBILL/
sftp_exim_upload_Path_four_pm=/users/TMB_fincomgw_uat/OTT/EXIM_NEXT_DAY/
sftp_exim_res_path=/users/TMB_fincomgw_uat/OTT/EXIMBILL_TO_SME/
sftp_exim_ftp_password=TMBpass123
sftp.murex.user=oneappuser
sftp.murex.password=OneApp@Ttb2021
sftp_murex_hostname=************
sftp_murex_port=22
sftp_murex_upload_path=/users/opicftp/stp/touch/import/
sftp_murex_upload_path_four_pm=/users/opicftp/stp/touch/
sftp_murex_res_path=/users/opicftp/stp/touch/export/
sftp_murex_ftp_user=${sftp.murex.user}
sftp_murex_ftp_password=${sftp.murex.password}
sftp_exim_archive_path=/users/TMB_fincomgw_uat/OTT/EXIMBILL_TO_SME_ARCHIVE/
sftp_swift_res_path=/users/TMB_fincomgw_uat/CIB/Swift/
sftp_swift_file_name=CIB_Swift_Code.TXT
ott.generate.csv.retry.limit=5
ott.generate.csv.retry.delay=5

#BatchScheduleService
batch.schedule.name=batch-schedule-service
batch.schedule.endpoint=batch-schedule-service.dev1-oneapp.svc

#prometheus, info
management.endpoint.prometheus.enabled=true
management.endpoints.web.exposure.include=health,info,prometheus


#ete-fcdtransfer
ete.transfer.fcd.service.name=fund-transfer-fcd-confirmation
ete.transfer.fcd.service.url=https://stmetead1.tmbbank.local:15966

#AES Cipher
spring.cipher.iv=B&E(H+MbQeThWmZq
spring.cipher.salt=WaXamSjI7cq9IOurNwgdEJ2hNAXv9Ap6
spring.cipher.passphrase=YO0cbiddpC2DL5goC8kRF2sGA7zNT7iv

private.key.location=keys/rsa_private.key
public.key.location=keys/rsa_public.key

sftp.pool.max.idle=2
sftp.pool.max.total=10
sftp.pool.max.wait.time=7500

validate.duplicate.flag=true
amlo.amount.validate=700000


#interceptor
feign.client.config.default.requestInterceptors=com.tmb.common.interceptor.FeignRequestInterceptor


spring.cloud.stream.bindings.FinancialLogOutput.destination=${kafka.prefix.topic}financial_log
spring.cloud.stream.bindings.FinancialLogOutput.contentType=application/json

spring.cloud.stream.bindings.TransactionLogOutput.destination=${kafka.prefix.topic}transaction_log
spring.cloud.stream.bindings.TransactionLogOutput.contentType=application/json

spring.cloud.stream.bindings.ExchangeTransactionLogOutput.destination=${kafka.prefix.topic}exchange_transaction_log
spring.cloud.stream.bindings.ExchangeTransactionLogOutput.contentType=application/json

spring.cloud.stream.kafka.binder.brokers=stmoneamqd1.tmbbank.local:9092

# Set false to not display "see detail" button if activity type is "activity_bill_payment_topup"
enable.topup.billpay.detail=false

ott.cross-border-api.active-status=true

kafka.topic.ott_fin_transaction=${kafka.prefix.topic}ott_fin_transaction
kafka.groupId.ott_fin_transaction=ott_fin_transaction

kafka.topic.payment_status=${kafka.prefix.topic}payment_status

# apply auth-helper-lib
auth.helper.feign.oneapp.auth.service.url=http://oneapp-auth-service.${oneapp.ocp.domain}
auth.helper.feign.common.service.url=http://common-service.${oneapp.ocp.domain}
auth.helper.feign.oneapp.auth.service.name=auth-helper-oneapp-auth
auth.helper.feign.common.service.name=auth-helper-common
auth.helper.pinvalidation.skip=false

#Product-exp-service details
feign.product.exp.service.name=products-exp-service
feign.product.exp.service.url=http://products-exp-service.${oneapp.ocp.domain}