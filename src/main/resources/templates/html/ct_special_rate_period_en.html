<table style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;" width="100%">
	<tr>
		<td style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;">
			<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${firstperiod.nameEn}">${firstperiod.nameEn}</span>
			<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${firsttier.noOfDaySr}">${firsttier.noOfDaySr}</span>
			<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;" th:text="${day.nameEn}">${day.nameEn}
				&nbsp;</span>
			<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${tierspecial.nameEn}">${tierspecial.nameEn} &nbsp;</span>
			(<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${firsttier.specialRateFromEN}">${firsttier.specialRateFromEN}</span>
			-&nbsp;<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${firsttier.specialRateToEN}">${firsttier.specialRateToEN}</span>):
			<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${firsttier.specialRateIntRate}">${firsttier.specialRateIntRate}</span>%<br />
		</td>
	</tr>
	<tr th:each="tier : ${tiers}">
		<td style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;">
			<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${nextperiod.nameEn}">${nextperiod.nameEn}</span>
			<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${tier.noOfDaySr}">${tier.noOfDaySr}</span>
			<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;" th:text="${day.nameEn}">${day.nameEn}
				&nbsp;</span>
			<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${tierspecial.nameEn}">${tierspecial.nameEn} &nbsp;</span>
			(<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${tier.specialRateFromEN}">${tier.specialRateFromEN}</span>
			-&nbsp;<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${tier.specialRateToEN}">${tier.specialRateToEN}</span>):
			<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${tier.specialRateIntRate}">${tier.specialRateIntRate}</span>%<br />
		</td>
	</tr>
	<tr>
		<td style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;">
			<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${normalratestart.nameEn}">${normalratestart.nameEn} &nbsp;</span>
			(<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${normalratestartEN}">${normalratestartEN}</span>
			<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${normalrateonward.nameEn}">${normalrateonward.nameEn} &nbsp;</span>):
			<span style="margin: 0px; font-family: 'Cordia New'; font-size: 20px;"
				th:text="${normalrates}">${normalrates}</span>%<br />
		</td>
	</tr>
</table>