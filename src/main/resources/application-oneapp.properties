#server.servlet.context-path =/apis/payment
server.port=80
jasypt.encryptor.password=RTHQB4FG92
#OCP Internal domain
oneapp.ocp.domain=oneapp.svc
ete.domain=eteuat.tmbbank.local
kafka.prefix.topic=
#CBO domain *************
cbo.domain=crossborder-gateway-uat1.apps.dc2d1.tmbcps.com
spring.application.name=payment-exp-service
spring.application.desciption=This service functionality to handle transfer functionality between accounts
#account details service
spring.service.name=customers-product-holdings-get
spring.account.details.url=https://${ete.domain}:15636
#CacheConfig
cache.endpoint=http://cache-service.${oneapp.ocp.domain}
cache.name=cache-service
feign.httpclient.enabled=true
feign.metrics.enabled=true
com.tmb.oneapp.transfer-exp-service.transfer-cache.time=300
custom.cache.expire.TRANS_ID=300

#account validation details service
ete.transfer.service.name=fund-transfer-validation-confirmation
ete.transfer.url=https://${ete.domain}:15408
#Cross Border Api Url service
cbo.transfer.service.name=transfer-cross-border
cbo.transfer.url=https://${cbo.domain}:443
cbo.transfer.service.secret.key=oneapptest
cbo.check-status.thread-seize=10
cbo.check-status.process-time=15
cbo.check-status.record-per-round=100

#get account info
account.info.name=account-service
account.info.endpoint=http://accounts-service.${oneapp.ocp.domain}
##ActivityConfig
com.tmb.oneapp.accountservice.service.activity=${kafka.prefix.topic}activity
#FinancialLogConfig
fin.transfer.name=financial-service
fin.transfer.endpoint=http://financial-service.${oneapp.ocp.domain}
#CommonLogConfig
common.transfer.name=common-service
common.transfer.endpoint=http://common-service.${oneapp.ocp.domain}
#Bank Service
bank.service.name=bank-service
bank.service.endpoint=http://bank-service.${oneapp.ocp.domain}
## Oracle settings
spring.datasource.url=
spring.datasource.username=
spring.datasource.password=
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.jpa.show-sql=true
spring.jpa.database=oracle
spring.jpa.properties.hibernate.format_sql=true
#Redis
spring.redis.cluster.nodes=
spring.redis.ssl=true
spring.redis.pool.min-idle=0
spring.redis.pool.max-idle=8
spring.redis.pool.max-total=8
utility.common.service.endpoint=http://common-service.${oneapp.ocp.domain}
#Kafka Producer
spring.kafka.producer.bootstrap-servers=
spring.kafka.producer.ssl.trust-store-location=
spring.kafka.producer.ssl.trust-store-password=
spring.kafka.producer.ssl.key-store-location=
spring.kafka.producer.ssl.key-store-password=
spring.kafka.producer.ssl.key-password=
spring.kafka.producer.security.protocol=SSL
#Kafka Consumer
spring.kafka.consumer.bootstrap-servers=
spring.kafka.consumer.ssl.trust-store-location=
spring.kafka.consumer.ssl.trust-store-password=
spring.kafka.consumer.ssl.key-store-location=
spring.kafka.consumer.ssl.key-store-password=
spring.kafka.consumer.ssl.key-password=
spring.kafka.consumer.security.protocol=
#kafka consumer for OTT insternation transfer
com.tmb.oneapp.paymentexpservice.Threads=10
kafka.groupId=confirmation_generateCSV
kafka.groupId2=confirmation_generateCSV_Murex_USD
kafka.groupId3=confirmation_generateCSV_Murex_NonUSD
kafka.topic=${kafka.prefix.topic}ott_transaction_confirmation
kafka.topic.batch.result.eximBill=${kafka.prefix.topic}ott_batch_result_eximbill
kafka.topic.batch.result.murex=${kafka.prefix.topic}ott_batch_result_murex
#Kafka cashforyou
kafka.cashtransfer.topic=${kafka.prefix.topic}cashtransfer_notification
kafka.cashchillchill.topic=${kafka.prefix.topic}cashchillchill_notification
kafka.cashtransfer.groupId=cashforyou_ct_notification
kafka.cashchillchill.groupId=cashforyou_cc_notification
#account validation details for TD service
ete.transfer.td.service.name=term-deposit-account-withdrawal-inquiry
ete.transfer.td.url=https://${ete.domain}:15788
#promptpay
ete.promptpay.validate.service.name=promptpay-credittransfer-inq
ete.promptpay.validate.url=https://${ete.domain}:15146
#billpay
ete.topup.service.name=ete-topup-service
ete.topup.service.url=https://${ete.domain}:15798
ete.auto.loan.service.name=ete-auto-loan-service
ete.auto.loan.service.url=https://${ete.endpoint}
ete.auto.loan.service.path=/v2/internal/autoloan/payment/verify

#CreditCard-service details
feign.creditcard.service.name=creditcard-service
feign.creditcard.service.url=http://creditcard-service.${oneapp.ocp.domain}
#credit-card-confirm
ete.credit.card.service.name=ete-credit-card-service
ete.credit.card.service.url=https://${ete.domain}:15870
#promptpay
ete.promptpay.confirmation.service.name=promptpay-credittransfer-add
ete.promptpay.confirmation.url=https://${ete.domain}:15148
#payment service
payment.service.name=payment-service
payment.service.endpoint=http://payment-service.${oneapp.ocp.domain}
#transfer-service
transfer.service.name=transfer-service
transfer.service.url=http://transfer-service.${oneapp.ocp.domain}
#ocp-gateway
ocp.gateway.service.name=ocp-gateway
ocp.gateway.service.url=https://${ete.domain}:15628
transfer.runningNumber=promptpay_ref_sequence
transaction.statement.service.name=transaction-statement
ete.transaction.statement.url=https://${ete.domain}:15784
transaction.statement.url=/v3.0/internal/deposit/statements/get-statements
transaction.statementdb.url=/apis/finactivity/activities
transaction.txnmemo.url=/apis/finactivity/txnmemo
#RegexforFormattingMasking
mobile_format_regex=(.{3})(.{3})(.{4})$
mobile_format_value=$1-$2-$3
citizen_format_regex=(.{1})(.{4})(.{5})(.{2})(.{1})$
citizen_format_value=$1-$2-$3-$4-$5
account_mask_regex=(.{3})(.{1})(.{5})(.{1})$
account_mask_value=xxx-x-$3-x
mobile_mask_regex=(.{3})(.{3})(.{4})$
mobile_mask_value=$1-xxx-$3
citizen_mask_regex=(.{9})(.{1})(.{2})(.{1})$
citizen_mask_value=x-xxxx-xxxx$2-$3-$4
#ete billpay promptpay
ete.billpay.promptpay.service.validation.url=https://${ete.domain}:15150
ete.billpay.promptpay.service.confirm.url=https://${ete.domain}:15152
#ete billpay legacy
ete.billpay.legacy.service=https://${ete.domain}:15846
##billpay
#ete.topup.service.name=ete-topup-service
#ete.topup.service.url=https://${ete.domain}:15798
#
#ete.auto.loan.service.name=ete-auto-loan-service
#ete.auto.loan.service.url=https://${ete.endpoint}
#
##credit-card-confirm
#ete.credit.card.service.name=ete-credit-card-service
#ete.credit.card.service.url=https://${ete.domain}:15870
#DStatement ETE Client
ete.dstatement.fee.service.name=ete-direct-credit-service
ete.dstatement.fee.url=https://${ete.domain}:15408
ete.dstatement.fee.endpoint=/v1.0/internal/fund-transfer/direct-debit/fee
#ocp-custom-biller
ocp.service.name=billpay-ocp-service
ocp.service.url=https://${ete.domain}:15628
common.accountnumber.mask=XXX-X-XXXXX-X
pdf.path.statement.withnoteth=classpath:reports/statement_withnote_TH.pdf
pdf.path.statement.withnoteen=classpath:reports/statement_withnote_EN.pdf
pdf.path.statement.withoutnoteth=classpath:reports/statement_withoutnote_TH.pdf
pdf.path.statement.withoutnoteen=classpath:reports/statement_withoutnote_EN.pdf
#customer-exp
customer.exp.service.name=customer-exp
customer.exp.service.url=http://customers-exp-service.${oneapp.ocp.domain}
#customer-service
customer.service.name=customer-service
customer.service.url=http://customers-service.${oneapp.ocp.domain}
#oauth service
oauth.name=oneapp-auth-service
oauth.endpoint=http://${oauth.name}.${oneapp.ocp.domain}
#Accounts Service
feign.accounts.service.url=http://accounts-service.${oneapp.ocp.domain}
feign.accounts.service.name=accounts-service
accounts.service.configuration.url=/v1/accounts-service/configuration
#Notification Service
notification-service.url=http://notification-service.${oneapp.ocp.domain}
notification-service.e-noti.send-message.endpoint=/apis/notification/e-noti/sendmessage
notification-service.e-noti.default.channel.th=\u0E17\u0E35\u0E17\u0E35\u0E1A\u0E35 \u0E17\u0E31\u0E0A
notification-service.e-noti.default.channel.en=ttb touch
notification-service.e-noti.default.support.no=1428
notification-service.e-noti.default.info.contactCenter.th=1428 \u0E01\u0E14 5
notification-service.e-noti.default.info.contactCenter.en=1428 press 5
notification-service.e-noti.default.info.overseanumber=+662-241-1428
notification-service.e-noti.default.template.date=dd/MM/yyyy
notification-service.e-noti.default.template.time=HH:mm
notification-service.e-noti.default.tc.url=//10.200.125.110/users/enotiftp/SIT/MIB/term_and_condition/products/OTT/TC-OTT_product_e1.pdf
notification-service.e-noti.default.code.office=5160-Digital
notification-service.e-noti.default.code.service=OTH-EBK
#IBS Service
ibs.service.url=https://bahub-service.tmbbank.local
#NCB Bill Payment
ncb.bill.payment.name=ncb-billpayment
ncb.bill.payment.url=https://${ete.domain}:15628
ncb.bill.payment.validation.path=/ocp-gateway/v3.0/payment/billpay/verify
ncb.bill.payment.confirm.path=/ocp-gateway/v3.0/payment/billpay/confirm
#HP Exp Service
hp-exp-service.service.name=hp-exp-service
hp-exp-service.service.url=http://hp-exp-service.${oneapp.ocp.domain}
#Service Time
service-endtime=16:00
service-starttime=08:00
#Account condition
account-transfer.active=ACTIVE
account-transfer.inactive=INACTIVE
account-transfer.relationship-code=PRIIND
account-transfer.product-code-one=211
account-transfer.product-code-two=206
account-transfer.allow-transfer-to-other-bank=1
location-path-sftp=/users/TMB_fincomgw_uat/OTT/DGW_TO_EXIMBILL/
location-path-sftp2=/users/opicftp/stp/biz_touch/import/
#BatchScheduleService
batch.schedule.name=batch-schedule-service
batch.schedule.endpoint=batch-schedule-service.${oneapp.ocp.domain}
#AES Cipher
spring.cipher.iv=B&E(H+MbQeThWmZq
spring.cipher.salt=WaXamSjI7cq9IOurNwgdEJ2hNAXv9Ap6
spring.cipher.passphrase=YO0cbiddpC2DL5goC8kRF2sGA7zNT7iv
private.key.location=keys/rsa_private.key
public.key.location=keys/rsa_public.key
sftp.pool.max.idle=2
sftp.pool.max.total=10
sftp.pool.max.wait.time=7500
validate.duplicate.flag=false
amlo.amount.validate=700000
#interceptor
feign.client.config.default.requestInterceptors=com.tmb.common.interceptor.FeignRequestInterceptor
spring.cloud.stream.bindings.FinancialLogOutput.destination=${kafka.prefix.topic}financial_log
spring.cloud.stream.bindings.FinancialLogOutput.contentType=application/json
spring.cloud.stream.bindings.TransactionLogOutput.destination=${kafka.prefix.topic}transaction_log
spring.cloud.stream.bindings.TransactionLogOutput.contentType=application/json
spring.cloud.stream.bindings.ExchangeTransactionLogOutput.destination=${kafka.prefix.topic}exchange_transaction_log
spring.cloud.stream.bindings.ExchangeTransactionLogOutput.contentType=application/json
spring.cloud.stream.kafka.binder.brokers=stmoneamqv1.tmbbank.local:9092
#Add Inbound/OutBound Log
app.api.logging.enable=true
app.api.logging.max-length=10000
app.api.logging.url-patterns=*
app.api.logging.feign.enable=true
app.api.logging.feign.exclude-url-patterns=
# Set false to not display "see detail" button if activity type is "activity_bill_payment_topup"
enable.topup.billpay.detail=false
ott.cross-border-api.active-status=true

kafka.topic.ott_fin_transaction=${kafka.prefix.topic}ott_fin_transaction
kafka.groupId.ott_fin_transaction=ott_fin_transaction

kafka.topic.payment_status=${kafka.prefix.topic}payment_status

# apply auth-helper-lib
auth.helper.feign.oneapp.auth.service.url=http://oneapp-auth-service.${oneapp.ocp.domain}
auth.helper.feign.common.service.url=http://common-service.${oneapp.ocp.domain}
auth.helper.feign.oneapp.auth.service.name=auth-helper-oneapp-auth
auth.helper.feign.common.service.name=auth-helper-common
auth.helper.pinvalidation.skip=false

#Product-exp-service details
feign.product.exp.service.name=products-exp-service
feign.product.exp.service.url=http://products-exp-service.${oneapp.ocp.domain}