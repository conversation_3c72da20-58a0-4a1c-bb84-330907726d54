<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="TH">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
   <!-- <link rel="stylesheet" href="../contents/css/bootstrap.min.css" />
  <script src="../contents/js/jquery.min.js"></script>
  <script src="../contents/js/bootstrap.min.js"></script> -->

  <link rel="stylesheet" href="/css/bootstrap.min.css"/>
  <script src="/js/jquery.min.js"></script>
  <script src="/js/bootstrap.min.js"></script>
  <title>Statement PDF</title>
  <style type="text/css">
    html,
    body {
      margin: 0;
      padding: 0;
    }

    body {
      font-family: "Ekachon";
      line-height: 1.3;
      font-size: 7px;
      color: #002d63;
    }

    div.header {
      display: block;
      position: running(header);
    }

    div.footer {
      display: block;
      text-align: center;
      position: running(footer);
    }

    .custom-page-start {
      width: auto;
      margin: 0 0px;
      display: block;
    }

    @page {

      content: "";
      background:url('data:image/png;base64,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');
      object-fit: contain;
      opacity: 0.6;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      position: absolute;
      z-index: -1;   
      background-color: #f5f6f7;
      margin-top: 40mm; 

      @top-right {
        font-family: "Ekachon";
        line-height: 1.3;
        font-size: 12px;
        color: #002d63;
        content: "Page " counter(page) " / " counter(pages);
      }

      @top-left {
        content: element(header);
      }
    }

    .table2 {
      width: 100%;
    }

    table, th, td {
      border: 1px solid #dfe6ec;
      border-collapse: collapse;
  
    }
    .thClassBalance, .thClassAmount  {
      text-align: right;
      padding: 8px 10px 7px 54px;
    }

    .thClassDate, .thClassTime, .thClassList, .thClassChannel {

      padding: 8px 19px 7px 10px;
    }

    /* td { text-align: center; } */
    .custom-page-start1 {
      width: auto;
      margin: 0 0px;
      display: block;
      margin-bottom: 10px;
    }
    .div2 {
      float: right;
    }
    .custom-page-start1::after {
      display: block;
      content: ' ';
      clear: both;
    }

    .tdClassSt1, .thClassSt1 {
      padding: 8px 82px 6px 15px;
    }
    .tdClassSt1, .thStatement{
      /* background-color: #ffffff; */
      background: rgba(255, 255, 255, 0.7);
    }

    .thClassSt1 {
      /* background-color: #eff7fc; */
      background: rgba(239, 247, 252, 0.7);
    }

    .thHead {
      /* background-color: #eff7fc; */
      background: rgba(239, 247, 252, 0.7);
    }

    table {
      -fs-table-paginate: paginate;
    }

    .greyColor {
      color: #728092;
    }

    .watermark {
      display: block;
      position: relative;
    }

  </style>
</head>

<body>
  <div class="watermark">
    <div class="row header">
      <div class="col-sm-6" style="margin: 0px 0px 15px;">
        <img class="logo" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAmCAYAAAAmy2KXAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAlqADAAQAAAABAAAAJgAAAAAHjEA0AAAP4ElEQVR4Ae1cC3BVxRn+9px7QxIICVAcBRVBntrcKAHUqhVQtIoPIgO2VWoHLuFVHSk+6oyPUJ9VEOsLExLr2PFRrEiRcSxQeShYFQ1JZABrRBACgoQQIM97z/bbPeR9zsnNjemD3J055+7+rz1n9z///++/mwhMKJNwLzlYnjLDHe2OqVlwdnpIhja7URjCmBF/584cN7wnPDWYCchsdxoxAkW5n7njY5iOHgGjozuIye+cIxBTrM457x3+1jHF6vAh7pwdxBSrc857h791TLE6fIg7Zwcxxeqc897hb+3z7kEMYzqCS3uXkmAsw+vdv3fBeoKltC6tWtDflSbqVISTxKtv64J9VQFA9EVYpkDIYzBECWCWYMuL3zixxGDtG4FWFEteSvHqci6VlsoVRadYwC1UrlucBWtodDmuOoGps3oAtb+kEv0ceysvgJT+OhRU5i6sbhYQCO5hTmwF4FuGwux/1NPEKu0agZPTFQrMA2q+ocI8RyW6pIlSNR8uKU8nzWzI0Boq2Ue83D+k5ryxtusInJyKJeUv+MbdXd/aDSHlhVTCDVSul6HcZ6xEPQInp2JFPRwnGKW8FXsr1mPk7FPbK6qz8scUy23mJS5Adc16nHdHihtJDO4+AjHFch8bhmhyMMLHlmLSUtOLLIZrOQIxxWo5Js0gchx2rLqvGTDWbGUEvNMNAm9zxfSIq4xTkre54lpBCGE8aoJ5sP9IEaUQWAoh1jKXVQJhJSCMwTRJ17P7cbyE92PIu5CemYPPcvZ508WwdSPgrVgSB3keq0PONXEmd8XdWdwhsuteTv8K8Qz8xgNUiiNN4MBqtp/H+TNGIBx+iW4vtRm+oSnRFTXyQQJmNgBjNa8R8FYsL87/C5yYgcJc70RrfvZmjJ79ExyqWUkLdpnrawk5BRfNnYuPFlW60rQVkZVlYPnedFrPZM3azfocH+WVtlXM/yL9yRtjKUtV1IpS1c3IuheOIaHLRLrKvXWgFr9SJuL4MeU2Iy+BYH8Epr3PvNhYR6a3vl3BHYBXqNBz9VVh/MiRzgt48d1Juo+04E1eZP9p3ElqsRhTKffXlvLJ84eQNv0BusQ8dzYdk3H7x6OkBpdxG+kcm0J2YYx6FiO4JY4cQpxGhVLKfDavd5AoI9seCwQXk2+Mlnn0kME+BjFMfNexj/8S8ORULBWot4ypWh/ixG6v4/jRZ6lciS7EQ13gjcHvAcb6egCDSSqWcyxpxl+HUOXFMH1bYFnXoEaE6/m8KlJ+AGFsrydRfUj5YX37f6BykiqWWv1FUVT8FAj+k5zOrgvo4yk1MH0ipMV9R/E0Z3o2aZOoVAtQkPslUoPphOXBMH+DgpwPMWpOL1RV3Uf3ezmskIQ01iEubOfLAsEnaIXOpSsfr/tLmz6Fisf9z7gxEKFfsI9M+MVIfjy1OoEbPrqO/ZQhKysfb+3JYj9XkK/h4zCQSflnkO9+9nct4849tM6DKZMrZfyO10D2N5VfwMfEzcJ5wdF00X8lbQaGXLkJ21c9TCvMd6PiG2IJCpY8pZ8rMO1W8txF+BtcaYf5O410j6MwL9fQBG43wS6jLMWhfp57bfuqe3WNUjT6dq325D01vvpgtLI5KV5xFl2XR5GyF7FpHOR5nIA/s85AnH9NdP6cfvCJbhpnySQtoapqOa1MBiePf20kXiDdZBwD4y0WiX4N7lQDTuE9DT6fjzxqmykNA/rYc2NWKOOQRivZG2/vmUBmKg/y2X69/jLivyOf/WzSH6ckwjASbD7BUyD4hM+wmzQzmVYZCkvOJ6wCCQkF2LFaxX/3EP8yYYVUxoX6I0nPPJOK9BJxm3gpRb6G/e4n7GHWYfDFvFYhqtOoyvzi6SpucC1bKwfxC46uTBlY4sk7qEd53+gkKy6hBtyliAoXRFOwNO7mV/0sJ+8+KomJUPV5TQjSM/20XAtpdcbqr78w9xnOxCLSjG/XFpJECWVIXhfBkGdQZgX8PBKU//yuJv03bxTkfUKuar57May4cirHENa/RkKvCj79p6xn0JI9SnXRSkMFOhchpUzSgA9PoHDJGNx4xmXkyydOfzjKYnkl/QY3f4ZI2++VXdLTi3ZQwjdR78Gl9Trqybt5f4r6OqMrUiVOXYrwHKtGTL4dumGc+GhFI7dkU/lhhXM4Oevper/QIAl78kWlt7tt1EuL6pArP6Uy8YSG+DuPmvWn5bmXfRQhNdM9R6eE6KNCtJggff5zJbS2t7N+GQ7tSUP+kvV0yX/TfQmzxv6luknY8yvMMhonSTfMw20NRSmWh+lHAD+vPKuBPPJaWaj7NYdD3V0Z+vq/GyWXTrJjCleqlgjFE+hxdFRLTAOkMmxe09BqQ+28mWdxQAOuHNLDTboyOSAGXFHNye/OSykCXY0uA/Q9Mflb/SsZz42erdynOo9opyHCXSpIf0TDtn93uv4N8zyZKpLwf60aS9qPOe3v6/jM8I0k3KRbHautl6ITYfujDFvDVNMu4gL9G2d+aLdlSP/6zDi77XCXVomGhmV/Byy7AzY6IephVdX31tcjrUwsG8EXGrepfLgrhyHCvat3b57mSuCCUDz9u1b0ZpzlQqHB43RG3YvCCRcOeb+r0cpYOcmsg4XqrJecit0fMEZUp1ZpXSyMROo0pVx38Mt/HRufOEqcYDsOpTWvMcj+Ld3LVOK2oHDBcbrQNWxz9Vj7EmOdObRKTD2IavittfDHFZGvirD7mNsKclHwONsstFrS/6nmk9bjWqaUD9k4jf9Y12us54mbyViLiwouBhKTbWvaQNhQ64b3+ExH2Ncz7OtWDA82UlQVYxnm8gZqh5pgpJ9x5GcOGGfQJNmNr62COrGydLQzzQmoBeuxqqfOHuhJ1AipaBWPAo0//UAjTIsqh57bNHVffAu0AyAwTb1j0AHTADKF91gZPKatFMCQttZbVqVuQx5GUU4R6wv5wdFFhRORmKj6epUXj09zjIFcIGW67swwdhL2GuvbuZLjRDNo9pFOlfzsAk7bjbRCJlt3E1dNm3QdPs/7Cp++sJ984wnnCg0qjzeEbm0q46P3Ubh4B9tcteFUKs7NhC8k7RbO02HiP2A8+Cvi+mr3CXGA9atsJSdXXTGNKs1jcVGidggMQc8glXucT5er5nEPZfP5qOrqRsX5ioCzdd35Vk6BN+HtZOZoPEpGOWMb6y2+lN4a6e0/jOKRV3BBZFtWJ04h8LVh4touc3dtc8LXwaoX9RvGBe1KKaFdxtp9PTF+tbtFtPnEep1RV8lPr6KUSuIvJHH33SqwLcqN+CPw6q4z4JQrZBGP2L+udzXgK/kXO9mOMdckmYCM8qlUqoI6pVKSDtb2QN7+ia5CFUIpChXmk8qF/e+XT51rB4SNOBRM4RRNnVIp9JjTSjGqtx1uNCJvVmUAWlVdQPM+lft8Cc2QgIqpUoPZhK/k5aFUxBoepzxaCI4BbIuVxWVjwRGlFD+OaEgETTPwJb0dTb3sS74L2U504j3FX4qt6ePR1Wx9pc6HoWljQCvwjZaltkMgR3L97HOSvfG7FIz7+wgnVEuY0KkCJj9VAM6Ugl79eQTqjSUIfMHldFrzlU9jkli96QjYiqVgNx79KZNf/+DAO05iU7a2tWad9gYWDtChUdsYI6CevvFcvFp8WgSUUZII+nFpXo6i7A1RSuiUbA2KpV5/Qtks3l/oiJF4dehdBzJ6rTrlh5ZdbRkYseKiA8XlCT+4bP2sQsxmcMuVVwQlbfq1DH7vrKcUYGwqH4YlnoDw8wgPA+hAcAmt5T74zBUIhZ+0aWUZrSi3TXALcec38AsetLSY/oh7BEWLv66Ht6cSmDUEsva3jBdnRCQmPTMRpj8BrcWpzYQ1VSyFnFC2gPd5zeja1xTiy5EpO67YcM7kXG6KXdk+YU25ueu0auTqC4Nf7O+2hgHb4KbYdrbUKq4wt0FRWhOn/vDCrDgVtWGu6LinplIDYYsBv/UuN43vQFKPl3C0tJTK8w5XYS9ChhfSyWcgJJRCBRBHmlpFT7zfSIfPOoxKHkg0uJ/HJRgG9VmNpQ/W4vxMpnNEF73nODwYoOzjXAcO5erwQ735PnzaQNQagxAn1uj9REVjca8woev7qKpg1jz8IkwmQYVRw1XmZh1/Vhy/FJb5lVZgvVfo3434ygpUmblcuBWjK7d52nBWrKViqcHTlovnmX4ItyjEaiR3n4yXRZlcO9pX+fnORczd/Ka1OYoIb+C5hOH954ox60L2Ziz/8AE8o97eotwfcHvElqp5f6nBjdSDx7hds5I5HpXGeIaKsxUmJ8myntJ1pVhW+E1O2kd85iG0VgtRkPcc9+rORI21lRbF3lMMBFU8e5A88aRZTgtYTgs4lvWzqLzcrFYZcx67EYwdJdMdPt9iplrYh2TqQWXzjc+o2L8n7Sb2cyZPUtxNvOozh3y/hJnIPxipzONzbCPPzUjqOQzlpQcpj4lW3+2kfZnwA/Bjjk5pkCmScmJV2Ix0ecpiflGX88G+aIZpS/M4XyYLvu5XK6VSjEoBEuftuk0YhtoV398WYU1pxX4lQ8tSSqXKlqfLMPTKqzlAWXxu9h1lUe+sYqpI3V9k3agz98m0XleRnHEsi7Akn/VbWiYmRqlUUtys4U4307yHE01FUMenhZp0tX0SR75RmtwQf2T9EeJSqXQqh7WLbbXCJl4ylUJ8Ye6v0bPL9SfEb+MJhDmsU2mrztGKLmU/KlA8jh0aRvk+GEk36JyZ5IkHw3i7LUql+nBWLIVZlrQBacncqTdU1rdYgSIsFeTJhuEfhOXJ8/FmyzNGCfN2vpKA7gOpHA9wAL6PUC7HDd8rHsWrZLTge3NymAM4H35zEF8tm89d0YLGFcB3VMlEtfr7IQN1SRemi9jMnxs4iUxKstjwFLrNDE78GMJLNdzpJsJMQnI/ThUpae3FBlYOsX5CNpOUwjiBD0+yx1R8STpGCnIzrylInX4Pj18vs2WA8pQsLVMdi2aSlmfYKIQflS0zJeWYphHsx+IHkTprgG5HePNeAWYJ9WX8SV8Tj9FPhyewfjEvphhkHz43l+1yH39L+Ludmr0CZtJqKlMlaTyLuKtQWZWHuPf3aNXuzRcz9prAN1KBax++MS9V1H+EQQlHLJ8jtDz+zBEbxeQ3uZ3RSrH/mmamPqOujxPrk59DydWHz8klpEqT8CSA2vtT2zQqo/55rnI5gM4b61r0N4MWSJi2tY8TW1ErFnCMCqm4m2BYO9j+FnG+baiqeYzPosKnHPSNXwe1IZPkK6UCNIpxxaPwxe1FyOL+Yojj4fsaovZa6sCTfHZu39BFmlYxQr5ybvM8ya2bNTBCN1HSTo7jemzJXYq0zH1s92RCaAoqlTLJP+iXM42nafioeLVTmekZxvn7FZ+rBNXWbVj7YFg5HFrURXTNN/GsmHuWWwtrevs3BKttDCz+j2IAAAAASUVORK5CYII="/>
      </div>

      <div>
        <span class="greyColor" style="font-size: 8px;">3000 ถ.พหลโยธิน จอมพล จตุจักร กรุงเทพฯ 10900 โทรศัพท์ 0-2299-1111 </span><br></br>
        <span class="greyColor" style="font-size: 8px;">3000 Phaholyothin RD., Chompon, Chatuchak Bangkok, Thailand 10900 Phone: 0-2299-1111</span>
      </div>
    </div>
  
    <div class="mainTitle" style="text-align: left; width: auto;
    margin: 0px 0px 15px;">
      <span style="font-size: 16px;">Statement Report (Condensed)</span>
    </div>
    <div class="custom-page-start1">
      <div class="row">
        <div class="col-sm-6">
          <div class="col-sm-12">
            <span class="greyColor" style="font-size: 10px;">Account name</span> 
          </div>
          <div class="col-sm-12">
            <span style="font: 14px;" th:text="${accountData.accountName}"> - </span>
          </div>
        </div>
        <div class="div2 col-sm-6">
          <table style="float: right;">
            <tbody>
              <tr>
                <th class="thClassSt1">Account Number</th>
                <td class="tdClassSt1"><span th:text="${accountMasking}"> Account Number </span></td>
              </tr>
              <tr>
                <th class="thClassSt1">Period</th>
                <td class="tdClassSt1"><span th:text="${dateRange[0]}"> Start Date </span><span> - </span> <span th:text="${dateRange[1]}"> End Date </span></td>
              </tr>
              <tr>
                <th class="thClassSt1">Branch</th>
                <td class="tdClassSt1"><span th:text="${accountData.branchNameEn}"> Branch </span></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
    </div>

    <div class="custom-page-start">
      <table class="table2">
        <thead>
          <tr>
            <th class="thClassDate thHead"> DATE </th>
            <th class="thClassTime thHead"> TIME </th>
            <th class="thClassList thHead"> DESCRIPTION </th>
            <th class="thClassChannel thHead"> CHANNEL </th>
            <th class="thClassAmount thHead"> AMOUNT </th>
            <th class="thClassBalance thHead"> BALANCE </th>
          </tr>
        </thead>
        <tbody>
          <!-- <tr th:if="${dataListTxnDate != ''}">
            <td class="thClassDate thStatement"><span th:text="${dateRange[0]}">  </span></td>
            <td class="thClassTime thStatement"><span></span></td>
            <td class="thClassList thStatement"><span> Remaining </span></td>
            <td class="thClassChannel thStatement"><span></span></td>
            <td class="thClassAmount thStatement"><span></span></td>
            <td class="thClassBalance thStatement"><span th:text="${sumAmount}">  </span></td>
          </tr> -->
          <tr th:if="${dataListTxnDate != ''}" th:each="statement : ${dataList}">
            <td class="thClassDate thStatement"><span th:text="${statement.txnDate}">  </span></td>
            <td class="thClassTime thStatement"><span th:text="${statement.timeEn}">  </span></td>
            <td class="thClassList thStatement"><span th:text="${statement.txnDescriptionEn}">  </span></td>
            <td class="thClassChannel thStatement"><span th:text="${statement.channelId}">  </span></td>
            <td class="thClassAmount thStatement"><span th:text="${statement.finTxnAmount}">  </span></td>
            <td class="thClassBalance thStatement"><span th:text="${statement.ledgerBalance}">  </span></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>  
</body>

</html>