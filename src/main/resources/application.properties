#server.servlet.context-path =/apis/payment
server.port=80

server.tomcat.max-connections=8192
server.tomcat.threads.max=200
server.tomcat.threads.min-spare=10

jasypt.encryptor.password=RTHQB4FG92
default.app-version=5.0.0

#OCP Internal domain
oneapp.ocp.domain=-https-internal-dev3-oneapp.apps.ddid1.tmbcps.com
ete.domain=stmetead1.tmbbank.local
ete.endpoint=integrations.sit.tmbcps.com

kafka.prefix.topic=

#CBO domain *************
cbo.domain=crossborder-gateway-uat1.apps.dc2d1.tmbcps.com

spring.application.name = payment-exp-service
spring.application.desciption=This service functionality to handle transfer functionality between accounts
spring.application.description=payment exp service

#feign
feign.httpclient.max-connections=200
feign.httpclient.max-connections-per-route=50
feign.client.config.default.connectTimeout=60000
feign.client.config.default.readTimeout=30000

#account details service
spring.service.name=customers-product-holdings-get
spring.account.details.url=https://${ete.domain}:15636

# OneAppAuthService
oneapp-auth-service.name=oneapp-auth-service
oneapp-auth-service.url=http://oneapp-auth-service.${oneapp.ocp.domain}

#CacheConfig
cache.endpoint=http://cache-service.${oneapp.ocp.domain}
#cache.endpoint=https://apis-portal.oneapp.tmbbank.local
cache.name=cache-service
feign.httpclient.enabled=true
com.tmb.oneapp.transfer-exp-service.transfer-cache.time=300
custom.cache.expire.TRANS_ID=300

#account validation details service
ete.transfer.service.name=fund-transfer-validation-confirmation
ete.transfer.url=https://${ete.domain}:15408

#Cross Border Api Url service
cbo.transfer.service.name=transfer-cross-border
cbo.transfer.url=https://${cbo.domain}:443/fake
cbo.transfer.service.secret.key=oneapptest
cbo.check-status.thread-seize=10
cbo.check-status.process-time=15
cbo.check-status.record-per-round=100

#ETE Fund Transfer Validation
ete.transfer.validation.service.name=fund-transfer-validation
ete.transfer.validation.url=https://${ete.domain}:15408

#ete-fcdtransfer
ete.transfer.fcd.service.name=fund-transfer-fcd-confirmation
ete.transfer.fcd.service.url=https://${ete.domain}:15966

#get account info
account.info.name=account-service
account.info.endpoint=http://accounts-service.${oneapp.ocp.domain}
#account.info.endpoint=https://apis-portal.oneapp.tmbbank.local

##ActivityConfig
com.tmb.oneapp.accountservice.service.activity=${kafka.prefix.topic}activity

#FinancialLogConfig
fin.transfer.name=financial-service
fin.transfer.endpoint=http://financial-service.${oneapp.ocp.domain}
#fin.transfer.endpoint=https://apis-portal.oneapp.tmbbank.local

#CommonLogConfig
common.transfer.name=common-service
common.transfer.endpoint=http://common-service.${oneapp.ocp.domain}
#common.transfer.endpoint=https://apis-portal.oneapp.tmbbank.local

#Bank Service
bank.service.name=bank-service
bank.service.endpoint=http://bank-service.${oneapp.ocp.domain}

#transfer-service
transfer.service.name=transfer-service
transfer.service.url=http://transfer-service.${oneapp.ocp.domain}

#report service
report.generate.name=report-service
report.generate.endpoint=http://report-service.${oneapp.ocp.domain}
#report.generate.endpoint=https://apis-portal.oneapp.tmbbank.local
#report.generate.endpoint=http://localhost:8080

#payment service
payment.service.name=payment-service
payment.service.endpoint=http://payment-service.${oneapp.ocp.domain}

#Accounts Service
feign.accounts.service.url=http://accounts-service.${oneapp.ocp.domain}
feign.accounts.service.name=accounts-service
accounts.service.configuration.url=/v1/accounts-service/configuration

#cms gateway service
cms.gateway.service.name=cms-gateway-service
cms.gateway.service.endpoint=https://dgwapi-sit.tau2904.com

# Hikari Connection Pool configuration
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=3600000
spring.datasource.hikari.minimum-idle=3
spring.datasource.hikari.maximum-pool-size=3
spring.datasource.hikari.connection-test-query=SELECT 1 FROM DUAL
spring.datasource.hikari.pool-name=CRM_HIKARICP_CONNECTION_POOL
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG

## Oracle settings
spring.datasource.url=*****************************************
spring.datasource.username=custcrmusr
spring.datasource.password=CrmPwV1t_2022
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.jpa.show-sql=true
spring.jpa.database=oracle
spring.jpa.properties.hibernate.format_sql=true

#Redis
spring.redis.host=***********
spring.redis.port=6379
spring.redis.pool.min-idle=0
spring.redis.pool.max-idle=8
spring.redis.pool.max-total=8
spring.redis.read-from=replicaPreferred
spring.redis.adaptive-refresh-trigger-timeout=15
spring.redis.periodic-refresh=15
spring.redis.mode=standalone

#Redis Library Feature Configuration
redis.cache.ttl.default=-1
redis.cache.enabled=true
redis.cache.custom.ttl=true
redis.template.enabled=true
redis.prefix.environment.name=${envconfig.oneapp.redis.prefix-default}
redis.prefix.cache-name.enabled=false


utility.common.service.endpoint=http://common-service.${oneapp.ocp.domain}
#utility.common.service.endpoint=https://apis-portal.oneapp.tmbbank.local

#Kafka
spring.kafka.jaas.options.username=appusr
spring.kafka.jaas.options.password=P@ssw0rd@1
spring.kafka.producer.bootstrap-servers=stmoneamqd1.tmbbank.local:9092
spring.kafka.consumer.bootstrap-servers=stmoneamqd1.tmbbank.local:9092
com.tmb.oneapp.paymentexpservice.Threads=10
kafka.groupId=confirmation_generateCSV
kafka.topic=${kafka.prefix.topic}ott_transaction_confirmation
kafka.topic.batch.result.eximBill=${kafka.prefix.topic}ott_batch_result_eximbill
kafka.topic.batch.result.murex=${kafka.prefix.topic}ott_batch_result_murex

#Kafka cashforyou
kafka.cashtransfer.topic=${kafka.prefix.topic}cashtransfer_notification
kafka.cashchillchill.topic=${kafka.prefix.topic}cashchillchill_notification
kafka.cashtransfer.groupId=cashforyou_ct_notification
kafka.cashchillchill.groupId=cashforyou_cc_notification

#account validation details for TD service
ete.transfer.td.service.name=term-deposit-account-withdrawal-inquiry
ete.transfer.td.url=https://${ete.domain}:15788

#promptpay
ete.promptpay.validate.service.name = promptpay-credittransfer-inq
ete.promptpay.validate.url = https://${ete.domain}:15146
ete.promptpay.validate.path = /v1.0/credittransfer/promptpay/validation

#promptpay
ete.promptpay.confirmation.service.name = promptpay-credittransfer-add
ete.promptpay.confirmation.url = https://${ete.domain}:15148
ete.promptpay.confirmation.path = /v1.0/credittransfer/promptpay/confirmation

#promptpay validate otherbank
ete.promptpay.validate.otherbank.service.name = promptpay-credittransfer-otherbank-inq
ete.promptpay.validate.otherbank.url = http://fdis-service${oneapp.ocp.domain}
ete.promptpay.validate.otherbank.path = /v1/fdis-service/promtpay/validation

#promptpay confirmation otherbank
ete.promptpay.confirmation.otherbank.service.name = promptpay-credittransfer-otherbank-add
ete.promptpay.confirmation.otherbank.url = https://fdis-service${oneapp.ocp.domain}
ete.promptpay.confirmation.otherbank.path = /v1/fdis-service/promtpay/confirmation

transfer.runningNumber = promptpay_ref_sequence

transaction.statement.service.name=transaction-statement
ete.transaction.statement.url=https://${ete.domain}:15784

# transation deposit get account
transaction.deposit.get.service.name=deposit-get-account

transaction.statement.url=/v3.0/internal/deposit/statements/get-statements
transaction.statementdb.url=/apis/finactivity/activities
transaction.txnmemo.url=/apis/finactivity/txnmemo

#RegexforFormattingMasking
mobile_format_regex=(.{3})(.{3})(.{4})$
mobile_format_value=$1-$2-$3
credit_card_mask_regex=(.{4})(.{2})(.{2})(.{4})(.{4})$
credit_card_mask_value=$1-$2XX-XXXX-$5
citizen_format_regex=(.{1})(.{4})(.{5})(.{2})(.{1})$
citizen_format_value=$1-$2-$3-$4-$5
account_mask_regex=(.{6})(.{4})$
account_mask_value=xx$2
mobile_mask_regex=(.{6})(.{4})$
mobile_mask_value=xx$2
citizen_mask_value=(.{9})(.{4})$
citizen_mask_regex=xx$2
citizen_format_masking_regex=(.{9})(.)(.{2})(.)$

#ete billpay promptpay
ete.billpay.promptpay.service.validation.url=https://${ete.domain}:15150
ete.billpay.promptpay.service.confirm.url=https://${ete.domain}:15152

#ete billpay legacy
ete.billpay.legacy.service=https://${ete.domain}:15846

#billpay
ete.topup.service.name=ete-topup-service
ete.topup.service.url=https://${ete.domain}:15798

ete.auto.loan.service.name=ete-auto-loan-service
ete.auto.loan.service.url=https://${ete.endpoint}
ete.auto.loan.service.path=/v2/internal/autoloan/payment/verify

#auto loan confirm
ete.auto.loan.confirm.service.name=ete-auto-loan-confirm-service
ete.auto.loan.confirm.service.url=https://${ete.domain}:15868

#promptpay-register-status
ete.promptpay.register.status.service.name=ete-promptpay-status
ete.promptpay.register.status.service.url=https://${ete.domain}:15620

#CreditCard-service details
feign.creditcard.service.name=creditcard-service
feign.creditcard.service.url=http://creditcard-service.${oneapp.ocp.domain}
#feign.creditcard.service.url=https://apis-portal.oneapp.tmbbank.local

#Customer-account-biz
customer.account.biz.name=customer-account-biz
customer.account.biz.endpoint=http://customer-account-biz.${oneapp.ocp.domain}

#Customer-transaction.service
customers.transaction.service.name=customers-transaction-service
customers.transaction.service.endpoint=http://customers-transaction-service.${oneapp.ocp.domain}


#credit-card-confirm
ete.credit.card.service.name=ete-credit-card-service
ete.credit.card.service.url=https://${ete.domain}:15870

#DStatement ETE Client
ete.dstatement.fee.service.name=ete-direct-credit-service
ete.dstatement.fee.url=https://${ete.domain}:15408
ete.dstatement.fee.endpoint=/v1.0/internal/fund-transfer/direct-debit/fee

#ocp-gateway
ocp.gateway.service.name=ocp-gateway
ocp.gateway.service.url=https://${ete.domain}:15628

#ocp-custom-biller
ocp.service.name = billpay-ocp-service
ocp.service.url=https://${ete.domain}:15628

#ocp-gateway-confirm
ocp.gateway.confirm.service.name=ocp-gateway-confirm
ocp.gateway.confirm.service.url=https://${ete.domain}:15628

common.accountnumber.mask = XXX-X-XXXXX-X

pdf.path.statement.withnoteth=classpath:reports/statement_withnote_TH.pdf
pdf.path.statement.withnoteen=classpath:reports/statement_withnote_EN.pdf
pdf.path.statement.withoutnoteth=classpath:reports/statement_withoutnote_TH.pdf
pdf.path.statement.withoutnoteen=classpath:reports/statement_withoutnote_EN.pdf

#customer-exp
customer.exp.service.name=customer-exp
customer.exp.service.url=http://customers-exp-service.${oneapp.ocp.domain}


#customer-service
customer.service.name=customer-service
customer.service.url=http://customers-service.${oneapp.ocp.domain}
#customer.service.url=https://apis-portal.oneapp.tmbbank.local

#Notification Service
notification-service.url=http://notification-service.${oneapp.ocp.domain}
notification-service.e-noti.send-message.endpoint=/apis/notification/e-noti/sendmessage
notification-service.e-noti.default.channel.th=\u0E17\u0E35\u0E17\u0E35\u0E1A\u0E35 \u0E17\u0E31\u0E0A
notification-service.e-noti.default.channel.en=ttb touch
notification-service.e-noti.default.support.no=1428
notification-service.e-noti.default.info.contactCenter.th=1428 \u0E01\u0E14 5
notification-service.e-noti.default.info.contactCenter.en=1428 press 5
notification-service.e-noti.default.info.overseanumber=+662-241-1428
notification-service.e-noti.default.template.date=dd/MM/yyyy
notification-service.e-noti.default.template.time=HH:mm
notification-service.e-noti.default.tc.url=//**************/users/enotiftp/SIT/MIB/term_and_condition/products/OTT/TC-OTT_product_e1.pdf
notification-service.e-noti.default.code.office=5160-Digital
notification-service.e-noti.default.code.service=OTH-EBK

#IBS Service
ibs.service.url=https://bahub-service.tmbbank.local
ibs.service.token=Basic qEIrDf+DOyIYwb0L+XEEi7OW84+l0cKYDpHbHhURHO9haupqqJcEQ2V+zDZeGlrtaoMBP2xYmnUN7SYzsP+2EPvXxiN7KenXd+QtFAtWQQeNhQD+bdg/hC4hKX8zwA5cAfBVn3BDbj0xuGAtTIOwSY/kE9n+H9EiaTtKZLsOjnN89eKiCdbJoEaWqJ46LYOdYPi5toclOhbrosHQqqa2mi8wFL5FK/ua75r4F3TUD4pJoAYPD7H4OREFP8u0VvZ7GK3/oZr3/WCb08D4/I06fSXexjpka9rPuJmONBEQzDi/1kQk9oE6QEjwbVxHpvb85KwlgEwlHPzcGxnhTPESVg==

#ete-fleetcard
ete.fleetcard.topup.service.name=ete-fleetcard-service
ete.fleetcard.topup.service.url=https://${ete.domain}:15878
ete.fleetcard.billpay.service.name=ete-fleetcard-fleetcard-service
ete.fleetcard.billpay.service.url=https://${ete.domain}:15942

#NCB Bill Payment
ncb.bill.payment.name=ncb-billpayment
ncb.bill.payment.url=https://**************:15628
ncb.bill.payment.validation.path=/ocp-gateway/v3.0/payment/billpay/verify


#NCB Bill Payment Confirm
ncb.bill.payment.confirm.name=ncb-billpayment-confirm
ncb.bill.payment.confirm.url=https://**************:15628
ncb.bill.payment.confirm.path=/ocp-gateway/v3.0/payment/billpay/confirm

#HP Exp Service
hp-exp-service.service.name=hp-exp-service
hp-exp-service.service.url=http://hp-exp-service.${oneapp.ocp.domain}
#hp-exp-service.service.url=https://apis-portal.oneapp.tmbbank.local

#Service Time
service-endtime=16:00
service-starttime=08:00

#Account condition
account-transfer.active=ACTIVE
account-transfer.inactive=INACTIVE
account-transfer.relationship-code=PRIIND
account-transfer.product-code-one=211
account-transfer.product-code-two=206
account-transfer.allow-transfer-to-other-bank=1

logo-thai-qr-promptpay=classpath:images/logo.png

ete-verifiy-transaction-url=https://${ete.domain}:15002
#location-path-sftp=/users/TMB_fincomgw_uat/OTT/DGW_TO_EXIMBILL/
location-path-sftp2=/users/opicftp/stp/biz_touch/import/

#OTT
ott.time.limit=16:00:00
location-path-sftp=/users/TMB_fincomgw_uat/OTT/DGW_TO_EXIMBILL/

#SFTP
sftp_user=oneappuser
sftp_password=OneApp@Ttb2021
sftp_exim_ftp_user=${sftp_user}
sftp_hostname=**************
sftp_port=22
sftp_exim_upload_Path=/users/TMB_fincomgw_uat/OTT/DGW_TO_EXIMBILL/UAT/
sftp_exim_upload_Path_four_pm=/users/TMB_fincomgw_uat/OTT/EXIM_NEXT_DAY/
sftp_exim_res_path=/users/TMB_fincomgw_uat/OTT/EXIMBILL_TO_SME/
sftp_exim_ftp_password=${sftp_password}
sftp.murex.user=oneappuser
sftp.murex.password=OneApp@Ttb2021
sftp_murex_hostname=************
sftp_murex_port=22
sftp_murex_upload_path=/users/opicftp/stp/touch/import/
sftp_murex_upload_path_four_pm=/users/opicftp/stp/touch/
sftp_murex_res_path=/users/opicftp/stp/touch/export/
sftp_murex_ftp_user=${sftp.murex.user}
sftp_murex_ftp_password=${sftp.murex.password}
sftp_exim_archive_path=/users/TMB_fincomgw_uat/OTT/EXIMBILL_TO_SME_ARCHIVE/
sftp_swift_res_path=/users/TMB_fincomgw_uat/CIB/Swift/
sftp_swift_file_name=CIB_Swift_Code.TXT
ott.generate.csv.retry.limit=5
ott.generate.csv.retry.delay=5

#BatchScheduleService
batch.schedule.name=batch-schedule-service
batch.schedule.endpoint=http://batch-schedule-service.${oneapp.ocp.domain}

#prometheus, info
management.endpoint.prometheus.enabled=true
management.endpoints.web.exposure.include=health,info,prometheus

#interceptor
feign.client.config.default.requestInterceptors=com.tmb.common.interceptor.FeignRequestInterceptor

#AES Cipher
spring.cipher.iv=B&E(H+MbQeThWmZq
spring.cipher.salt=WaXamSjI7cq9IOurNwgdEJ2hNAXv9Ap6
spring.cipher.passphrase=YO0cbiddpC2DL5goC8kRF2sGA7zNT7iv

private.key.location=keys/rsa_private.key
public.key.location=keys/rsa_public.key

sftp.pool.max.idle=2
sftp.pool.max.total=10
sftp.pool.max.wait.time=7500

validate.duplicate.flag=false

amlo.amount.validate=700000

#oauth service
oauth.name=oneapp-auth-service
oauth.endpoint=https://${oauth.name}-https-internal-uat1-oneapp.apps.ddid1.tmbcps.com

spring.cloud.stream.bindings.FinancialLogOutput.destination=${kafka.prefix.topic}financial_log
spring.cloud.stream.bindings.FinancialLogOutput.contentType=application/json

spring.cloud.stream.bindings.TransactionLogOutput.destination=${kafka.prefix.topic}transaction_log
spring.cloud.stream.bindings.TransactionLogOutput.contentType=application/json

spring.cloud.stream.bindings.ExchangeTransactionLogOutput.destination=${kafka.prefix.topic}exchange_transaction_log
spring.cloud.stream.bindings.ExchangeTransactionLogOutput.contentType=application/json

spring.cloud.stream.kafka.binder.brokers=stmoneamqd1.tmbbank.local:9092

spring.cloud.stream.kafka.binder.configuration.security.protocol=PLAINTEXT
spring.cloud.stream.kafka.binder.configuration.ssl.truststore.location=
spring.cloud.stream.kafka.binder.configuration.ssl.truststore.password=
spring.cloud.stream.kafka.binder.configuration.ssl.keystore.location=
spring.cloud.stream.kafka.binder.configuration.ssl.keystore.password=
spring.cloud.stream.kafka.binder.configuration.ssl.key.password=
#Kafka Cloud Stream Header Mapper
spring.cloud.stream.kafka.binder.headerMapperBeanName=kafkaStreamHeaderMapper

#Add Inbound/OutBound Log
app.api.logging.enable=true
app.api.logging.max-length=10000
app.api.logging.url-patterns=*
app.api.logging.feign.enable=true
app.api.logging.feign.exclude-url-patterns=


############### Circuit Breaker Resilience4j ###########################################################################
circuitbreaker.enabled=false

#default
resilience4j.circuitbreaker.instances.defaultCircuitBreaker.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.configs.defaultCircuitBreaker.sliding-window-type=time_based
resilience4j.circuitbreaker.configs.defaultCircuitBreaker.sliding-window-size=60

#/v1.0/credittransfer/promptpay/validation
resilience4j.circuitbreaker.instances.transferPromptpayValidation.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidation.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidation.enabled=true

#validation bank_cd=Other
resilience4j.circuitbreaker.instances.transferPromptpayValidationToOther.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToOther.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToOther.enabled=true

#validation bank_cd=BBL
resilience4j.circuitbreaker.instances.transferPromptpayValidationToBBL.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToBBL.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToBBL.enabled=true

#validation bank_cd=KBANK
resilience4j.circuitbreaker.instances.transferPromptpayValidationToKBANK.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToKBANK.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToKBANK.enabled=true

#validation bank_cd=KTB
resilience4j.circuitbreaker.instances.transferPromptpayValidationToKTB.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToKTB.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToKTB.enabled=true

#validation bank_cd=SCB
resilience4j.circuitbreaker.instances.transferPromptpayValidationToSCB.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToSCB.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToSCB.enabled=true

#validation bank_cd=BAY
resilience4j.circuitbreaker.instances.transferPromptpayValidationToBAY.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToBAY.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToBAY.enabled=true

#validation bank_cd=GSB
resilience4j.circuitbreaker.instances.transferPromptpayValidationToGSB.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToGSB.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToGSB.enabled=true

#/v3.0/internal/deposit/get-account
resilience4j.circuitbreaker.instances.depositGetAccount.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.depositGetAccount.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.DepositGetAccountPredicate
circuitbreaker.instances.depositGetAccount.enabled=true

#/v1.0/internal/fund-transfer/validation
resilience4j.circuitbreaker.instances.fundTransferValidation.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.fundTransferValidation.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.FundTransferValidationPredicate
circuitbreaker.instances.fundTransferValidation.enabled=true

#/v1.0/internal/fund-transfer/confirmation
resilience4j.circuitbreaker.instances.fundTransferConfirmation.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.fundTransferConfirmation.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.FundTransferConfirmationPredicate
circuitbreaker.instances.fundTransferConfirmation.enabled=true

#/v1.0/credittransfer/promptpay/confirmation
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmation.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmation.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.CreditTransferPromptpayConfirmationPredicate
circuitbreaker.instances.creditTransferPromptpayConfirmation.enabled=true

#confirmation bank_cd=Other
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToOther.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToOther.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.CreditTransferPromptpayConfirmationPredicate
circuitbreaker.instances.creditTransferPromptpayConfirmationToOther.enabled=true

#confirmation bank_cd=BBL
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToBBL.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToBBL.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.CreditTransferPromptpayConfirmationPredicate
circuitbreaker.instances.creditTransferPromptpayConfirmationToBBL.enabled=true

#confirmation bank_cd=KBANK
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToKBANK.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToKBANK.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.CreditTransferPromptpayConfirmationPredicate
circuitbreaker.instances.creditTransferPromptpayConfirmationToKBANK.enabled=true

#confirmation bank_cd=KTB
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToKTB.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToKTB.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.CreditTransferPromptpayConfirmationPredicate
circuitbreaker.instances.creditTransferPromptpayConfirmationToKTB.enabled=true

#confirmation bank_cd=SCB
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToSCB.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToSCB.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.CreditTransferPromptpayConfirmationPredicate
circuitbreaker.instances.creditTransferPromptpayConfirmationToSCB.enabled=true

#confirmation bank_cd=BAY
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToBAY.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToBAY.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.CreditTransferPromptpayConfirmationPredicate
circuitbreaker.instances.creditTransferPromptpayConfirmationToBAY.enabled=true

#confirmation bank_cd=GSB
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToGSB.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.creditTransferPromptpayConfirmationToGSB.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.CreditTransferPromptpayConfirmationPredicate
circuitbreaker.instances.creditTransferPromptpayConfirmationToGSB.enabled=true

#/ocp-gateway/v3.0/payment/billpay/confirm
resilience4j.circuitbreaker.instances.ocpGatewayBillpayConfirm.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.ocpGatewayBillpayConfirm.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.OcpGatewayBillpayConfirmPredicate
circuitbreaker.instances.ocpGatewayBillpayConfirm.enabled=false

#/v3.0/internal/credit-card/bill-payment/confirm
resilience4j.circuitbreaker.instances.creditCardBillPaymentConfirm.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.creditCardBillPaymentConfirm.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.CreditCardBillPaymentConfirmPredicate
circuitbreaker.instances.creditCardBillPaymentConfirm.enabled=false

#/v3.0/internal/deposit/statements/get-statements
resilience4j.circuitbreaker.instances.depositGetStatements.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.depositGetStatements.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.DepositGetStatementsPredicate
circuitbreaker.instances.depositGetStatements.enabled=true

#/ocp-gateway/v3.0/payment/billpay/verify
resilience4j.circuitbreaker.instances.ocpGatewayBillpayVerify.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.ocpGatewayBillpayVerify.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.OcpGatewayBillpayVerifyPredicate
circuitbreaker.instances.ocpGatewayBillpayVerify.enabled=false

#/v3.0/internal/bill-payment/legacy/verify
resilience4j.circuitbreaker.instances.billpayLegacyVerify.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.billpayLegacyVerify.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.BillpayLegacyVerifyPredicate
circuitbreaker.instances.billpayLegacyVerify.enabled=false

#/v3.0/internal/bill-payment/legacy/confirm
resilience4j.circuitbreaker.instances.billpayLegacyConfirm.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.billpayLegacyConfirm.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.BillpayLegacyConfirmPredicate
circuitbreaker.instances.billpayLegacyConfirm.enabled=false

#/v3.0/internal/auto-loan/bill-payment/verify
resilience4j.circuitbreaker.instances.autoLoanBillpayVerify.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.autoLoanBillpayVerify.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.AutoLoanBillpayVerifyPredicate
circuitbreaker.instances.autoLoanBillpayVerify.enabled=false

#/v3.0/internal/auto-loan/bill-payment/confirm
resilience4j.circuitbreaker.instances.autoLoanBillpayConfirm.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.autoLoanBillpayConfirm.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.AutoLoanBillpayConfirmPredicate
circuitbreaker.instances.autoLoanBillpayConfirm.enabled=false

#/v3.0/internal/fleet-card/bill-payment/verify
resilience4j.circuitbreaker.instances.fleetCardBillpayVerify.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.fleetCardBillpayVerify.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.FleetCardBillpayVerifyPredicate
circuitbreaker.instances.fleetCardBillpayVerify.enabled=false

#/v3.0/internal/registered-services/promptpay/on-us/mobile/get-service-status
resilience4j.circuitbreaker.instances.promptpayOnUsMobileGetStatus.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.promptpayOnUsMobileGetStatus.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.PromptpayOnUsMobileGetStatusPredicate
circuitbreaker.instances.promptpayOnUsMobileGetStatus.enabled=false

#/v3.0/internal/registered-services/promptpay/on-us/citizen/get-service-status
resilience4j.circuitbreaker.instances.promptpayOnUsCitizenGetStatus.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.promptpayOnUsCitizenGetStatus.record-failure-predicate=com.tmb.oneapp.paymentexpservice.predicate.PromptpayOnUsCitizenGetStatusPredicate
circuitbreaker.instances.promptpayOnUsCitizenGetStatus.enabled=false

#circuit breaker instances list
circuitbreaker.instances.list=defaultCircuitBreaker,transferPromptpayValidation,transferPromptpayValidationToOther,transferPromptpayValidationToBBL,transferPromptpayValidationToKBANK,transferPromptpayValidationToKTB,transferPromptpayValidationToSCB,transferPromptpayValidationToBAY,transferPromptpayValidationToGSB,depositGetAccount,fundTransferValidation,fundTransferConfirmation,creditTransferPromptpayConfirmation,creditTransferPromptpayConfirmationToOther,creditTransferPromptpayConfirmationToBBL,creditTransferPromptpayConfirmationToKBANK,creditTransferPromptpayConfirmationToKTB,creditTransferPromptpayConfirmationToSCB,creditTransferPromptpayConfirmationToBAY,creditTransferPromptpayConfirmationToGSB,ocpGatewayBillpayConfirm,creditCardBillPaymentConfirm,depositGetStatements,ocpGatewayBillpayVerify,billpayLegacyVerify,billpayLegacyConfirm,autoLoanBillpayVerify,autoLoanBillpayConfirm,fleetCardBillpayVerify,promptpayOnUsMobileGetStatus,promptpayOnUsCitizenGetStatus

########################################################################################################################

# Set false to not display "see detail" button if activity type is "activity_bill_payment_topup"
enable.topup.billpay.detail=false

ott.cross-border-api.active-status=false

#Face recognition
validate.fr.require.app.version=4.3.0
validate.fr.schedule.require.app.version=5.0.0
validate.fr.ott.require.app.version=5.0.0
validate.fr.financial.accu.amount=200000
validate.fr.transfer.trans.limit=50000
validate.fr.schedule.flag=true
validate.fr.topup.flag=true
validate.fr.transfer.flag=true
validate.fr.ott.flag=true

punboon.require.app.version=4.1.0

kafka.topic.ott_fin_transaction=${kafka.prefix.topic}ott_fin_transaction

kafka.topic.payment_status=${kafka.prefix.topic}payment_status

# apply auth-helper-lib
auth.helper.feign.oneapp.auth.service.url=http://oneapp-auth-service.${oneapp.ocp.domain}
auth.helper.feign.common.service.url=http://common-service.${oneapp.ocp.domain}
auth.helper.feign.oneapp.auth.service.name=auth-helper-oneapp-auth
auth.helper.feign.common.service.name=auth-helper-common
auth.helper.pinvalidation.skip=false

#Springdoc
springdoc.api-docs.path=/apis/payment/api-docs
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true
springdoc.swagger-ui.use-root-path=false
springdoc.swagger-ui.operationsSorter=alpha
springdoc.swagger-ui.urls[0].name=payment-exp-service
springdoc.swagger-ui.urls[0].url=/apis/payment/api-docs
swagger.host=https://apis-portal.oneapp.tmbbank.local,http://localhost:${server.port}

#circuit breaker by bank_cd
bank.cd-bbl=02
bank.cd-kbank=04
bank.cd-ktb=06
bank.cd-scb=14
bank.cd-bay=25
bank.cd-gsb=30

#Product-exp-service details
feign.product.exp.service.name=products-exp-service
feign.product.exp.service.url=https://apis-portal.oneapp.tmbbank.local

#Limit QR pay slip per day
qr.verify.max.limit=200

ete.promptpay.billpay.20022.url=https://integrations-sit.ttbbank.local
ete.promptpay.billpay.20022.validate.path=/v3.0/payment/promptpay/billpay/validation
#ete.promptpay.billpay.20022.validate.path=/v1.0/payment/promptpay/billpay/validation
ete.promptpay.billpay.20022.confirmation.path=/v3.0/payment/promptpay/billpay/confirmation
#ete.promptpay.billpay.20022.confirmation.path=/v1.0/payment/promptpay/billpay/confirmation

feign.documents.core.service.name=documents-core-service
feign.documents.core.service.url=http://documents-core-service.${oneapp.ocp.domain}

firebase.service.url=http://firebase-service.${oneapp.ocp.domain}

#ete-biller-service
ete-biller-service-url=https://stmetead1.tmbbank.local:15820
master.biller.ocp.domain=integrations-sit.ttbbank.local
ocp-master-biller-service-url=https://${master.biller.ocp.domain}:443
master-biller-ocp-flag=true

ott.documents.upload.object-id=TradeFinanceTransactionDocuments
ott.documents.upload.repo-id=TMB_UAT

spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=3MB
spring.servlet.multipart.max-request-size=15MB
spring.servlet.multipart.file-size-threshold=4MB
spring.servlet.multipart.location=/data/upload_tmp

ott.waive-fee.receiver-country-same-currency=CAD

#retail-lending-biz
feign.retail.lending.service.name=retail-lending-biz
feign.retail.lending.service.url=http://retail-lending-biz.${oneapp.ocp.domain}

retry.visa.pay.qr.attempts=15
retry.visa.pay.qr.delay=2000

enable.creditcard.visa.pay.qr=false

common.authen.require.app.version=5.12.0

odsi.get-rate.customer-tier.wealth=5,6,7

ete.term.deposit.withdrawal.inquiry.service.name=ete-term-deposit-withdrawal-inquiry
ete.term.deposit.withdrawal.inquiry.service.url=https://${ete.endpoint}

ete.transfer.fcd.term.confirm.service.name=fund-transfer-fcd-term-confirmation
ete.transfer.fcd.term.service.url=https://${ete.endpoint}/v3.0/internal/fund-transfer/fcd/term/confirmation

# Async
app.async.enabled=true
thread.core-pool-size=5
thread.max-pool-size=20
thread.keep-alive-seconds=60
thread.queue-capacity=500
thread.name-prefix=AsyncTask-

cache.exchange.ttl=900