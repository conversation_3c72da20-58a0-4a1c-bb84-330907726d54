package com.tmb.oneapp;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * accounts service main class
 *
 */
@EnableAsync
@EnableRetry
@EnableCaching
@SpringBootApplication
@EnableEncryptableProperties
@EnableFeignClients(defaultConfiguration = com.tmb.common.filter.FeignCommonConfig.class)
@ComponentScan(basePackages = {
		"com.tmb.oneapp.paymentexpservice",
		"com.tmb.common",
		"com.tmb.activity" ,
		"com.ttb.oneapp",
		"com.ttb.common",
		"com.tmb.oneapp.auth",
		"com.tmb.oneapp.bodydecryption"})
public class PaymentExpServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(PaymentExpServiceApplication.class, args);
		setCertificate();
	}

	static void setCertificate() {
		String keyStoreFile = "oneapp-dev.tmbbank.local.jks";
		if (null == System.getProperty("javax.net.ssl.keyStore")) {
			System.setProperty("javax.net.ssl.keyStore", keyStoreFile);
			System.setProperty("javax.net.ssl.keyStorePassword", "changeit");
		}
		if (null == System.getProperty("javax.net.ssl.trustStore")) {
			System.setProperty("javax.net.ssl.trustStore", keyStoreFile);
			System.setProperty("javax.net.ssl.trustStorePassword", "changeit");
		}
	}
}
