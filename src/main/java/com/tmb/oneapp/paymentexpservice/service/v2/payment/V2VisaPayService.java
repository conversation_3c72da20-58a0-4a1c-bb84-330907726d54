package com.tmb.oneapp.paymentexpservice.service.v2.payment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.cache.Transaction;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBEventStatus;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.paymentexpservice.client.PaymentServiceFeignClient;
import com.tmb.oneapp.paymentexpservice.client.RetailLendingBizServiceClient;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.BillerInfoResponse;
import com.tmb.oneapp.paymentexpservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenticationInformation;
import com.tmb.oneapp.paymentexpservice.model.PaymentCacheData;
import com.tmb.oneapp.paymentexpservice.model.ReferenceTopUpResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.VisaConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayVerifyResponse;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityVisaQRPayVerifyEvent;
import com.tmb.oneapp.paymentexpservice.model.billpay.visa.ScsResponse;
import com.tmb.oneapp.paymentexpservice.model.billpay.visa.ScsStatus;
import com.tmb.oneapp.paymentexpservice.model.commonauth.CommonAuthenWithPayloadRequest;
import com.tmb.oneapp.paymentexpservice.model.creditcard.BillPaymentCreditCard;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCard;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCardConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCardFee;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCardInformationResponse;
import com.tmb.oneapp.paymentexpservice.model.creditcard.PayeeCard;
import com.tmb.oneapp.paymentexpservice.model.creditcard.PayerAccount;
import com.tmb.oneapp.paymentexpservice.model.financiallog.FinancialCreditCard;
import com.tmb.oneapp.paymentexpservice.model.transactionlog.TransactionCreditCard;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import com.tmb.oneapp.paymentexpservice.service.BillPaymentValidateTransaction;
import com.tmb.oneapp.paymentexpservice.service.CommonPaymentService;
import com.tmb.oneapp.paymentexpservice.service.LogService;
import com.tmb.oneapp.paymentexpservice.service.OauthService;
import com.tmb.oneapp.paymentexpservice.service.TransactionLimitService;
import com.tmb.oneapp.paymentexpservice.utils.CacheService;
import com.tmb.oneapp.paymentexpservice.utils.CreditCardUtils;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import com.tmb.oneapp.paymentexpservice.utils.TTBEventLogUtils;
import com.tmb.oneapp.paymentexpservice.utils.TtbUtils;
import feign.FeignException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Valid;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.ByteBuffer;
import java.sql.SQLException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import static com.tmb.oneapp.paymentexpservice.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.paymentexpservice.constant.CommonAuthenticationConstant.COMMON_AUTH_VISA_QR_FEATURE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.InternationalTransferConstant.FIN_FAILED_STATUS;
import static com.tmb.oneapp.paymentexpservice.constant.InternationalTransferConstant.FIN_SUBMITTED_STATUS;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACTIVITY_LOG_CONFIRM_VISA_QR_PAY;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACTIVITY_LOG_VALIDATE_VISA_QR_PAY;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILLER_GROUP_BILL_PAY;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_VISA;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.FAILURE;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.N_ALPHABET;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PRE_LOGIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.Y_ALPHABET;
import static java.nio.charset.StandardCharsets.UTF_8;
import static org.springframework.data.util.CastUtils.cast;

/**
 * Service for Visa QR payment processing version 2
 * Provides enhanced validation processes and security for transactions
 * Includes comprehensive fee calculation and transaction data management
 */
@Service
@RequiredArgsConstructor
public class V2VisaPayService {

    private static final TMBLogger<V2VisaPayService> logger = new TMBLogger<>(V2VisaPayService.class);

    private final BillPaymentValidateTransaction billPaymentValidateTransaction;
    private final RetailLendingBizServiceClient retailLendingBizServiceClient;
    private final PaymentServiceFeignClient paymentServiceFeignClient;
    private final TransactionLimitService transactionLimitService;
    private final CommonPaymentService commonPaymentService;
    private final CacheService cacheService;
    private final OauthService oauthService;
    private final LogService logService;

    private static final String BILL_PAYMENT_SCAN_QR_REF_PREFIX = "bill_payment:scan_qr:ref_code:";
    private static final String STATUS_CODE_SUCCESS = PaymentServiceConstant.STATUS_SUCCESS_CODE;

    /**
     * Validates a Visa QR payment request
     *
     * @param requestCorrelationId Correlation ID for tracking the request
     * @param customerCrmId        Customer's CRM ID
     * @param requestHeaders       HTTP request headers
     * @param paymentRequest       Visa QR payment verification request
     * @return Results of the payment request validation
     * @throws TMBCommonException If validation fails
     * @throws SQLException       If a database access error occurs
     */
    public VisaQRPayVerifyResponse validate(@Valid String requestCorrelationId,
                                            @Valid String customerCrmId,
                                            HttpHeaders requestHeaders,
                                            VisaQRPayVerifyRequest paymentRequest) throws TMBCommonException, SQLException {
        long processingStartTime = System.currentTimeMillis();
        String transactionId = paymentRequest.getTransId();

        PaymentCacheData paymentData = retrievePaymentCacheData(requestCorrelationId, transactionId);
        if (Objects.isNull(paymentData)) {
            throw PaymentServiceUtils.failException(ResponseCode.NOT_FOUND);
        }

        PaymentCacheData.VisaData visaTransactionData = paymentData.getVisaData();
        performVisaDataValidation(paymentRequest, visaTransactionData);

        BigDecimal transactionAmount = new BigDecimal(paymentRequest.getAmount());
        BigDecimal feeAmount = computeTransactionFee(visaTransactionData, transactionAmount);

        ActivityVisaQRPayVerifyEvent activityLogEvent = prepareActivityVerifyEvent(
                requestHeaders, paymentRequest, visaTransactionData, feeAmount);

        try {
            CustomerCrmProfile customerProfile = transactionLimitService.fetchTransactionLimit(
                    requestCorrelationId, customerCrmId);

            CommonAuthenResult authenticationResult = billPaymentValidateTransaction.validateIsRequireCommonAuthen(
                    requestHeaders, paymentRequest.getAmount(), false, customerProfile);

            activityLogEvent.setDdpFlagAndPinFreeFlag(authenticationResult);

            CreditCard creditCardInfo = validateCreditCardOwnership(
                    customerCrmId, requestCorrelationId, paymentRequest, activityLogEvent);

            logService.saveLogActivityBillPayEvent(activityLogEvent);

            CommonAuthenticationInformation commonAuthenticationInformation = createAuthenticationInfo(
                    paymentRequest, authenticationResult, customerProfile);

            updateCachedPaymentData(paymentData, false, paymentRequest, feeAmount, 
                    creditCardInfo, visaTransactionData, authenticationResult, commonAuthenticationInformation);

            commonPaymentService.saveDataToCacheAndSecondary(transactionId, paymentData, requestCorrelationId);

            logBusinessEvent(customerCrmId, authenticationResult, processingStartTime);

            return createValidationResponse(transactionId, creditCardInfo, paymentRequest, 
                    false, paymentData);
        } catch (Exception exception) {
            logger.error("Visa QR payment validation failed: {}", exception.getMessage(), exception);
            activityLogEvent.setFailureStatusWithReasonFromException(exception);
            logService.saveLogActivityBillPayEvent(activityLogEvent);
            throw exception;
        }
    }

    /**
     * Retrieves payment data from cache
     */
    private PaymentCacheData retrievePaymentCacheData(String correlationId, String cacheKey) {
        logger.info("Retrieving payment data for key: [{}]", cacheKey);

        try {
            String cachedDataString = cacheService.get(cacheKey);

            if (cachedDataString == null) {
                logger.debug("Cache miss for key [{}], retrieving from secondary storage", cacheKey);
                return fetchDataFromSecondaryStorage(correlationId, cacheKey);
            }

            return (PaymentCacheData) TMBUtils.convertStringToJavaObj(cachedDataString, PaymentCacheData.class);
        } catch (Exception ex) {
            logger.error("Failed to find data in cache: {}", ex.getMessage(), ex);
            return fetchDataFromSecondaryStorage(correlationId, cacheKey);
        }
    }

    /**
     * Retrieves data from secondary storage
     */
    private <T> T fetchDataFromSecondaryStorage(String correlationId, String cacheKey) {
        try {
            return Optional.ofNullable(paymentServiceFeignClient
                            .getDraftDataFromSecondary(correlationId, cacheKey))
                    .map(ResponseEntity::getBody)
                    .<T>map(t -> cast(t.getData()))
                    .orElse(null);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Validates Visa transaction data
     */
    private void performVisaDataValidation(VisaQRPayVerifyRequest request,
                                  PaymentCacheData.VisaData visaData)
            throws TMBCommonException {
        try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<VisaQRPayVerifyRequest>> validationErrors = validator.validate(request);

            if (!validationErrors.isEmpty()) {
                logger.warn("VISA QR validation failed (request validation): {}",
                        validationErrors.stream().map(ConstraintViolation::getMessage).toList());
                throw PaymentServiceUtils.failException(ResponseCode.QR_VISA_ERR_1111);
            }
        } catch (Exception validationException) {
            logger.warn("VISA QR validation failed (error occurred): {}", validationException.getMessage());
            throw PaymentServiceUtils.failException(ResponseCode.QR_VISA_ERR_1111);
        }

        if (!"764".equals(visaData.getCurrencyId())) {
            logger.warn("VISA QR validation failed (invalid currency code)");
            throw PaymentServiceUtils.failException(ResponseCode.QR_VISA_ERR_1111);
        }

        String tag62Data = StringUtils.defaultString(visaData.getPurchaseIdentifier())
                + StringUtils.defaultString(visaData.getMerchantRefId())
                + StringUtils.defaultString(visaData.getTerminalId())
                + StringUtils.defaultString(visaData.getSenderContract());

        if (tag62Data.length() > 99) {
            logger.warn("VISA QR validation failed (Tag62 length exceeds 99)");
            throw PaymentServiceUtils.failException(ResponseCode.QR_VISA_ERR_1111);
        }

        if (!validateLuhnAlgorithm(visaData.getMerchantId())) {
            logger.warn("VISA QR validation failed (invalid merchant ID)");
            throw PaymentServiceUtils.failException(ResponseCode.QR_VISA_ERR_1111);
        }
    }

    /**
     * Calculates transaction fee
     */
    private BigDecimal computeTransactionFee(PaymentCacheData.VisaData visaData, BigDecimal amount) {
        return Optional.ofNullable(visaData.getFeeFlat())
                .filter(StringUtils::isNotBlank)
                .map(BigDecimal::new)
                .orElseGet(() -> Optional.ofNullable(visaData.getFeePercentage())
                        .filter(StringUtils::isNotBlank)
                        .map(percentageValue -> {
                            double feeRate = Double.parseDouble(percentageValue) / 100;
                            return amount.multiply(BigDecimal.valueOf(feeRate))
                                    .setScale(2, RoundingMode.HALF_UP);
                        })
                        .orElse(BigDecimal.ZERO));
    }

    /**
     * Creates activity event for Visa QR validation logging
     */
    private ActivityVisaQRPayVerifyEvent prepareActivityVerifyEvent(
            HttpHeaders headers, VisaQRPayVerifyRequest request, 
            PaymentCacheData.VisaData visaData, BigDecimal fee) {
        
        ActivityVisaQRPayVerifyEvent activityEvent = new ActivityVisaQRPayVerifyEvent(ACTIVITY_LOG_VALIDATE_VISA_QR_PAY, headers);
        String preLoginFlag = headers.getFirst(PRE_LOGIN);
        boolean isPreLogin = "true".equalsIgnoreCase(preLoginFlag);
        
        activityEvent.setFlow("Home-VisaQRScan-" + (isPreLogin ? "Pre" : "Post"));
        activityEvent.setStep("Enter details");
        activityEvent.setCardNumber(request.getCardNumber());
        activityEvent.setBillerName(visaData.getMerchantName());
        activityEvent.setReference1(StringUtils.defaultIfBlank(visaData.getMerchantRefId(), "-"));
        activityEvent.setReference2(StringUtils.defaultIfBlank(visaData.getTerminalId(), "-"));
        activityEvent.setAmount(request.getAmount());
        activityEvent.setFee(fee.toString());
        
        return activityEvent;
    }

    /**
     * Validates credit card ownership
     */
    private CreditCard validateCreditCardOwnership(String crmId,
                                                   String correlationId,
                                                   VisaQRPayVerifyRequest request,
                                                   ActivityVisaQRPayVerifyEvent activityEvent)
            throws TMBCommonException {
        try {
            ResponseEntity<TmbOneServiceResponse<CreditCardInformationResponse>> cardResponseEntity = 
                    retailLendingBizServiceClient.getCustomerCreditCard(
                            crmId, correlationId, Y_ALPHABET, N_ALPHABET, N_ALPHABET);
            
            List<CreditCard> allCreditCards = Optional.ofNullable(cardResponseEntity)
                    .map(ResponseEntity::getBody)
                    .map(TmbOneServiceResponse::getData)
                    .map(CreditCardInformationResponse::getCreditCards)
                    .orElse(Collections.emptyList());

            List<CreditCard> activeCreditCards = CreditCardUtils.getFilteredCards(allCreditCards);

            return activeCreditCards.stream()
                    .filter(card -> card.getAccountId().equalsIgnoreCase(request.getAccountNumber()))
                    .findFirst()
                    .orElseThrow(() -> {
                        logger.warn("No credit card found matching account number: {}", request.getAccountNumber());
                        return PaymentServiceUtils.failException(ResponseCode.CREDIT_CARD_NOT_ELIGIBLE);
                    });
        } catch (TMBCommonException e) {
            logger.error("Failed to retrieve customer credit card information: {}", e.getMessage(), e);
            activityEvent.updateActivityStatus(FAILURE, ResponseCode.CREDIT_CARD_NOT_ELIGIBLE.getMessage());
            logService.saveLogActivityBillPayEvent(activityEvent);
            throw PaymentServiceUtils.failException(ResponseCode.CREDIT_CARD_NOT_ELIGIBLE);
        } catch (Exception e) {
            activityEvent.updateActivityStatus(FAILURE, e.getMessage());
            logService.saveLogActivityBillPayEvent(activityEvent);
            logger.error("Error occurred while retrieving credit card information: {}", e.getMessage(), e);
            throw PaymentServiceUtils.failException(ResponseCode.FAILED);
        }
    }

    /**
     * Creates authentication information if needed
     */
    private CommonAuthenticationInformation createAuthenticationInfo(
            VisaQRPayVerifyRequest request, 
            CommonAuthenResult authResult, 
            CustomerCrmProfile customerProfile) {
        
        if (!authResult.isRequireCommonAuthen()) {
            return null;
        }

        BigDecimal totalAccumulatedUsage = Optional.ofNullable(customerProfile)
                .map(CustomerCrmProfile::getPaymentAccuUsgAmt)
                .orElse(BigDecimal.ZERO)
                .add(new BigDecimal(request.getAmount()));

        CommonAuthenticationInformation authInfo = new CommonAuthenticationInformation();
        authInfo.setFeatureId(COMMON_AUTH_VISA_QR_FEATURE_ID);
        authInfo.setFlowName(COMMON_AUTH_BILL_FLOW_NAME);
        authInfo.setTotalPaymentAccumulateUsage(totalAccumulatedUsage);
        
        return authInfo;
    }

    /**
     * Updates payment data in cache
     */@SuppressWarnings("SameParameterValue")
    private void updateCachedPaymentData(
            PaymentCacheData paymentData, boolean isPinRequired, 
            VisaQRPayVerifyRequest request, BigDecimal fee,
            CreditCard creditCard, PaymentCacheData.VisaData visaData,
            CommonAuthenResult commonAuthenResult, CommonAuthenticationInformation commonAuthenticationInformation) {

        paymentData.setRequirePin(isPinRequired)
                .setNote(request.getNote())
                .setFlow("VISA-QR-Pay")
                .setAmount(request.getAmount())
                .setCardNumber(creditCard.getCardNo())
                .setVisaData(visaData)
                .setFeeBillpay(fee.toString());

        paymentData.getVisaData().setAccountId(request.getAccountNumber());

        paymentData.setRequireCommonAuthentication(commonAuthenResult.isRequireCommonAuthen());
        paymentData.setCommonAuthenticationInformation(commonAuthenticationInformation);
    }

    /**
     * Logs business event
     */
    private void logBusinessEvent(String crmId, CommonAuthenResult authResult, long startTime) {
        Map<String, Object> eventDetails = new HashMap<>();
        eventDetails.put("crmId", crmId);
        eventDetails.put("isForceFr", authResult.getIsForceFR());
        eventDetails.put("isForceDipchip", authResult.getIsForceDipchip());
        eventDetails.put("pinfree", authResult.isPinFree());
        
        int processingTimeMs = (int) (System.currentTimeMillis() - startTime);
        TTBEventLogUtils.sendBusinessEventLog(
                TTBEventStatus.SUCCESS, 
                "/v2/visa/validate", 
                200, 
                processingTimeMs, 
                eventDetails);
    }

    /**
     * Creates Visa QR validation response
     */
    public VisaQRPayVerifyResponse createValidationResponse(
            String transactionId, CreditCard creditCard,
            VisaQRPayVerifyRequest request, boolean isPinRequired,
            PaymentCacheData paymentData) {
        
        VisaQRPayVerifyResponse response = new VisaQRPayVerifyResponse();
        PaymentCacheData.VisaData visaData = paymentData.getVisaData();

        response.setTransId(transactionId);
        response.setIsRequireConfirmPin(isPinRequired);
        response.setMerchantId(visaData.getMerchantId());
        response.setTerminalId(visaData.getTerminalId());
        response.setMerchantName(visaData.getMerchantName());
        response.setMerchantRefId(visaData.getMerchantRefId());
        response.setFee(new BigDecimal(paymentData.getFeeBillpay()));
        response.setCardEmbossingName(creditCard.getCardName());
        response.setAmount(request.getAmount());

        response.setIsRequireCommonAuthen(paymentData.isRequireCommonAuthentication());
        response.setCommonAuthenticationInformation(paymentData.getCommonAuthenticationInformation());

        return response;
    }

    /**
     * Validates a card number using the Luhn Algorithm
     *
     * @param cardNumber The card number to validate
     * @return true if the card number is valid according to the Luhn algorithm, false otherwise
     */
    public static boolean validateLuhnAlgorithm(String cardNumber) {
        if (cardNumber.length() > 8 && cardNumber.length() < 16) {
            cardNumber = cardNumber.substring(0, 6) +
                    StringUtils.leftPad(cardNumber.substring(6), 10, "0");
        }

        int totalSum = 0;
        boolean isAlternatePosition = false;

        for (int i = cardNumber.length() - 1; i >= 0; i--) {
            int currentDigit = Character.getNumericValue(cardNumber.charAt(i));

            if (isAlternatePosition) {
                currentDigit *= 2;
                if (currentDigit > 9) {
                    currentDigit -= 9;
                }
            }

            totalSum += currentDigit;
            isAlternatePosition = !isAlternatePosition;
        }

        return (totalSum % 10 == 0);
    }

    /**
     * Generates a transaction reference ID
     *
     * @param crmId      Customer's CRM ID
     * @param merchantId Merchant ID
     * @return Generated transaction reference ID
     */
    public String generateTransactionRef(String crmId, String merchantId) {
        return String.format("VISA_QR_%s_%s_%s", crmId, merchantId, UUID.randomUUID());
    }

    /**
     * Confirms a Visa QR payment transaction
     *
     * @param customerCrmId           Customer's CRM ID
     * @param correlationId           Correlation ID for tracking
     * @param headers                 HTTP request headers
     * @param confirmResponse         Confirmation response object to be populated
     * @param visaQRPayConfirmRequest Payment confirmation request
     * @return Populated confirmation response
     * @throws TMBCommonException If confirmation fails
     */
    public VisaQRPayConfirmResponse confirm(String customerCrmId,
                                            String correlationId,
                                            HttpHeaders headers,
                                            VisaQRPayConfirmResponse confirmResponse,
                                            VisaQRPayConfirmRequest visaQRPayConfirmRequest)
            throws TMBCommonException {
        logger.info("Starting Visa QR payment confirmation {}", visaQRPayConfirmRequest.getTransId());

        PaymentCacheData paymentData = retrievePaymentCacheData(
                correlationId, visaQRPayConfirmRequest.getTransId());
        confirmResponse.setPaymentCacheData(paymentData);

        if (Objects.isNull(paymentData)) {
            throw PaymentServiceUtils.failException(ResponseCode.NOT_FOUND);
        }

        verifyAuthentication(headers, visaQRPayConfirmRequest, paymentData);

        try {
            VisaConfirmRequest confirmRequest = prepareVisaConfirmRequest(paymentData);

            String transactionDateTime = commonPaymentService.getCurrentDateTime();
            confirmResponse.setTransactionDateTime(transactionDateTime);

            final String referenceID = Transaction.getTransactionId(
                    PaymentServiceConstant.TRANSFER_REFERENCE_NUMBER_PREFIX,
                    PaymentServiceConstant.EIGHT_INT);
            confirmResponse.setReferenceID(referenceID);
            confirmRequest.getCreditCard().getCardSpendToPay().setReferenceCode(referenceID);

            TtbUtils.logJsonNotThrow(logger, "Visa confirmation request: {}", confirmRequest);

            ResponseEntity<TmbOneServiceResponse<Void>> confirmResult =
                    paymentServiceFeignClient.visaConfirm(correlationId, customerCrmId, confirmRequest);

            Optional<TmbOneServiceResponse<Void>> resultBody = Optional.ofNullable(confirmResult.getBody());
            String confirmStatus = STATUS_CODE_SUCCESS.equals(
                    resultBody.map(TmbOneServiceResponse::getStatus)
                            .map(TmbStatus::getCode).orElse(null)) ? "success" : "failure";
            
            logger.info("Visa QR payment confirmation result {}: {}", 
                    confirmStatus, visaQRPayConfirmRequest.getTransId());

            return confirmResponse;
        } catch (FeignException feignEx) {
            logger.error("Visa QR payment confirmation failed: {}", feignEx.getMessage(), feignEx);
            throw handleVisaError(feignEx, confirmResponse);
        } finally {
            removeFromCacheStorage(correlationId, visaQRPayConfirmRequest.getTransId());
        }
    }

    /**
     * Verifies authentication for Visa QR transaction
     */
    private void verifyAuthentication(
            HttpHeaders headers, 
            VisaQRPayConfirmRequest confirmRequest, 
            PaymentCacheData paymentData) throws TMBCommonException {

        if (paymentData.isRequireCommonAuthentication()) {
            CommonAuthenWithPayloadRequest commonAuthenWithPayloadRequest = createAuthRequestWithPayload(
                    confirmRequest, paymentData);
            oauthService.verifyCommonAuthenWithPayload(headers, commonAuthenWithPayloadRequest);
        }
    }

    /**
     * Creates authentication request with payload
     */
    private CommonAuthenWithPayloadRequest createAuthRequestWithPayload(
            VisaQRPayConfirmRequest confirmRequest, 
            PaymentCacheData paymentData) {

        CommonAuthenticationInformation commonAuthenticationInformation = paymentData.getCommonAuthenticationInformation();

        return CommonAuthenWithPayloadRequest.builder()
                .amount(paymentData.getAmount())
                .dailyAmount(String.valueOf(commonAuthenticationInformation.getTotalPaymentAccumulateUsage()))
                .billerCompCode(commonAuthenticationInformation.getBillerCompCode())
                .featureId(commonAuthenticationInformation.getFeatureId())
                .flowName(commonAuthenticationInformation.getFlowName())
                .refId(confirmRequest.getTransId())
                .build();
    }

    /**
     * Prepares Visa payment confirmation request
     */
    private VisaConfirmRequest prepareVisaConfirmRequest(PaymentCacheData paymentData) 
            throws TMBCommonException {
        try {
            VisaConfirmRequest confirmRequest = new VisaConfirmRequest();
            VisaConfirmRequest.CreditCard creditCard = new VisaConfirmRequest.CreditCard();
            confirmRequest.setCreditCard(creditCard);

            BigDecimal amount = new BigDecimal(paymentData.getAmount());
            BigDecimal fee = new BigDecimal(Optional.ofNullable(paymentData.getFeeBillpay())
                    .orElse("0.00"));
            BigDecimal totalAmount = amount.add(fee).setScale(2, RoundingMode.HALF_UP);

            PaymentCacheData.VisaData visaData = paymentData.getVisaData();

            List<String> tag62Fields = extractTag62Data(visaData);

            creditCard.setCardSpendToPay(new VisaConfirmRequest.CardSpendToPay());
            creditCard.setAccountId(visaData.getAccountId());

            VisaConfirmRequest.CardSpendToPay paymentDetails = creditCard.getCardSpendToPay();
            paymentDetails.setRecipientPrimaryAccountNumber(visaData.getMerchantId());
            paymentDetails.setMerchantCategoryCode(visaData.getBusinessCode());
            paymentDetails.setTransactionCurrencyCode(visaData.getCurrencyId());
            paymentDetails.setAmount(totalAmount.toString());
            paymentDetails.setTransactionFeeAmt(visaData.getFeeFlat());
            paymentDetails.setTransactionFeeAmt3(visaData.getFeePercentage());
            paymentDetails.setCardAcceptorCountry(visaData.getMerchantCountry());
            paymentDetails.setCardAcceptorName(visaData.getMerchantName());
            paymentDetails.setCardAcceptorAddressCity(visaData.getMerchantCity());

            paymentDetails.setPurchaseIdentifier(!tag62Fields.isEmpty() ? tag62Fields.get(0) : "");
            paymentDetails.setSecondaryId(tag62Fields.size() > 1 ? tag62Fields.get(1) : "");

            paymentDetails.setMobileNumber("");
            paymentDetails.setEmailId("");

            return confirmRequest;
        } catch (Exception ex) {
            logger.error("Failed to create Visa QR payment confirmation request: {}", ex.getMessage(), ex);
            throw PaymentServiceUtils.failException(ResponseCode.FAILED);
        }
    }

    /**
     * Extracts Tag62 data from Visa data
     */
    private List<String> extractTag62Data(PaymentCacheData.VisaData visaData) {
        List<String> tag62Fields = new ArrayList<>();

        addIfNotBlank(tag62Fields, visaData.getPurchaseIdentifier());
        addIfNotBlank(tag62Fields, visaData.getMobileNumber());
        addIfNotBlank(tag62Fields, visaData.getStoreId());
        addIfNotBlank(tag62Fields, visaData.getLoyaltyNumber());
        addIfNotBlank(tag62Fields, visaData.getMerchantRefId());
        addIfNotBlank(tag62Fields, visaData.getConsumerId());
        addIfNotBlank(tag62Fields, visaData.getTerminalId());
        addIfNotBlank(tag62Fields, visaData.getPurposeOfTransaction());
        
        return tag62Fields;
    }
    
    /**
     * Adds value to list if not blank
     */
    private void addIfNotBlank(List<String> list, String value) {
        if (StringUtils.isNotBlank(value)) {
            list.add(value);
        }
    }

    /**
     * Handles errors from Visa API
     */
    public static TMBCommonException handleVisaError(FeignException exception, VisaQRPayConfirmResponse response) {
        TmbOneServiceResponse<Object> errorResponse = processExceptionResponse(exception);
        Optional<TmbStatus> status = Optional.of(errorResponse)
                .map(TmbOneServiceResponse::getStatus);

        if (ResponseCode.QR_VISA_ERR_94101.getCode()
                .equals(status.map(TmbStatus::getCode).orElse(null))) {
            response.setStatus(FIN_SUBMITTED_STATUS);
            return PaymentServiceUtils.failException(ResponseCode.QR_VISA_ERR_94101);
        } else {
            if ("card-scan-to-pay-async".equals(status.map(TmbStatus::getService).orElse(null))) {
                response.setStatus(FIN_FAILED_STATUS);
                return PaymentServiceUtils.failException(ResponseCode.FAILED);
            }
        }

        response.setStatus(FIN_FAILED_STATUS);
        return PaymentServiceUtils.failException(ResponseCode.PAY_BILL_ERR_2300);
    }

    /**
     * Converts FeignException to TmbOneServiceResponse
     */
    public static TmbOneServiceResponse<Object> processExceptionResponse(FeignException exception) {
        TmbOneServiceResponse<Object> errorResponseObj = new TmbOneServiceResponse<>();
        Optional<ByteBuffer> responseBuffer = exception.responseBody();

        if (responseBuffer.isPresent()) {
            try {
                String responseStr = new String(responseBuffer.get().array(), UTF_8);
                errorResponseObj = cast(TMBUtils.convertStringToJavaObj(responseStr, TmbOneServiceResponse.class));
            } catch (JsonProcessingException e) {
                logger.warn("Unable to convert error data: {}", e.getMessage());
            }
        }

        return errorResponseObj;
    }

    /**
     * Processes actions after confirmation is complete
     * Records transaction logs and updates activity events
     *
     * @param correlationId  Correlation ID for tracking
     * @param customerCrmId  Customer's CRM ID
     * @param response       Confirmation result
     * @param headers        HTTP request headers
     */
    public void postConfirmProcess(String correlationId,
                                   String customerCrmId,
                                   VisaQRPayConfirmResponse response,
                                   HttpHeaders headers,
                                   VisaQRPayConfirmRequest request) {
        String referenceID = response.getReferenceID();
        String transactionDateTime = response.getTransactionDateTime();
        PaymentCacheData paymentData = response.getPaymentCacheData();
        String transactionStatus = response.getStatus();

        FinancialCreditCard financialRecord = createFinancialRecord(
                customerCrmId, correlationId, referenceID, paymentData, transactionDateTime);

        TransactionCreditCard transactionRecord = createTransactionCalendarRecord(
                customerCrmId, referenceID, paymentData, transactionDateTime);

        logger.info("[Visa Confirmation] Status: {}, Reference ID: {}", transactionStatus, referenceID);

        try {
            ActivityVisaQRPayVerifyEvent activityEvent = createActivityConfirmEvent(
                    headers, paymentData);
            activityEvent.setRefNo(response.getReferenceID());
            ActivityCustomSlipCompleteEvent customSlipCompleteEvent = new ActivityCustomSlipCompleteEvent(headers, request.getCustomSlip(), CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_VISA);

            if (!PaymentServiceConstant.SUCCESS.equalsIgnoreCase(transactionStatus)) {
                activityEvent.setActivityStatus(FAILURE);
                activityEvent.setFailReason("visa confirm service failure");
            }

            logService.saveLogActivityBillPayEvent(activityEvent, customSlipCompleteEvent);
        } catch (Exception ex) {
            logger.error("[Visa Confirmation] Failed to save activity log: {}", ex.getMessage(), ex);
        }

        if (StringUtils.isNotBlank(referenceID)) {
            logger.info("[Visa Confirmation] Saving financial and transaction records - Status: {}, Reference ID: {}",
                    transactionStatus, referenceID);

            financialRecord.setTxnStatus(StringUtils.defaultIfBlank(transactionStatus, FIN_FAILED_STATUS));
            transactionRecord.setTransactionStatus(transactionStatus);

            TtbUtils.logJsonNotThrow(logger, "Financial credit card log event: {},", financialRecord);
            TtbUtils.logJsonNotThrow(logger, "Transaction credit card log event: {},", transactionRecord);

            logService.saveLogFinancialAndTransactionEvent(
                    correlationId, financialRecord, transactionRecord);
        }
    }

    /**
     * Creates financial record for logging
     */
    private FinancialCreditCard createFinancialRecord(
            String customerCrmId, String correlationId, String referenceId,
            PaymentCacheData paymentData, String transactionDateTime) {
        
        PaymentCacheData.VisaData visaData = paymentData.getVisaData();

        CreditCardConfirmRequest confirmRequest = new CreditCardConfirmRequest();
        BillPaymentCreditCard billPaymentRequest = new BillPaymentCreditCard();
        billPaymentRequest.setPayerAccount(new PayerAccount());
        billPaymentRequest.setPayeeCard(new PayeeCard());
        billPaymentRequest.setFee(new CreditCardFee());

        BillerTopUpDetailResponse billerResponse = new BillerTopUpDetailResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setBillerGroupType(BILLER_GROUP_BILL_PAY);
        billerResponse.setBillerInfo(billerInfo);

        paymentData.setBillerResp(billerResponse);
        confirmRequest.setPaymentCacheData(paymentData);
        confirmRequest.setBillPayment(billPaymentRequest);

        FinancialCreditCard financialRecord = new FinancialCreditCard(
                customerCrmId, referenceId, confirmRequest, correlationId, transactionDateTime);

        financialRecord.setMemo(paymentData.getNote());
        financialRecord.setFromAccNickName(paymentData.getFromAccountNickname());
        financialRecord.setFromAccName(paymentData.getFromAccountName());
        financialRecord.setFromAccNo(paymentData.getCardNumber());
        financialRecord.setBillerRef1(StringUtils.defaultIfBlank(visaData.getMerchantRefId(), "-"));
        financialRecord.setBillerRef2(StringUtils.defaultIfBlank(visaData.getTerminalId(), "-"));
        financialRecord.setBillerCustomerName(visaData.getMerchantName());
        financialRecord.setTxnAmount(paymentData.getAmount());
        financialRecord.setTxnFee(paymentData.getFeeBillpay());
        financialRecord.setActivityTypeIdNew(ACTIVITY_LOG_CONFIRM_VISA_QR_PAY);
        financialRecord.setCreateDate(commonPaymentService.getCurrentDateTime());
        financialRecord.setChannelId("MB");
        financialRecord.setSmartFlag("");
        financialRecord.setClearingStatus(FIN_SUBMITTED_STATUS);
        financialRecord.setTxnStatus(FIN_SUBMITTED_STATUS);
        financialRecord.setCompCode(null);
        financialRecord.setFromAccType("");
        financialRecord.setToAccNo(visaData.getAccountId());
        financialRecord.setToAccName(StringUtils.defaultIfBlank(visaData.getMerchantName(), "-"));

        return financialRecord;
    }

    /**
     * Creates transaction calendar record for logging
     */
    private TransactionCreditCard createTransactionCalendarRecord(
            String customerCrmId, String referenceId,
            PaymentCacheData paymentData, String transactionDateTime) {
        
        PaymentCacheData.VisaData visaData = paymentData.getVisaData();

        CreditCardConfirmRequest creditCardConfirmRequest = new CreditCardConfirmRequest();
        BillPaymentCreditCard billPayment = new BillPaymentCreditCard();

        BillerTopUpDetailResponse billerResponse = new BillerTopUpDetailResponse();
        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();

        PayerAccount payerAccount = new PayerAccount();
        PayeeCard payeeCard = new PayeeCard();

        payeeCard.setAccountId(Optional.ofNullable(visaData.getAccountId()).orElse(""));
        payerAccount.setId(Optional.ofNullable(visaData.getAccountId()).orElse(""));

        billerInfoResponse.setBillerCompCode(Optional.ofNullable(visaData.getMerchantId()).orElse(""));
        billerInfoResponse.setNameEn(Optional.ofNullable(visaData.getMerchantName()).orElse(""));

        billerResponse.setRef1(new ReferenceTopUpResponse());
        billerResponse.setRef2(new ReferenceTopUpResponse());
        billerResponse.setBillerInfo(billerInfoResponse);

        paymentData.setBillerResp(billerResponse);

        billPayment.setPayerAccount(payerAccount);
        billPayment.setPayeeCard(payeeCard);
        billPayment.setAmount(paymentData.getAmount());
        billPayment.setRef1(StringUtils.defaultIfBlank(visaData.getMerchantRefId(), "-"));
        billPayment.setRef2(StringUtils.defaultIfBlank(visaData.getTerminalId(), "-"));

        creditCardConfirmRequest.setBillPayment(billPayment);
        creditCardConfirmRequest.setPaymentCacheData(paymentData);

        return new TransactionCreditCard(
                referenceId, customerCrmId, creditCardConfirmRequest, transactionDateTime);
    }

    /**
     * Creates activity event for Visa QR payment confirmation
     */
    private ActivityVisaQRPayVerifyEvent createActivityConfirmEvent(
            HttpHeaders headers, PaymentCacheData paymentData) {
        
        PaymentCacheData.VisaData visaData = paymentData.getVisaData();

        ActivityVisaQRPayVerifyEvent activityEvent = new ActivityVisaQRPayVerifyEvent(
                ACTIVITY_LOG_CONFIRM_VISA_QR_PAY, headers);

        String preLoginFlag = headers.getFirst(PRE_LOGIN);
        boolean isPreLogin = "true".equalsIgnoreCase(preLoginFlag);

        activityEvent.setFlow("Home-VisaQRScan-" + (isPreLogin ? "Pre" : "Post"));
        activityEvent.setStep("Confirm");
        activityEvent.setCardNumber(paymentData.getCardNumber());
        activityEvent.setBillerName(visaData.getMerchantName());
        activityEvent.setReference1(StringUtils.defaultIfBlank(visaData.getMerchantRefId(), "-"));
        activityEvent.setReference2(StringUtils.defaultIfBlank(visaData.getTerminalId(), "-"));
        activityEvent.setAmount(paymentData.getAmount());
        activityEvent.setFee(paymentData.getFeeBillpay());

        return activityEvent;
    }

    /**
     * Removes data from cache after processing is complete
     */
    private void removeFromCacheStorage(String correlationId, String cacheKey) {
        try {
            cacheService.delete(cacheKey);
        } catch (Exception ex) {
            logger.warn("Error occurred while removing data from primary cache");
        }

        try {
            paymentServiceFeignClient.deleteDraftDataFromSecondary(correlationId, cacheKey);
        } catch (Exception ex) {
            logger.warn("Error occurred while removing data from secondary storage");
        }
    }

    /**
     * Creates confirmation response
     *
     * @param response Visa QR payment confirmation result
     * @return TopUpConfirmResponse containing payment confirmation details
     */
    public TopUpConfirmResponse buildConfirmResponse(VisaQRPayConfirmResponse response) {
        TopUpConfirmResponse confirmResponse = new TopUpConfirmResponse();

        Optional.ofNullable(response.getTransactionDateTime())
                .ifPresent(txnDateTime -> {
                    try {
                        DateTimeFormatter dateFormatter = DateTimeFormatter
                                .ofPattern(PaymentServiceConstant.BANK_TMB_VALIDATE_DATEFORMAT);

                        LocalDateTime dateTime = Instant.ofEpochMilli(Long.parseLong(txnDateTime))
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime();

                        String formattedDateTime = dateTime.format(dateFormatter);
                        confirmResponse.setTopUpCreatedDatetime(formattedDateTime);
                    } catch (Exception ex) {
                        logger.error("Failed to convert transaction datetime: {}", ex.getMessage(), ex);
                    }
                });

        confirmResponse.setReference1(response.getReferenceID());
        confirmResponse.setRemainingBalance(null);
        confirmResponse.setQr(null);

        return confirmResponse;
    }

    /**
     * Pulls payment result with retry capability
     *
     * @param correlationId  Correlation ID for tracking
     * @param referenceCode  Reference code for the payment
     * @param retryCounter   Counter for retry attempts
     * @return Payment status from response
     * @throws TMBCommonException If polling fails
     */
    @Retryable(
            retryFor = TMBCommonException.class,
            recover = "handlePullingResultFailure",
            maxAttemptsExpression = "#{T(Integer).parseInt('${retry.visa.pay.qr.attempts:15}')}",
            backoff = @Backoff(
                    delayExpression = "#{T(Long).parseLong('${retry.visa.pay.qr.delay:2000}')}"
            )
    )
    public String pullingResult(@SuppressWarnings("unused")
                                    String correlationId,
                                String referenceCode,
                                AtomicInteger retryCounter)
            throws TMBCommonException {
        retryCounter.incrementAndGet();
        logger.info("Polling for Visa result (attempt {}): reference code {}", 
                retryCounter.get(), referenceCode);

        try {
            String resultCacheKey = BILL_PAYMENT_SCAN_QR_REF_PREFIX + referenceCode;
            String cachedResultData = cacheService.get(resultCacheKey);

            if (cachedResultData == null) {
                throw new TMBCommonException("Result data not found in cache");
            }

            ScsResponse scsResponse = cast(TMBUtils.convertStringToJavaObj(
                    cachedResultData, ScsResponse.class));

            return Optional.ofNullable(scsResponse.getStatus())
                    .map(ScsStatus::getStatusCode)
                    .orElse("");
        } catch (TMBCommonException exception) {
            logger.warn("Failed to poll Visa result (attempt {}): reference code {}, error: {}",
                    retryCounter.get(), referenceCode, exception.getMessage());
            throw exception;
        } catch (Exception exception) {
            logger.error("Failed to poll Visa result (attempt {}): reference code {}, error: {}",
                    retryCounter.get(), referenceCode, exception.getMessage(), exception);
            throw new TMBCommonException("Failed to poll Visa result", exception);
        }
    }

    @Recover
    public String handlePullingResultFailure(TMBCommonException e,
                                             String correlationId,
                                             String referenceCode,
                                             @SuppressWarnings("unused")
                                             AtomicInteger retryCount) {
        try {
            String cacheKey = BILL_PAYMENT_SCAN_QR_REF_PREFIX + referenceCode;
            ScsResponse scsResponse = retrieveScsResponseData(correlationId, cacheKey);
            if (scsResponse != null) {
                logger.info("Pulling Visa result from secondary - referenceCode: {}", referenceCode);
                return Optional.of(scsResponse)
                        .map(ScsResponse::getStatus)
                        .map(ScsStatus::getStatusCode)
                        .orElse("");
            }
        } catch (Exception ex) {
            logger.error("Unable to pull Visa result from secondary - referenceCode: {}, exception: {}",
                    referenceCode, e.getMessage(), e);
        }
        logger.info("Pulling Visa result - referenceCode: {}, TIMEOUT", referenceCode);
        return "TIMEOUT";
    }

    public ScsResponse retrieveScsResponseData(String correlationId, String cacheKey) {
        logger.info("Started retrieval ScsResponse, cache key: [{}]", cacheKey);
        try {
            String cacheDataString = cacheService.get(cacheKey);
            if (cacheDataString == null) {
                return fetchDataFromSecondaryStorage(correlationId, cacheKey);
            }
            return (ScsResponse) TMBUtils.convertStringToJavaObj(cacheDataString, ScsResponse.class);
        } catch (Exception ex) {
            logger.error("Failed to find cache - error: {}", ex.getMessage(), ex);
            return fetchDataFromSecondaryStorage(correlationId, cacheKey);
        }
    }

}
