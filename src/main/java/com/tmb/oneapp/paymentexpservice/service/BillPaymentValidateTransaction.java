package com.tmb.oneapp.paymentexpservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CommonData;
import com.tmb.common.util.VersionUtils;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.BillValidateTransaction;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.paymentexpservice.model.commonauth.CommonAuthForceFR;
import com.tmb.oneapp.paymentexpservice.model.commonauth.VerifyTransactionResult;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.APP_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DAILY_LIMIT_TYPE_BILL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.IS_REQUIRE_FR_KEY;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PAYMENT_ACCUMULATE_USAGE_AMT_KEY;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PIN_FREE_TYPE_BILL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PRE_LOGIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TRANSFER_COMMON_CONFIG_MODULE_TRANSFER;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;

@Service
@RequiredArgsConstructor
public class BillPaymentValidateTransaction {
    private static final TMBLogger<BillPaymentValidateTransaction> logger = new TMBLogger<>(BillPaymentValidateTransaction.class);
    private final TransactionLimitService transactionLimitService;
    private final ValidateDailyLimitPinFree validateDailyLimitPinFree;
    private final OauthService oauthService;
    private final CommonService commonService;
    private final ValidatorFaceRecognizeTransfer validatorFaceRecognizeTransfer;

    @Value("${common.authen.require.app.version:5.12.0}")
    private String commonAuthenRequireAppVersion;

    @Value("${validate.fr.financial.accu.amount:200000}")
    private BigDecimal totalPaymentAccumulateUsageLimit;

    public BillValidateTransaction validateBillConfirmTransaction(HttpHeaders headers, String amount, String transId, boolean isPreLogin, boolean isOwner) throws SQLException, TMBCommonException {
        Map<String, Object> validateConfirmTransactionResponse = this.validateConfirmTransaction(headers, Double.valueOf(amount), transId, isPreLogin, isOwner, DAILY_LIMIT_TYPE_BILL, PIN_FREE_TYPE_BILL);

        BillValidateTransaction response = new BillValidateTransaction();
        response.setCustomerCrmProfile((CustomerCrmProfile) validateConfirmTransactionResponse.get(PaymentServiceConstant.DAILY_LIMIT));
        response.setRequirePin((boolean) validateConfirmTransactionResponse.get(PaymentServiceConstant.IS_REQUIRE_PIN));

        return response;
    }

    @LogAround
    public Map<String, Object> validateConfirmTransaction(HttpHeaders headers,
                                                          Double amount,
                                                          String transId,
                                                          boolean isPreLogin,
                                                          boolean isOwner,
                                                          String dailyLimitType,
                                                          String pinFreeType) throws SQLException, TMBCommonException {
        final String crmId = getValueFromHeader(headers, PaymentServiceConstant.X_CRMID);
        final String correlationId = getValueFromHeader(headers, PaymentServiceConstant.HEADER_CORRELATION_ID);

        CustomerCrmProfile dailyLimit = fetchTransactionLimit(correlationId, crmId);
        BigDecimal remaining = getRemaining(dailyLimit, dailyLimitType);

        validateTransactionLimitExceeded(remaining.doubleValue(), amount, isOwner);

        boolean isRequirePin = isRequireConfirmPinAndDailyLimit(dailyLimit, amount, correlationId, isPreLogin, isOwner, pinFreeType);
        validateRequirePinAndNotHavePinInCache(isRequirePin, transId, correlationId);

        Map<String, Object> validateTransaction = new HashMap<>();
        validateTransaction.put(PaymentServiceConstant.IS_REQUIRE_PIN, isRequirePin);
        validateTransaction.put(PaymentServiceConstant.DAILY_LIMIT, dailyLimit);
        return validateTransaction;
    }

    private String getValueFromHeader(HttpHeaders headers, String keyValue) throws TMBCommonException {
        return Optional.ofNullable(headers.getFirst(keyValue))
                .orElseThrow(() -> {
                    logger.error("Mandatory field missing :: [keyValue : {}]", keyValue);
                    return PaymentServiceUtils.failException(ResponseCode.MANDATORY_FIELD);
                });
    }

    @LogAround
    public Map<String, Object> validateRequirePin(HttpHeaders headers,
                                                  Double amount,
                                                  String transId,
                                                  boolean isPreLogin,
                                                  boolean isOwner) throws SQLException, TMBCommonException {
        final String crmId = getValueFromHeader(headers, PaymentServiceConstant.X_CRMID);
        final String correlationId = getValueFromHeader(headers, PaymentServiceConstant.HEADER_CORRELATION_ID);

        CustomerCrmProfile dailyLimit = fetchTransactionLimit(correlationId, crmId);
        boolean isRequirePin = isRequireConfirmPinAndDailyLimit(dailyLimit, amount, correlationId, isPreLogin, isOwner, PIN_FREE_TYPE_BILL);
        validateRequirePinAndNotHavePinInCache(isRequirePin, transId, correlationId);

        Map<String, Object> validateTransaction = new HashMap<>();
        validateTransaction.put(PaymentServiceConstant.IS_REQUIRE_PIN, isRequirePin);
        return validateTransaction;
    }

    private BigDecimal getRemaining(CustomerCrmProfile dailyLimit, String dailyLimitType) {
        BigDecimal remaining;
        if (StringUtils.equals(DAILY_LIMIT_TYPE_BILL, dailyLimitType)) {
            remaining = BigDecimal.valueOf(dailyLimit.getBillpayMaxLimitAmt()).subtract(dailyLimit.getBillpayAccuUsgAmt());
        } else {
            remaining = BigDecimal.valueOf(dailyLimit.getEbMaxLimitAmtCurrent() - dailyLimit.getEbAccuUsgAmtDaily());
        }
        return remaining;
    }

    @LogAround
    public CustomerCrmProfile fetchTransactionLimit(String correlationId, String crmId) throws SQLException {
        return transactionLimitService.fetchTransactionLimit(correlationId, crmId);
    }

    @LogAround
    public void validateTransactionLimitExceeded(double remaining, Double amount, boolean isOwner) throws TMBCommonException {
        if (isTransactionLimitExceeded(remaining, amount) && !isOwner) {

            throw new TMBCommonException(
                    ResponseCode.DAILY_LIMIT.getCode(),
                    ResponseCode.DAILY_LIMIT.getMessage(),
                    ResponseCode.DAILY_LIMIT.getService(),
                    HttpStatus.OK,
                    null
            );
        }
    }

    @LogAround
    private boolean isTransactionLimitExceeded(Double remaining, Double amount) {
        return remaining < amount;
    }

    @LogAround
    public boolean validateIsRequireConfirmPinOfBillAndTopUP(String crmId, String correlationId, String amount, boolean isPreLogin, boolean isOwner) throws TMBCommonException, SQLException {
        CustomerCrmProfile dailyLimit = this.fetchTransactionLimit(correlationId, crmId);

        return this.isRequireConfirmPinAndDailyLimit(dailyLimit, Double.valueOf(amount), correlationId, isPreLogin, isOwner, PIN_FREE_TYPE_BILL);
    }

    @LogAround
    public boolean isRequireConfirmPinAndDailyLimit(CustomerCrmProfile dailyLimit, Double amount, String correlationId, boolean isPreLogin, boolean isOwner, String pinFreeType) throws TMBCommonException {
        if (isPreLogin) {
            return true;
        }
        if (isOwner) {
            return false;
        }

        Double pinFreeLimit = StringUtils.equals(PIN_FREE_TYPE_BILL, pinFreeType) ? dailyLimit.getPinFreeBpLimit() : dailyLimit.getPinFreeTrLimit();
        boolean isSettingCheckPinFreeTurnOn = !StringUtils.equals("Y", dailyLimit.getPinFreeSeetingFlag());
        if (isSettingCheckPinFreeTurnOn || amount > pinFreeLimit) {
            return true;
        }

        return validatePinFreeCount(dailyLimit, correlationId);
    }

    private boolean validatePinFreeCount(CustomerCrmProfile dailyLimit, String correlationId) throws TMBCommonException {
        int pinFreeMaxTrans = Integer.parseInt(validateDailyLimitPinFree.fetchPinFreeMaxCount(correlationId));
        return dailyLimit.getPinFreeTxnCount() >= pinFreeMaxTrans;
    }

    @LogAround
    public void validateRequirePinAndNotHavePinInCache(boolean isRequirePin, String transId, String correlationId) throws TMBCommonException {
        boolean isPinCache = isPinCache(transId, correlationId);

        if (isRequirePin && !isPinCache) {

            throw new TMBCommonException(
                    ResponseCode.PIN_REQUIRED.getCode(),
                    ResponseCode.PIN_REQUIRED.getMessage(),
                    ResponseCode.PIN_REQUIRED.getService(),
                    HttpStatus.OK,
                    null
            );
        }
    }

    public boolean isPinCache(String verifyPinRefId, String correlationId) throws TMBCommonException {
        String keyVerify = "VERIFY_PIN_REF_ID_" + verifyPinRefId;

        return oauthService.getCache(correlationId, keyVerify);
    }

    public void updateDailyUsageAndPinCount(String correlationId, String crmId, String amount, boolean isRequirePin) throws SQLException {
        CustomerCrmProfile customerCrmProfile = this.fetchTransactionLimit(correlationId, crmId);
        logger.info("After get transactionLimit, [dailyLimitResponse = {}]", customerCrmProfile.toString());

        this.updateBillDailyUsage(crmId, Double.valueOf(amount), customerCrmProfile, isRequirePin, correlationId);
    }

    public void updateBillDailyUsage(String crmId, Double amount, CustomerCrmProfile dailyLimit, boolean isRequirePin, String correlationId) {
        updatePinFreeCount(crmId, dailyLimit, isRequirePin, correlationId);

        try {
            transactionLimitService.updateBillPayAccumulateUsage(crmId, amount + dailyLimit.getBillpayAccuUsgAmt().doubleValue(), correlationId);
        } catch (Exception e) {
            logger.info("Update Bill daily limit error : {}", e);
        }
    }

    public void updateTransferDailyUsage(String crmId, Double amount, CustomerCrmProfile dailyLimit, boolean isRequirePin, String correlationId) {
        updatePinFreeCount(crmId, dailyLimit, isRequirePin, correlationId);

        try {
            transactionLimitService.updateEBAccountUsageAmountDaily(crmId, amount + dailyLimit.getEbAccuUsgAmtDaily(), correlationId);
        } catch (Exception e) {
            logger.info("Update daily limit error : {}", e);
        }
    }

    private void updatePinFreeCount(String crmId, CustomerCrmProfile dailyLimit, boolean isRequirePin, String correlationId) {
        try {
            if (!isRequirePin) {
                transactionLimitService.updatePinFreeCount(
                        crmId,
                        dailyLimit.getPinFreeTxnCount() + 1,
                        dailyLimit.getPinFreeSeetingFlag(),
                        false,
                        correlationId);
            }
        } catch (Exception e) {
            logger.info("Update pin free error : {}", e);
        }
    }

    public VerifyTransactionResult validateIsRequireVerifyTransactionForTopUp(HttpHeaders headers, BigDecimal amount, boolean isOwn, CustomerCrmProfile crmProfile) throws TMBCommonException {
        boolean isRequirePinResult = false;
        var faceRecognizeResponseResult = new FaceRecognizeResponse();
        var commonAuthenResult = new CommonAuthenResult();

        String appVersion = headers.getFirst(APP_VERSION);
        String crmId = headers.getFirst(X_CRMID);
        String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        boolean isPreLogin = Boolean.parseBoolean(headers.getFirst("pre-login"));
        if (isShouldValidateWithCommonAuthen(appVersion)) {
            commonAuthenResult = this.validateIsRequireCommonAuthen(headers, String.valueOf(amount), isOwn, crmProfile, crmProfile.getPinFreeBpLimit(), true);
        } else {
            isRequirePinResult = this.isRequireConfirmPinAndDailyLimit(crmProfile, amount.doubleValue(), correlationId, isPreLogin, isOwn, PIN_FREE_TYPE_BILL);

            Map<String, Object> resultValidateFR = validatorFaceRecognizeTransfer.validateFaceRecognize(correlationId, crmId, appVersion, amount);
            faceRecognizeResponseResult.setIsRequireFr((boolean) Objects.requireNonNullElse(resultValidateFR.get(IS_REQUIRE_FR_KEY), false));
            faceRecognizeResponseResult.setPaymentAccuUsgAmt((BigDecimal) resultValidateFR.get(PAYMENT_ACCUMULATE_USAGE_AMT_KEY));

        }
        return new VerifyTransactionResult(isRequirePinResult, faceRecognizeResponseResult, commonAuthenResult);
    }

    public CommonAuthenResult validateIsRequireCommonAuthen(
            HttpHeaders headers,
            String reqAmount,
            boolean isOwn,
            CustomerCrmProfile crmProfile
    ) throws TMBCommonException {
        return this.validateIsRequireCommonAuthen(headers, reqAmount, isOwn, crmProfile, crmProfile.getPinFreeBpLimit(), false);
    }

    private CommonAuthenResult validateIsRequireCommonAuthen(
            HttpHeaders headers,
            String reqAmount,
            boolean isOwn,
            CustomerCrmProfile crmProfile,
            double pinFreeTranLimit,
            boolean paymentAccumulateUsageLimitRule
    ) throws TMBCommonException {
        CommonAuthenResult response = new CommonAuthenResult();
        boolean isPreLogin = Boolean.parseBoolean(headers.getFirst(PRE_LOGIN));

        if (isPreLogin) {
            response.setRequireCommonAuthen(true);
            return response;
        }

        if (isOwn) {
            response.setRequireCommonAuthen(false);
            return response;
        }

        boolean isPinFree = false;
        Boolean isDDP = null;
        boolean requiresCommonAuthentication;
        String crmId = headers.getFirst(X_CRMID);
        String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        double amountDouble = Double.parseDouble(reqAmount);
        Integer pinFreeTranCount = crmProfile.getPinFreeTxnCount();
        boolean isPinFreeDisabled = ("N").equalsIgnoreCase(crmProfile.getPinFreeSeetingFlag());
        boolean amountMoreThanLimit = amountDouble > pinFreeTranLimit;

        if (isPinFreeDisabled || amountMoreThanLimit) {
            requiresCommonAuthentication = true;
        } else if (isNotPinFree(pinFreeTranCount, correlationId)) {
            requiresCommonAuthentication = true;
        } else if (validateCrmIDInDDP(crmId, correlationId)) {
            isPinFree = true;
            isDDP = true;
            requiresCommonAuthentication = true;
        } else {
            isPinFree = true;
            isDDP = false;
            if (paymentAccumulateUsageLimitRule) {
                BigDecimal totalUsage = crmProfile.getPaymentAccuUsgAmt().add(new BigDecimal(reqAmount));
                requiresCommonAuthentication = totalUsage.compareTo(totalPaymentAccumulateUsageLimit) >= 0;
            } else {
                requiresCommonAuthentication = false;
            }
        }

        response.setRequireCommonAuthen(requiresCommonAuthentication);
        response.setIsForceFR(isDDP);
        response.setPinFree(isPinFree);
        return response;
    }

    public VerifyTransactionResult validateIsRequireVerifyTransaction(HttpHeaders headers, BigDecimal amount, boolean isOwn, CustomerCrmProfile crmProfile) throws TMBCommonException {
        boolean isRequirePinResult = false;
        var commonAuthenResult = new CommonAuthenResult();
        String appVersion = headers.getFirst(APP_VERSION);
        String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        boolean isPreLogin = Boolean.parseBoolean(headers.getFirst("pre-login"));
        if (isShouldValidateWithCommonAuthen(appVersion)) {
            commonAuthenResult = this.validateIsRequireCommonAuthen(headers, String.valueOf(amount), isOwn, crmProfile);
        } else {
            isRequirePinResult = this.isRequireConfirmPinAndDailyLimit(crmProfile, amount.doubleValue(), correlationId, isPreLogin, isOwn, PIN_FREE_TYPE_BILL);
        }
        return new VerifyTransactionResult(isRequirePinResult, null, commonAuthenResult);
    }

    private boolean isShouldValidateWithCommonAuthen(String appVersion) {
        return appVersion != null && VersionUtils.compare(appVersion, commonAuthenRequireAppVersion) >= 0;
    }

    private boolean validateCrmIDInDDP(String crmId, String correlationId) {
        CommonAuthForceFR commonAuthForceFRResult = oauthService.getCommonAuthForceFR(correlationId, crmId);
        if (ObjectUtils.isNotEmpty(commonAuthForceFRResult)) {
            return commonAuthForceFRResult.getIsForce();
        }
        return false;
    }

    private boolean isNotPinFree(
            Integer pinFreeTxnCount,
            String correlationId
    ) throws TMBCommonException {
        CommonData commonConfig = commonService.getCommonConfig(correlationId, TRANSFER_COMMON_CONFIG_MODULE_TRANSFER);

        Integer pinFreeMaxTrans = Integer.parseInt(commonConfig.getPinFreeMaxTrans());
        return pinFreeTxnCount >= pinFreeMaxTrans;
    }

}
