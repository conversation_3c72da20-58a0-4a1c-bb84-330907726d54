package com.tmb.oneapp.paymentexpservice.model.commonauth;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Accessors(chain = true)
public class CommonAuthForceFR {
    private String id;
    private String crmId;
    private String requestId;
    private Boolean isForce;
    private Boolean isForceDipchip;
    private String ruleId;
    private String reason;
    private String livenessParameter;
    private String channel;
    private Date updateDate;
}
