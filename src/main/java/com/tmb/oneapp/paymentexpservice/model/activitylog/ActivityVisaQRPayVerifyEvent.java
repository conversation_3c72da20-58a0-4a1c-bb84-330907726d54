package com.tmb.oneapp.paymentexpservice.model.activitylog;

import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import org.springframework.http.HttpHeaders;

public class ActivityVisaQRPayVerifyEvent extends ActivityBillPayEvent {
    public ActivityVisaQRPayVerifyEvent(String activityTypeId, HttpHeaders headers) {
        super(activityTypeId, headers);
    }
    public void setDdpFlagAndPinFreeFlag(CommonAuthenResult commonAuthenResult) {
        if (commonAuthenResult == null) {
            return;
        }

        this.isForceFr = commonAuthenResult.getIsForceFR() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceFR());
        this.isForceDipchip = commonAuthenResult.getIsForceDipchip() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceDipchip());

        this.pinFreeFlag = String.valueOf(commonAuthenResult.isPinFree());
    }
}
