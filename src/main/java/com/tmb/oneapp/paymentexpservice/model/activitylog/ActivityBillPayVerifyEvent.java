package com.tmb.oneapp.paymentexpservice.model.activitylog;

import com.tmb.oneapp.paymentexpservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_COMP_CODE_MEA;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_COMP_CODE_MWA;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_PAYMENT_ACTIVITY_VERIFY_STEP;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TOP_UP_DEFAULT_FEE;


public class ActivityBillPayVerifyEvent extends ActivityBillPayEvent {

    public ActivityBillPayVerifyEvent(String correlationId, String activityTypeId, HttpHeaders headers, TopUpVerifyRequest topUpVerifyRequest, BillerTopUpDetailResponse billerTopUpDetailResponse) {
        super(correlationId, activityTypeId, headers);

        setUpFields(topUpVerifyRequest, billerTopUpDetailResponse);
    }

    private void setUpFields(TopUpVerifyRequest topUpVerifyRequest, BillerTopUpDetailResponse billerTopUpDetailResponse) {
        setStep(BILL_PAYMENT_ACTIVITY_VERIFY_STEP);
        setFee(TOP_UP_DEFAULT_FEE);
        if (Boolean.TRUE.equals(topUpVerifyRequest.getIsCreditCard())) {
            setCardNumber(topUpVerifyRequest.getCardNumber());
        } else {
            setFromAccount(topUpVerifyRequest.getAccountNumber());
        }
        setReference1(topUpVerifyRequest.getReference1(), billerTopUpDetailResponse.getBillerInfo().getBillerCategoryCode(), billerTopUpDetailResponse.getRef1().getIsMobile());

        boolean isNotMEAOrMWA = !StringUtils.equalsAny(topUpVerifyRequest.getBillerCompCode(), BILL_COMP_CODE_MEA, BILL_COMP_CODE_MWA);
        if (isNotMEAOrMWA) {
            setReference2(topUpVerifyRequest.getReference2());
        }
        setAmount(insertCommas(new BigDecimal(topUpVerifyRequest.getAmount())));
        setFlow(topUpVerifyRequest.getFlow());
        setBillerName(generateActivityBillerName(
                billerTopUpDetailResponse.getBillerInfo().getNameEn(),
                topUpVerifyRequest.getBillerCompCode()
        ));
    }


    public void setDdpFlagAndPinFreeFlag(CommonAuthenResult commonAuthenResult) {
        if (commonAuthenResult == null) {
            return;
        }
        
        this.isForceFr = commonAuthenResult.getIsForceFR() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceFR());
        this.isForceDipchip = commonAuthenResult.getIsForceDipchip() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceDipchip());
        this.pinFreeFlag = String.valueOf(commonAuthenResult.isPinFree());
    }
}
