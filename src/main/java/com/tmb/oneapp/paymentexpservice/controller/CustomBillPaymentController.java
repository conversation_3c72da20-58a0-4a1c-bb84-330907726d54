package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.MEABillPaymentDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.MEAValidationResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.MWABillPaymentDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.MWAValidationResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.PEABillPaymentDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.PEAValidationResponse;
import com.tmb.oneapp.paymentexpservice.service.BillPaymentValidationMEAService;
import com.tmb.oneapp.paymentexpservice.service.BillPaymentValidationMWAService;
import com.tmb.oneapp.paymentexpservice.service.BillPaymentValidationPEAService;
import com.tmb.oneapp.paymentexpservice.service.CustomBillPaymentConfirmService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@Tag(name = "API customize payment")
public class CustomBillPaymentController {
    private static final TMBLogger<BankInfoController> logger = new TMBLogger<>(BankInfoController.class);

    private final BillPaymentValidationPEAService billPaymentValidationPEAService;
    private final BillPaymentValidationMEAService billPaymentValidationMEAService;
    private final CustomBillPaymentConfirmService customBillPaymentConfirmService;
    private final BillPaymentValidationMWAService billPaymentValidationMWAService;

    public CustomBillPaymentController(BillPaymentValidationPEAService billPaymentValidationPEAService, BillPaymentValidationMEAService billPaymentValidationMEAService, CustomBillPaymentConfirmService customBillPaymentConfirmService, BillPaymentValidationMWAService billPaymentValidationMWAService) {
        this.billPaymentValidationPEAService = billPaymentValidationPEAService;
        this.billPaymentValidationMEAService = billPaymentValidationMEAService;
        this.customBillPaymentConfirmService = customBillPaymentConfirmService;
        this.billPaymentValidationMWAService = billPaymentValidationMWAService;
    }

    @LogAround
    @Operation(summary = "MEA Get payment Detail")
    @PostMapping(value = "/bill-payment/mea/payment-detail")
    public ResponseEntity<TmbOneServiceResponse<MEABillPaymentDetailResponse>> getDetailOfMEA(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Valid @RequestBody TopUpVerifyRequest topUpVerifyRequest
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<MEABillPaymentDetailResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            MEABillPaymentDetailResponse meaBillPaymentDetailResponse = (MEABillPaymentDetailResponse) billPaymentValidationMEAService.getPaymentDetail(correlationId, topUpVerifyRequest);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(meaBillPaymentDetailResponse);
        } catch (Exception e) {
            logger.error("Failed to process getDetailOfMEA error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "PEA Get payment Detail")
    @PostMapping(value = "/bill-payment/pea/payment-detail")
    public ResponseEntity<TmbOneServiceResponse<PEABillPaymentDetailResponse>> getDetailOfPEA(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Valid @RequestBody TopUpVerifyRequest topUpVerifyRequest
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<PEABillPaymentDetailResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            PEABillPaymentDetailResponse paymentDetailResponse = (PEABillPaymentDetailResponse) billPaymentValidationPEAService.getPaymentDetail(correlationId, topUpVerifyRequest);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(paymentDetailResponse);

        } catch (Exception e) {
            logger.error("Failed to process getDetailOfPEA error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "MWA Get payment Detail")
    @PostMapping(value = "/bill-payment/mwa/payment-detail")
    public ResponseEntity<TmbOneServiceResponse<MWABillPaymentDetailResponse>> getDetailOfMWA(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Valid @RequestBody TopUpVerifyRequest topUpVerifyRequest
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<MWABillPaymentDetailResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            MWABillPaymentDetailResponse paymentDetail = (MWABillPaymentDetailResponse) billPaymentValidationMWAService.getPaymentDetail(correlationId, topUpVerifyRequest);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(paymentDetail);
            return ResponseEntity.ok(tmbOneServiceResponse);
        } catch (Exception e) {
            logger.error("Failed to process getDetailOfMWA error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "MEA payment Validation")
    @Parameters({
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/bill-payment/mea/validation")
    public ResponseEntity<TmbOneServiceResponse<MEAValidationResponse>> validationOfMEA(
            @RequestBody @Valid TopUpVerifyRequest topUpVerifyRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<MEAValidationResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            MEAValidationResponse verifyResponse = (MEAValidationResponse) billPaymentValidationMEAService.validation(topUpVerifyRequest, headers);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(verifyResponse);
        } catch (Exception e) {
            logger.error("Failed to process validationOfMEA error : {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "PEA payment Validation")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
    })
    @PostMapping(value = "/bill-payment/pea/validation")
    public ResponseEntity<TmbOneServiceResponse<PEAValidationResponse>> validationOfPEA(
            @Valid @RequestBody TopUpVerifyRequest topUpVerifyRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<PEAValidationResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            PEAValidationResponse verifyResponse = (PEAValidationResponse) billPaymentValidationPEAService.validation(topUpVerifyRequest, headers);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(verifyResponse);
        } catch (Exception e) {
            logger.error("Failed to process validationOfPEA error : {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "MWA payment Validation")
    @Parameters({
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20"),
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
    })
    @PostMapping(value = "/bill-payment/mwa/validation")
    public ResponseEntity<TmbOneServiceResponse<MWAValidationResponse>> validationOfMWA(
            @Valid @RequestBody TopUpVerifyRequest topUpVerifyRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<MWAValidationResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            MWAValidationResponse verifyResponse = (MWAValidationResponse) billPaymentValidationMWAService.validation(topUpVerifyRequest, headers);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(verifyResponse);
        } catch (Exception e) {
            logger.error("Failed to process validationOfMWA error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Custom payment Confirm")
    @Parameters({
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "1111d3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "11.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "11.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S10")
    })
    @PostMapping(value = "/bill-payment/{module}/confirm")
    public ResponseEntity<TmbOneServiceResponse<TopUpConfirmResponse>> confirm(
            @PathVariable String module,
            @RequestBody TopUpConfirmRequest topUpConfirmRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            if (!module.equals("mea") && !module.equals("pea") && !module.equals("mwa")) {
                return ResponseEntity.badRequest().headers(headers).body(tmbOneServiceResponse);
            }
            TopUpConfirmResponse confirmResponse = customBillPaymentConfirmService.paymentConfirm(topUpConfirmRequest, headers, "CUSTOM_BILL_PAY");
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(confirmResponse);
        } catch (Exception e) {
            logger.error("Failed to process confirm module = ".concat(module).concat(", error : {}"), e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
