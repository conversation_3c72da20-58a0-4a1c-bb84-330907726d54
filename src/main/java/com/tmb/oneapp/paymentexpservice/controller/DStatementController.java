package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.DStatementConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.DStatementResponse;
import com.tmb.oneapp.paymentexpservice.service.DStatementConfirmService;
import feign.FeignException;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;

@RestController
@Tag(name="Controller for DStatement")
public class DStatementController {
	
	private static final TMBLogger<DStatementController> logger = new TMBLogger<>(DStatementController.class);
	private final DStatementConfirmService dStatementConfirmService;
	
	@Autowired
	public DStatementController(DStatementConfirmService dStatementConfirmService) {
		super();
		this.dStatementConfirmService = dStatementConfirmService;
	}

	@LogAround
	@PostMapping(value = "/direct-debit/confirm", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<TmbOneServiceResponse<DStatementResponse>> dStatementConfirm(
			@Parameter(description = "Correlation ID", example = "ae40d276-35f4-4512-b010-7bde6a32eb8d", required = true) @Valid @RequestHeader(PaymentServiceConstant.HEADER_CORRELATION_ID) String correlationId,
			@RequestBody DStatementConfirmRequest dStatementConfirmRequest) {
		logger.info("dStatementConfirm method start time {}", System.currentTimeMillis());
		TmbOneServiceResponse<DStatementResponse> serviceResponse = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_CORRELATION_ID, correlationId);
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			long startaccTimeMob = System.nanoTime();
			//need to implement validate
			ResponseCode errorCode = validateFieldDStatement(dStatementConfirmRequest);
			if (null != errorCode) {
				serviceResponse.setStatus(new TmbStatus(errorCode.getCode(), errorCode.getMessage(),
						errorCode.getService(), errorCode.getDescription()));
				return ResponseEntity.badRequest().headers(responseHeaders).body(serviceResponse);
			}
			DStatementResponse data = dStatementConfirmService.eteCallDirectCredit(dStatementConfirmRequest);

			if (data == null) {
				serviceResponse.setStatus(new TmbStatus(ResponseCode.INVALID_REQUEST.getCode(), ResponseCode.INVALID_REQUEST.getMessage(),
						ResponseCode.INVALID_REQUEST.getService(), ResponseCode.INVALID_REQUEST.getDescription()));
				return ResponseEntity.badRequest().headers(responseHeaders).body(serviceResponse);
			}
			serviceResponse.setData(data);
			long endaccTimeMob = System.nanoTime();
			long timeaccapsedMoendb = endaccTimeMob - startaccTimeMob;
			logger.info("DStatement ===> direct-credit/confirm end ---> Time elapsed  : {}", timeaccapsedMoendb);
			serviceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));

			return ResponseEntity.ok().headers(responseHeaders).body(serviceResponse);
		} catch (Exception e) {
			String errorBody = null;

			if (e instanceof FeignException feignException) {
				errorBody = feignException.contentUTF8();
				logger.error("dStatementConfirm Error - status={}, body={}", feignException.status(), errorBody, feignException);
			} else {
				logger.error("dStatementConfirm Error {}", e.getMessage(), e);
			}

			if (errorBody != null && errorBody.contains(PaymentServiceConstant.DIRECR_DEBIT_FEE_INSUFFICIENT_FUNDS)) {
				serviceResponse.setStatus(new TmbStatus(
						ResponseCode.INSUFFICIENT_FUNDS.getCode(),
						ResponseCode.INSUFFICIENT_FUNDS.getMessage(),
						ResponseCode.INSUFFICIENT_FUNDS.getService(),
						ResponseCode.INSUFFICIENT_FUNDS.getDescription()
				));
			} else {
				serviceResponse.setStatus(new TmbStatus(
						ResponseCode.FAILED.getCode(),
						ResponseCode.FAILED.getMessage(),
						ResponseCode.FAILED.getService(),
						ResponseCode.FAILED.getDescription()
				));
			}

			return ResponseEntity.badRequest().headers(responseHeaders).body(serviceResponse);
		}
	}

	private ResponseCode validateFieldDStatement(DStatementConfirmRequest dStatementConfirmRequest) {
		
		if(dStatementConfirmRequest.getAccountNo() == null
				|| dStatementConfirmRequest.getAccountType() == null
				|| dStatementConfirmRequest.getAmount() == null) {
			return ResponseCode.MISSING_REQUIRED_FIELD;
		}
		return null;
	}
}
