package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.foreignexchange.FXConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.foreignexchange.FXConfirmationResponse;
import com.tmb.oneapp.paymentexpservice.model.foreignexchange.FXExchangeRateResponse;
import com.tmb.oneapp.paymentexpservice.model.foreignexchange.FXValidationRequest;
import com.tmb.oneapp.paymentexpservice.model.foreignexchange.FXValidationResponse;
import com.tmb.oneapp.paymentexpservice.service.foreignexchange.FXConfirmationService;
import com.tmb.oneapp.paymentexpservice.service.foreignexchange.FXExchangeRateService;
import com.tmb.oneapp.paymentexpservice.service.foreignexchange.FXValidationService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import static com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils.getCustomMessage;

@RestController
@Tag(name = "API Foreign exchange")
public class ForeignExchangeController {
    private static final TMBLogger<InternationalTransferController> logger = new TMBLogger<>(InternationalTransferController.class);
    private final FXExchangeRateService fxExchangeRateService;
    private final FXValidationService fxValidationService;
    private final FXConfirmationService fxConfirmationService;

    public ForeignExchangeController(FXExchangeRateService fxExchangeRateService, FXValidationService fxValidationService, FXConfirmationService fxConfirmationService) {
        this.fxExchangeRateService = fxExchangeRateService;
        this.fxValidationService = fxValidationService;
        this.fxConfirmationService = fxConfirmationService;
    }

    @LogAround
    @Operation(summary = "Get Exchange rate list of FX")
    @GetMapping(value = "/fx/exchange-rate")
    public ResponseEntity<TmbOneServiceResponse<FXExchangeRateResponse>> getFxExchangeRate(
            @Parameter(description = "CRM ID", example = "001100000000000000000000031310", required = true) @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId
    ) throws TMBCommonException {
        try {
            TmbOneServiceResponse<FXExchangeRateResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
            FXExchangeRateResponse data = fxExchangeRateService.getFXExchangeRate(crmId, correlationId);

            tmbOneServiceResponse.setData(data);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            return ResponseEntity.ok(tmbOneServiceResponse);
        } catch (Exception e) {
            logger.error("Error : getTransferHistoryFinancialActivityLog {}", e);
            throw PaymentServiceUtils.failException(ResponseCode.FX_RATE_ERROR, HttpStatus.BAD_REQUEST);
        }
    }

    @LogAround
    @Operation(summary = "Validate Foreign Exchange")
    @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da")
    @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000000031310")
    @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER)
    @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0")
    @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb")
    @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0")
    @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a")
    @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    @Parameter(name = "Flow", description = "Flow", schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "openAccountFCD")
    @PostMapping(value = "/fx/validation")
    public ResponseEntity<TmbOneServiceResponse<FXValidationResponse>> validateFX(
            @RequestBody @Valid FXValidationRequest request,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        try {
            TmbOneServiceResponse<FXValidationResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

            FXValidationResponse data = fxValidationService.validate(request, headers);

            tmbOneServiceResponse.setData(data);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            return ResponseEntity.ok(tmbOneServiceResponse);
        } catch (Exception e) {
            logger.error("Error : Validate Foreign Exchange {}", e.getMessage(), e);
            throw PaymentServiceUtils.handleExceptions(e);
        }
    }

    @LogAround
    @Operation(summary = "Confirm Foreign Exchange")
    @PostMapping(value = "/fx/confirm")
    @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da")
    @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000000031310")
    @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER)
    @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0")
    @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb")
    @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0")
    @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a")
    @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    @Parameter(name = "Flow", description = "Flow", schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "openAccountFCD")
    public ResponseEntity<TmbOneServiceResponse<FXConfirmationResponse>> confirmFX(
            @RequestBody @Valid FXConfirmRequest request,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        try {
            TmbOneServiceResponse<FXConfirmationResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
            FXConfirmationResponse data = fxConfirmationService.confirm(request, headers);
            tmbOneServiceResponse.setData(data);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            return ResponseEntity.ok(tmbOneServiceResponse);
        } catch (Exception e) {
            logger.error("Error : confirmFX {}", getCustomMessage(e), e);
            throw PaymentServiceUtils.handleExceptions(e);
        }
    }
}
