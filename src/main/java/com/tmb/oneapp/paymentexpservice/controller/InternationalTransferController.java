package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.AutoSlipResponse;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.InfoFormSubmitRequest;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.InternationalExchangeResponse;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.ReviewingTransferRequest;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.SubmitTransactionResponse;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.SubmitTransferRequest;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.TransferFeeResponse;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.TransferReviewResponse;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.TransferValidationRequest;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.TransferValidationResponse;
import com.tmb.oneapp.paymentexpservice.service.InternationalTransferService;
import com.tmb.oneapp.paymentexpservice.service.SFTPService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;
import java.util.UUID;

/**
 * InternationalTransferController request mapping will handle apis call
 * respective method to get data for international transfer screen
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "API International Transfer Screen")
public class InternationalTransferController {
    private static final TMBLogger<InternationalTransferController> logger = new TMBLogger<>(InternationalTransferController.class);
    private final InternationalTransferService transferService;
    private final SFTPService sftpService;

    @Value("${ott.cross-border-api.active-status:false}")
    private boolean isCrossBorderApiActiveStatus;

    /**
     * Method responsible for controlling API for get Exchange rate currency
     */
    @Operation(summary = "Get Exchange rate list")
    @LogAround
    @GetMapping(value = "/ott/exchange-rate")
    public ResponseEntity<TmbOneServiceResponse<InternationalExchangeResponse>> getExchangeRate(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @Valid @RequestHeaderNonNull String correlationId,
            @Parameter(description = PaymentServiceConstant.X_CRMID, example = "001100000000000000000018593707", required = true)
            @RequestHeader(PaymentServiceConstant.X_CRMID) @Valid @RequestHeaderNonNull String crmId) {

        logger.info("InternationalTransfer Controller  getExchangeRate method start Time : {} ", System.currentTimeMillis());
        TmbOneServiceResponse<InternationalExchangeResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {

            final InternationalExchangeResponse output = transferService.getCurrencyList(correlationId, crmId);
            response.setData(output);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(responseHeaders).body(response);

        } catch (Exception e) {
            logger.error("Unable to fetch Currency : {} ", e.getMessage(), e);
            return callResponse(response, responseHeaders, ResponseCode.FAILED, ResponseEntity.badRequest());

        }
    }


    /**
     * Method responsible for controlling API for submit Info form and get Transfer fee
     */
    @Operation(summary = "Submit info form and get Transfer fee list")
    @LogAround
    @PostMapping(value = "ott/transfer_fee_info")
    public ResponseEntity<TmbOneServiceResponse<TransferFeeResponse>> submitInputFormAndGetTransferFee(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true)
            @RequestHeader(name = "X-CRMID") String crmId,
            @RequestBody InfoFormSubmitRequest infoFormSubmitRequest

    ) {

        logger.info("Submit input form and get Transfer fee on Transfer_fee_info api : {} ", System.currentTimeMillis());
        TmbOneServiceResponse<TransferFeeResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {

            final TransferFeeResponse output = transferService.submitInfoFormAndGetTransferFee(correlationId, crmId, infoFormSubmitRequest);
            response.setData(output);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(responseHeaders).body(response);

        } catch (Exception e) {
            logger.error("Unable to submit and get Transfer fee: {} ", e.getMessage(), e);
            return callResponse(response, responseHeaders, ResponseCode.FAILED, ResponseEntity.badRequest());

        }
    }

    /**
     * Method responsible for controlling API for review transfer fee
     */
    @Operation(summary = "Review transfer fee")
    @LogAround
    @PostMapping(value = "ott/reviewing_submission")
    public ResponseEntity<TmbOneServiceResponse<TransferReviewResponse>> reviewTransferFee(
            @Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true)
            @RequestHeader(name = "X-CRMID") String crmId,
            @RequestHeader HttpHeaders headers,
            @RequestBody ReviewingTransferRequest reviewRequest
    ) throws TMBCommonException {

        logger.info("Review transfer fee : {} ", System.currentTimeMillis());
        TmbOneServiceResponse<TransferReviewResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {

            final TransferReviewResponse output = transferService.reviewTransferFee(headers, crmId, reviewRequest);
            response.setData(output);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(responseHeaders).body(response);

        } catch (TMBCommonException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Unable to review transfer fee api : {} ", e);
            return callResponse(response, responseHeaders, ResponseCode.FAILED, ResponseEntity.badRequest());

        }
    }

    /**
     * Method responsible for controlling API for get transfer (fin)
     */
    @Operation(summary = "GET Transaction from ott_fin_transaction for transfer successful screen")
    @LogAround
    @GetMapping(value = "ott/transaction_fin")
    public ResponseEntity<TmbOneServiceResponse<TransferReviewResponse>> getTransaction(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @Valid String correlationId,
            @Parameter(description = "Reference ID", example = "NT2096710000000700", required = true)
            @RequestHeader("finTxnRefId") @Valid String finTxnRefId,
            @Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true)
            @RequestHeader(name = "X-CRMID") String crmId) {

        logger.info("[IN] GET Transaction from ott_fin_transaction: {} ", System.currentTimeMillis());
        TmbOneServiceResponse<TransferReviewResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {

            final TransferReviewResponse output = transferService.
                    getTransactionFinData(correlationId, finTxnRefId, crmId);
            response.setData(output);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(responseHeaders).body(response);

        } catch (Exception e) {
            logger.error("Unable to get information : {} ", e);
            return callResponse(response, responseHeaders, ResponseCode.FAILED, ResponseEntity.badRequest());

        }
    }

    /**
     * Method responsible for controlling API for validate transfer
     */
    @Operation(summary = "Validate transfer")
    @LogAround
    @PostMapping(value = "ott/transaction_validation")
    public ResponseEntity<TmbOneServiceResponse<TransferValidationResponse>> validateTransfer(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true)
            @RequestHeader(name = "X-CRMID") String crmId,
            @RequestBody TransferValidationRequest request,
            @RequestHeader HttpHeaders header
    ) {

        logger.info("Validate transfer : {} ", System.currentTimeMillis());
        TmbOneServiceResponse<TransferValidationResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {

            final TransferValidationResponse output = transferService.validationTransaction
                    (header, request.getFlowName(), correlationId, crmId, request.getTotalAmount());
            response.setData(output);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (InterruptedException e){
            logger.error("Unable to validate transfer : {} ", e);
            Thread.currentThread().interrupt();
            return callResponse(response, responseHeaders, ResponseCode.FAILED, ResponseEntity.badRequest());
        } catch (Exception e) {
            logger.error("Unable to validate transfer : {} ", e);
            return callResponse(response, responseHeaders, ResponseCode.FAILED, ResponseEntity.badRequest());
        }
    }

    /**
     * Method responsible for controlling API for submit transfer api
     */
    @Operation(summary = "Submit transfer")
    @LogAround
    @PostMapping(value = "ott/submit_transaction")
    public ResponseEntity<TmbOneServiceResponse<SubmitTransactionResponse>> submitTransfer(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull @SuppressWarnings("unused") String correlationId,
            @Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true)
            @RequestHeader(name = "X-CRMID") String crmId,
            @RequestBody SubmitTransferRequest request,
            @RequestHeader HttpHeaders header) {

        logger.info("Submit transfer  : {} ", System.currentTimeMillis());
        TmbOneServiceResponse<SubmitTransactionResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {
            logger.info("Submit transfer FrUuid : {} ", request.getFrUuid());
            SubmitTransactionResponse result = transferService.submitTransaction(header, crmId, request);
            response.setData(result);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(responseHeaders).body(response);

        } catch (Exception e) {
            logger.error("Unable to submit transfer: {} ", e.getMessage(), e);
            return callResponse(response, responseHeaders, ResponseCode.FAILED, ResponseEntity.badRequest());
        }
    }

    /**
     * Method responsible for controlling API for e-spit transfer
     */
    @Operation(summary = "E-slip Notification")
    @LogAround
    @GetMapping(value = "ott/auto-save-slip")
    public ResponseEntity<TmbOneServiceResponse<AutoSlipResponse>> generateSlip(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true)
            @RequestHeader(name = "X-CRMID") String crmId
    ) {

        logger.info("generate slip : {} ", System.currentTimeMillis());
        TmbOneServiceResponse<AutoSlipResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {

            final AutoSlipResponse output = transferService.getAutoSaveSlip(correlationId, crmId);
            response.setData(output);
            return callResponse(response, responseHeaders, ResponseCode.SUCCESS, ResponseEntity.ok());

        } catch (Exception e) {
            logger.error("Unable to get auto save slip transfer : {} ", e);
            response.setData(new AutoSlipResponse("N", "N"));
            return callResponse(response, responseHeaders, ResponseCode.FAILED, ResponseEntity.badRequest());
        }
    }

    /**
     * Method responsible for update result batch
     */
    @LogAround
    @Operation(summary = "Update batch result")
    @PostMapping(value = "/internal/ott/update-result-transaction")
    public ResponseEntity<TmbOneServiceResponse<Object>> updateResult() {
        String correlationId = UUID.randomUUID().toString();
        if(this.isCrossBorderApiActiveStatus){
            return this.checkStatusCrossBorder(correlationId);
        } else {
            return this.updateResultBatch(correlationId);
        }
    }

    @LogAround
    public ResponseEntity<TmbOneServiceResponse<Object>> updateResultBatch(String correlationId) {
        logger.info("update result ott_transaction from batch method start Time : {} ", System.currentTimeMillis());
        HttpHeaders httpHeaders = new HttpHeaders();
        TmbOneServiceResponse<Object> response = new TmbOneServiceResponse<>();
        try {
            logger.info("Generate correlationId from uuid {}", correlationId);
            sftpService.downloadAndUpdateResult(correlationId);

            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));

            return ResponseEntity.ok().headers(httpHeaders).body(response);

        } catch (Exception e) {
            logger.error("Unable to update result transaction  : {} ", e);
            return callResponse(response, httpHeaders, ResponseCode.FAILED, ResponseEntity.badRequest());
        }
    }

    /**
     * Method responsible for update result API
     */

    @LogAround
    public ResponseEntity<TmbOneServiceResponse<Object>> checkStatusCrossBorder(String correlationId) {
        logger.info("[checkStatusCrossBorder] start Time : {} ", System.currentTimeMillis());
        HttpHeaders httpHeaders = new HttpHeaders();
        TmbOneServiceResponse<Object> response = new TmbOneServiceResponse<>();
        try {
            logger.info("[checkStatusCrossBorder] correlationId {}", correlationId);
            transferService.executeCheckStatusCrossBorder();

            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));

            return ResponseEntity.ok().headers(httpHeaders).body(response);

        } catch (Exception e) {
            logger.error("Unable to check & update result transaction  : {} ", e);
            return callResponse(response, httpHeaders, ResponseCode.FAILED, ResponseEntity.badRequest());
        }
    }

    /**
     * Method responsible for update result batch
     */
    @Operation(summary = "To move old transaction files")
    @LogAround
    @PostMapping(value = "/internal/ott/move-files")
    public ResponseEntity<TmbOneServiceResponse<Object>> moveFile() {
        HttpHeaders httpHeaders = new HttpHeaders();
        TmbOneServiceResponse<Object> response = new TmbOneServiceResponse<>();
        try {
            sftpService.ottMoveFile();
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));

            return ResponseEntity.ok().headers(httpHeaders).body(response);

        } catch (Exception e) {
            logger.error("Unable to move files : {} ", e);
            return callResponse(response, httpHeaders, ResponseCode.FAILED, ResponseEntity.badRequest());
        }
    }

    private <T> ResponseEntity<TmbOneServiceResponse<T>> callResponse(TmbOneServiceResponse<T> response,
                                                                      HttpHeaders responseHeaders,
                                                                      ResponseCode failed,
                                                                      ResponseEntity.BodyBuilder bodyBuilder) {

        response.setStatus(new TmbStatus(failed.getCode(), failed.getMessage(),
                failed.getService(), failed.getDescription()));

        return bodyBuilder.headers(responseHeaders).body(response);
    }

    /**
     * Method responsible for controlling API for submit transfer api
     */
    @Operation(summary = "Upload document")
    @LogAround
    @PostMapping(value = "ott/submit_document/{refId}",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<SubmitTransactionResponse>> submitDocument(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull @SuppressWarnings("unused") String correlationId,
            @Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true)
            @RequestHeader(name = "X-CRMID") String crmId,
            @RequestHeader HttpHeaders headers,
            @RequestParam("file") MultipartFile file,
            @PathVariable("refId") String refId) throws TMBCommonException {
        TmbOneServiceResponse<SubmitTransactionResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {
            logger.info("Submit document - crmId: {}, refId: {}, file.isEmpty: {}",
                    crmId, refId, Objects.isNull(file));
            Objects.requireNonNull(file);
            transferService.submitDocument(headers, correlationId, crmId, file, refId);
            response.setData(null);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            logger.info("Submit document - crmId: {}, refId: {} End..", crmId, refId);
            return ResponseEntity.ok().headers(responseHeaders).body(response);

        } catch (TMBCommonException e) {
            logger.error("Failed to submit document: {} ", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            logger.error("Unable to submit document: {} ", e.getMessage(), e);
            return callResponse(response, responseHeaders, ResponseCode.FAILED, ResponseEntity.badRequest());
        }
    }
}
