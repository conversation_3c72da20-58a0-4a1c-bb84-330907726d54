package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.model.BillValidateTransaction;
import com.tmb.oneapp.paymentexpservice.model.ValidateIsRequireConfirmPinResponse;
import com.tmb.oneapp.paymentexpservice.service.BillPaymentValidateTransaction;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("validations")
@Tag(name = "Validations of BillPay and Top-Up")
public class BillPayValidateTransactionController {
    @Autowired
    BillPaymentValidateTransaction billPaymentValidateTransaction;

    @Operation(summary = "Validate transaction is require pin or not")
    @PostMapping(value = "/bill/is-require-pin")
    public ResponseEntity<TmbOneServiceResponse<ValidateIsRequireConfirmPinResponse>> validateIsRequireConfirmPin(
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true) @Valid @RequestHeader(name = "X-CRMID") @RequestHeaderNonNull String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "Amount", example = "10.00", required = true) @RequestParam("amount") String amount,
            @Parameter(description = "Is pre login") @RequestParam("is_pre_login") boolean isPreLogin,
            @Parameter(description = "Is owner") @RequestParam("is_owner") boolean isOwner
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        TmbOneServiceResponse<ValidateIsRequireConfirmPinResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {

            ValidateIsRequireConfirmPinResponse response = new ValidateIsRequireConfirmPinResponse();
            response.setRequireConfirmPin(billPaymentValidateTransaction.validateIsRequireConfirmPinOfBillAndTopUP(crmId, correlationId, amount, isPreLogin, isOwner));

            tmbOneServiceResponse.setData(response);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @Operation(summary = "Validation exceed Daily-limit and is require Pin or not")
    @PostMapping(value = "/bill/daily-limit-and-is-require-pin")
    public ResponseEntity<TmbOneServiceResponse<BillValidateTransaction>> validateDailyLimitAndIsRequireConfirmPin(
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true) @Valid @RequestHeader(name = "X-CRMID") @RequestHeaderNonNull String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "Amount", example = "10.00", required = true) @RequestParam("amount") String amount,
            @Parameter(description = "Trans ID", example = "EPAYMENT_REF_fe1f15cc-6db8-4e5e-a18e-6ec1eec7f891", required = true) @RequestParam("trans_id") String transId,
            @Parameter(description = "Is pre login") @RequestParam("is_pre_login") boolean isPreLogin,
            @Parameter(description = "Is owner") @RequestParam("is_owner") boolean isOwner
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<BillValidateTransaction> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add(PaymentServiceConstant.X_CRMID, crmId);
            headers.add(PaymentServiceConstant.HEADER_CORRELATION_ID, correlationId);
            BillValidateTransaction response = billPaymentValidateTransaction.validateBillConfirmTransaction(headers, amount, transId, isPreLogin, isOwner);

            tmbOneServiceResponse.setData(response);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @Operation(summary = "Update Daily usage and Pin free count")
    @PostMapping(value = "/bill/update-daily-usage-and-pin-count")
    public ResponseEntity<TmbOneServiceResponse<String>> updateDailyUsageAndPinCount(
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true) @Valid @RequestHeader(name = "X-CRMID") @RequestHeaderNonNull String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "Amount", example = "10.00", required = true) @RequestParam("amount") String amount,
            @Parameter(description = "Is require pin") @RequestParam("is_require_pin") boolean isRequirePin
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        TmbOneServiceResponse<String> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            billPaymentValidateTransaction.updateDailyUsageAndPinCount(correlationId, crmId, amount, isRequirePin);

            tmbOneServiceResponse.setData("Update daily-limit and pin count success");
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
