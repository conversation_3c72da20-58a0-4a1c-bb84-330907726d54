package com.tmb.oneapp.paymentexpservice.controller.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayVerifyResponse;
import com.tmb.oneapp.paymentexpservice.service.CustomerDeviceStatusService;
import com.tmb.oneapp.paymentexpservice.service.v2.payment.V2VisaPayService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import static com.tmb.oneapp.paymentexpservice.constant.InternationalTransferConstant.FIN_FAILED_STATUS;
import static com.tmb.oneapp.paymentexpservice.constant.InternationalTransferConstant.FIN_SUBMITTED_STATUS;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.FINANCIAL_TRANSACTION_STATUS;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PRE_LOGIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;

/**
 * Controller responsible for handling visa pay related endpoints for v2
 */
@RestController
@RequestMapping("/visa-pay")
@Tag(name = "VisaPay V2 Controller")
@RequiredArgsConstructor
public class V2VisaPayController {
    private static final TMBLogger<V2VisaPayController> logger = new TMBLogger<>(V2VisaPayController.class);

    private final CustomerDeviceStatusService customerDeviceStatusService;
    private final V2VisaPayService visaPayService;

    /**
     * Method responsible for controlling API for visa-pay validation
     */
    @Operation(summary = "Visa-Pay validation")
    @LogAround
    @PostMapping(value = "/validation")
    public ResponseEntity<TmbOneServiceResponse<VisaQRPayVerifyResponse>> validate(
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383")
            @Valid @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76")
            @Valid @RequestHeader(name = DEVICE_ID, required = false) String deviceId,
            @Valid @RequestBody VisaQRPayVerifyRequest visaQRPayVerifyRequest,
            @RequestHeader HttpHeaders headers)
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<VisaQRPayVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            if (StringUtils.isBlank(crmId) && StringUtils.isNotBlank(deviceId)) {
                crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
                headers.set(PRE_LOGIN, "true");
                headers.set(X_CRMID, crmId);
            }
            VisaQRPayVerifyResponse response = visaPayService.validate(correlationId, crmId, headers, visaQRPayVerifyRequest);
            tmbOneServiceResponse.setData(response);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
        } catch (Exception e) {
            logger.error("validate : Error in V2VisaPayController : {} ", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    /**
     * Method responsible for controlling API for visa-pay confirmation
     */
    @LogAround
    @Operation(summary = "Visa-Pay confirmation")
    @PostMapping(value = "/confirm")
    public ResponseEntity<TmbOneServiceResponse<TopUpConfirmResponse>> confirm(
            @Parameter(description = "CRM ID", example = "001100000000000000000025536284")
            @Valid @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76")
            @Valid @RequestHeader(name = DEVICE_ID, required = false) String deviceId,
            @Valid @RequestBody VisaQRPayConfirmRequest visaQRPayConfirmRequest,
            @RequestHeader HttpHeaders headers)
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        VisaQRPayConfirmResponse visaQRPayConfirmResponse = new VisaQRPayConfirmResponse();
        try {
            logger.info("visa qr confirm start");

            if (StringUtils.isBlank(crmId) && StringUtils.isNotBlank(deviceId)) {
                crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
                headers.set(PRE_LOGIN, "true");
                headers.set(X_CRMID, crmId);
            }

            visaQRPayConfirmResponse = visaPayService.confirm(crmId, correlationId, headers, visaQRPayConfirmResponse, visaQRPayConfirmRequest);
            AtomicInteger retryCount = new AtomicInteger(0);

            String status = visaPayService.pullingResult(correlationId, visaQRPayConfirmResponse.getReferenceID(), retryCount);
            logger.info("Pull visa result status: {}, referenceId: {}", status, visaQRPayConfirmResponse.getReferenceID());
            
            if ("0".equals(Objects.requireNonNull(status))) {
                visaQRPayConfirmResponse.setStatus(FINANCIAL_TRANSACTION_STATUS);
                TopUpConfirmResponse result = visaPayService.buildConfirmResponse(visaQRPayConfirmResponse);
                tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
                tmbOneServiceResponse.setData(result);
            } else {
                if ("TIMEOUT".equals(status)) {
                    visaQRPayConfirmResponse.setStatus(FIN_SUBMITTED_STATUS);
                    throw PaymentServiceUtils.failException(ResponseCode.QR_VISA_ERR_94102);
                } else {
                    visaQRPayConfirmResponse.setStatus(FIN_FAILED_STATUS);
                    throw PaymentServiceUtils.failException(ResponseCode.FAILED);
                }
            }
        } catch (TMBCommonException e) {
            throw e;
        } catch (Exception e) {
            logger.error("confirm : Error in V2VisaPayController : {} ", e);
            PaymentServiceUtils.handleException(e);
        } finally {
            visaPayService.postConfirmProcess(correlationId, crmId, visaQRPayConfirmResponse, headers, visaQRPayConfirmRequest);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
