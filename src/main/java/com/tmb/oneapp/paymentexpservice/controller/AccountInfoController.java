package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.AccountListResponse;
import com.tmb.oneapp.paymentexpservice.service.AccountService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@AllArgsConstructor
@Tag(name = "AccountInfo , Account Info For International Transfer")
public class AccountInfoController {
    private static final TMBLogger<AccountInfoController> logger = new TMBLogger<>(AccountInfoController.class);
    private AccountService accountService;

    /**
     * Method responsible for controlling API for T&C
     *
     * @param crmId
     * @return
     */
    @Operation(summary = "Account Info")
    @LogAround
    @GetMapping(value = "account/info/list")
    public ResponseEntity<TmbOneServiceResponse<AccountListResponse>> accountInfo(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true)
            @Valid @RequestHeader(name = "X-CRMID") String crmId
    )
    {
        logger.info("account/info/list get data method start Time : {} ", System.currentTimeMillis());
        HttpHeaders httpHeaders = new HttpHeaders();
        TmbOneServiceResponse<AccountListResponse> response = new TmbOneServiceResponse<>();
        try {
            AccountListResponse result = accountService.getInternationalTransferAccount(crmId, correlationId);
            response.setData(result);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(httpHeaders).body(response);

        } catch (Exception e) {
            logger.error("Unable to fetch AccountInfo : {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));
            return ResponseEntity.badRequest().headers(httpHeaders).body(response);
        }
    }

}
