package com.tmb.oneapp.paymentexpservice.controller.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyResponse;
import com.tmb.oneapp.paymentexpservice.service.EWalletPaymentService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller responsible for handling all promptPay v2 endpoints
 */
@RestController
@Tag(name = "EWalletPayment V2 Controller")
public class V2EWalletPaymentController {
    private static final TMBLogger<V2EWalletPaymentController> logger = new TMBLogger<>(V2EWalletPaymentController.class);

    private final EWalletPaymentService eWalletPaymentService;

    public V2EWalletPaymentController(EWalletPaymentService eWalletPaymentService) {
        this.eWalletPaymentService = eWalletPaymentService;
    }

    /**
     * Method responsible for controlling API for transfer prompt-pay validation
     */
    @Operation(summary = "E-Wallet : Validate")
    @Parameters({
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @LogAround
    @PostMapping(value = "/transfer/prompt-pay/validate")
    public ResponseEntity<TmbOneServiceResponse<TopUpVerifyResponse>> eWalletValidate(
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true) @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fwc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Valid @RequestBody TopUpVerifyRequest promptPayRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            TopUpVerifyResponse data = eWalletPaymentService.validate(crmId, correlationId, promptPayRequest, headers);

            tmbOneServiceResponse.setData(data);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            logger.error("eWalletValidate : Error in EWalletPaymentV2Controller : {} ", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @Operation(summary = "E-Wallet : Confirm")
    @Parameters({
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @LogAround
    @PostMapping(value = "/transfer/prompt-pay/confirm")
    public ResponseEntity<TmbOneServiceResponse<TopUpConfirmResponse>> eWalletConfirm(
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8va", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @RequestBody TopUpConfirmRequest topUpConfirmRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        TmbOneServiceResponse<TopUpConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            TopUpConfirmResponse data = eWalletPaymentService.confirm(
                    topUpConfirmRequest,
                    crmId,
                    correlationId,
                    headers
            );

            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(data);

        } catch (Exception e) {
            logger.error("eWalletConfirm confirm top up error {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
