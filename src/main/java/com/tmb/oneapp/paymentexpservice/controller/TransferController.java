package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.TMBDataSuccess;
import com.tmb.oneapp.paymentexpservice.model.TPromptPayVerifyETEResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.TransferData;
import com.tmb.oneapp.paymentexpservice.model.transfer.TransferConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.transfer.TransferOtherBankETERequest;
import com.tmb.oneapp.paymentexpservice.model.transfer.TransferOtherBankVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.transfer.TransferVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.transfer.TransferVerifyResponse;
import com.tmb.oneapp.paymentexpservice.service.FundTransferConfirmationService;
import com.tmb.oneapp.paymentexpservice.service.FundTransferTDValidationServiceImpl;
import com.tmb.oneapp.paymentexpservice.service.FundTransferValidationServiceImpl;
import com.tmb.oneapp.paymentexpservice.service.PromptPayPaymentETEService;
import com.tmb.oneapp.paymentexpservice.service.TransferConfirmationService;
import com.tmb.oneapp.paymentexpservice.service.TransferOtherBankAndPromptPayConfirmationService;
import com.tmb.oneapp.paymentexpservice.service.TransferOtherBankValidationService;
import com.tmb.oneapp.paymentexpservice.service.TransferOtherTTBServiceImpl;
import com.tmb.oneapp.paymentexpservice.service.TransferOwnAndOtherTTBValidationService;
import com.tmb.oneapp.paymentexpservice.service.TransferPromptPayService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;
import java.util.Map;

/**
 * 
 * Controller responsible for handling all own bank transfer related endpoint
 *
 */
@RestController
@AllArgsConstructor
@Tag(name = "Account Service Controller for Transfer")
public class TransferController {
	private static final TMBLogger<TransferController> logger = new TMBLogger<>(TransferController.class);
	private final PromptPayPaymentETEService promptPayPaymentETEService;
	private final FundTransferValidationServiceImpl transferValidationImplService;
	private final FundTransferConfirmationService fundConfirmationImplService;
	private final FundTransferTDValidationServiceImpl fundTransferTDValidationServiceImpl;
	private final TransferOtherTTBServiceImpl otherTTBServiceImpl;
	private final TransferConfirmationService transferConfirmationService;
	private final TransferOtherBankValidationService transferOtherBankValidationService;
	private final TransferPromptPayService transferPromptPayService;
	private final TransferOtherBankAndPromptPayConfirmationService transferOtherBankAndPromptPayConfirmationService;
	private final TransferOwnAndOtherTTBValidationService transferOwnAndOtherTTBValidationService;

	@Operation(summary = "Fund transfer validation")
	@LogAround
	@PostMapping(value = "/transfer/validate")
	public ResponseEntity<TmbServiceResponse<TransferData>> transferValidation(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
			@Parameter(description = "X-CRMID", example = "001100000000000000000000086006", required = true) @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmID,
			@Parameter(description = "X-TRANSFER-TYPE", example = "0", required = true) @RequestHeader("X-TRANSFER-TYPE") @Valid @RequestHeaderNonNull String transferType,
			@RequestBody TransferData reqBody) throws TMBCommonException, TMBCommonExceptionWithResponse {
		logger.info("payment-exp-service  transferValidation method start Time : {} ", System.currentTimeMillis());
		TmbServiceResponse<TransferData> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			TransferData output = new TransferData();
			if (transferType.equalsIgnoreCase(PaymentServiceConstant.ZERO)) {
				output = transferValidationImplService.processValidate(reqBody, correlationId, crmID, transferType);
			} else {
				if (transferType.equalsIgnoreCase(PaymentServiceConstant.CONSTANTONE)) {
					output = otherTTBServiceImpl.processOtherTTBValidate(reqBody, correlationId, crmID, transferType);
				}
			}
			oneServiceResponse.setData(output);
			oneServiceResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), PaymentServiceUtils.returnSuccessDescription()));
			logger.info("payment-exp-service  transferValidation method End Time : {} ", System.currentTimeMillis());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException e) {
			logger.error("Validate Transfer : TMBCommonException : {}", e);
			throw e;
		} catch (Exception e) {
			logger.error("Validate Transfer : Exception : {}", e);
			throw new TMBCommonExceptionWithResponse(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null, null);

		}

	}


	@Operation(summary = "Fund transfer validation for term deposit")
	@LogAround
	@PostMapping(value = "/transfer/tdvalidate")
	public ResponseEntity<TmbServiceResponse<TransferData>> transferTDValidation(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
			@Parameter(description = "X-CRMID", example = "001100000000000000000000086006", required = true) @RequestHeader("X-CRMID") @RequestHeaderNonNull String crmID,
			@RequestBody TransferData reqBody) throws TMBCommonExceptionWithResponse, TMBCommonException {
		logger.info("payment-exp-service  transferTDValidation method start Time : {} ", System.currentTimeMillis());
		TmbServiceResponse<TransferData> oneServiceResponseForTDAcc = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

		try {
			final TransferData transferTDData = fundTransferTDValidationServiceImpl.processTDValidate(reqBody,
					correlationId, crmID);
			oneServiceResponseForTDAcc.setData(transferTDData);
			oneServiceResponseForTDAcc.setStatus(new Status(ResponseCode.SUCCESS.getCode(),
					ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), PaymentServiceUtils.returnSuccessDescription()));
			logger.info("payment-exp-service  transferValidation method End Time : {} ", System.currentTimeMillis());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponseForTDAcc);
		} catch (TMBCommonException e) {
			logger.error("TD Validate : TMBCommonException TD Validate Transfer : {}", e);
			throw e;
		} catch (Exception e) {
			logger.error("TD Validate : Error in TD Validate Transfer : {} ", e);
			throw new TMBCommonExceptionWithResponse(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null, null);
		}

	}

	@Operation(summary = "Fund transfer based on account no")
	@Parameters({
			@Parameter(name = "X-Correlation-ID", description = "Transations ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da"),
			@Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000000086006"),
			@Parameter(name = "Timestamp", description = "Timestamp", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Timestamp"),
			@Parameter(name = "Accept-Language", description = "Accept-Language", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "en-US, th-TH"),
			@Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
			@Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
			@Parameter(name = "Device-Nickname", description = "Device-Nickname", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "test nickname"),
			@Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20"),
			@Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
			@Parameter(name = "X-Forward-For", description = "IP-Address", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
			@Parameter(name = "Flow-Name", description = "Flow-Name", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Mobile"),
			@Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
			@Parameter(name = "Login-Method", description = "Login-Method", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Fingerprint") })
	@LogAround
	@PostMapping(value = "/transfer/confirm")
	public ResponseEntity<TmbServiceResponse<TMBDataSuccess>> fundTransfer(
			@Parameter(hidden = true) @RequestHeader Map<String, String> reqHeader)
			throws TMBCommonExceptionWithResponse, TMBCommonException {
		logger.info("payment-exp-service fundTransfer method start Time : {} ", System.currentTimeMillis());
		TmbServiceResponse<TMBDataSuccess> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			TMBDataSuccess output = fundConfirmationImplService.processTransfer(reqHeader);
			oneServiceResponse.setData(output);
			oneServiceResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), PaymentServiceUtils.returnSuccessDescription()));
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException | TMBCommonExceptionWithResponse e) {
			logger.error("Confirm : Error in Confirmation Transfer : {}", e);
			throw e;
		} catch (Exception e) {
			logger.error("Confirm : Error in Confirmation Transfer : {} ", e);
			throw new TMBCommonExceptionWithResponse(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null, null);

		}

	}


	@LogAround
	@Operation(summary = "Transfer own TTB : Validate ")
	@Parameters({
			@Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
			@Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
			@Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
			@Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
			@Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
			@Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
			@Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
			@Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
	})
	@PostMapping(value = "/transfer/on-us/validate")
	public ResponseEntity<TmbOneServiceResponse<TransferVerifyResponse>> transValidation(
			@RequestBody TransferVerifyRequest reqBody,
			@RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
		TmbOneServiceResponse<TransferVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
		try {
			TransferVerifyResponse data = transferOwnAndOtherTTBValidationService.validate(reqBody, header);

			tmbOneServiceResponse.setData(data);
			tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

		} catch (Exception e) {
			logger.error("Failed to process transferValidation error : {}", e);
			PaymentServiceUtils.handleException(e);
		}

		return ResponseEntity.ok(tmbOneServiceResponse);
	}

	@LogAround
	@Operation(summary = "Transfer own TTB : Confirm")
	@Parameters({
			@Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8ta"),
			@Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
			@Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
			@Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
			@Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
			@Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
			@Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2821b3a"),
			@Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S22")
	})
	@PostMapping(value = "/transfer/on-us/confirm")
	public ResponseEntity<TmbOneServiceResponse<TransferConfirmResponse>> transferConfirm(
			@RequestBody TopUpConfirmRequest transferConfirmRequest,
			@RequestHeader HttpHeaders reqHeader
	) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
		TmbOneServiceResponse<TransferConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
		try {
			TransferConfirmResponse data = transferConfirmationService.confirm(transferConfirmRequest, reqHeader);

			tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
			tmbOneServiceResponse.setData(data);

		} catch (Exception e) {
			logger.error("Failed to process transferConfirm error : {}", e);
			PaymentServiceUtils.handleException(e);
		}

		return ResponseEntity.ok(tmbOneServiceResponse);
	}

	@Operation(summary = "Transfer Other Bank : Validate ")
	@LogAround
	@PostMapping(value = "/transfer/other-bank-validate")
	public ResponseEntity<TmbOneServiceResponse<TransferVerifyResponse>> transOtherBankValidate(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
			@Parameter(description = "X-CRMID", example = "001100000000000000000000086006", required = true) @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmId,
			@RequestBody TransferOtherBankVerifyRequest otherBankVerifyRequest,
			@RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
		TmbOneServiceResponse<TransferVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
		try {
			TransferVerifyResponse data = transferOtherBankValidationService.validate(otherBankVerifyRequest, crmId, correlationId, header);


			tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
			tmbOneServiceResponse.setData(data);

		} catch (Exception e) {
			logger.error("Failed to process transOtherBankValidate error : {}", e);
			PaymentServiceUtils.handleException(e);
		}

		return ResponseEntity.ok(tmbOneServiceResponse);
	}
	
	@LogAround
	@Operation(summary = "Transfer Other Bank : PromptPay Validation ")
	@PostMapping(value = "/transfer/promptpay-validation")
	public ResponseEntity<TmbOneServiceResponse<TPromptPayVerifyETEResponse>> validatePromptPay(
					@Parameter(description = "X-Correlation-ID", example = "fffbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
					@RequestBody TransferOtherBankETERequest transferBankEteRequest,
					@RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
		TmbOneServiceResponse<TPromptPayVerifyETEResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
		try {
			TPromptPayVerifyETEResponse data = promptPayPaymentETEService.validateTransPromptPayToETE(transferBankEteRequest);
			tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
			tmbOneServiceResponse.setData(data);
		} catch (Exception e) {
			logger.error("Failed to process validatePromptPay error : {}", e);
			PaymentServiceUtils.handleException(e);
		}
		return ResponseEntity.ok(tmbOneServiceResponse);
	}

	@Operation(summary = "Transfer Other-Bank And Prompt Pay : Confirm ")
	@LogAround
	@PostMapping(value = {"/transfer/other-bank-confirm", "/transfer/promptpay-confirm"})
	public ResponseEntity<TmbOneServiceResponse<TransferConfirmResponse>> transOtherBankAndPromptPayConfirm(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
			@Parameter(description = "X-CRMID", example = "001100000000000000000000086006", required = true) @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmId,
			@RequestBody TopUpConfirmRequest reqBody,
			@RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
		TmbOneServiceResponse<TransferConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
		try {
			TransferConfirmResponse data = transferOtherBankAndPromptPayConfirmationService.confirm(reqBody.getTransId(), crmId, correlationId, header, reqBody.getFrUuid());

			tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
			tmbOneServiceResponse.setData(data);

		} catch (Exception e) {
			logger.error("Failed to process transOtherBankAndPromptPayConfirm error : {}", e);
			PaymentServiceUtils.handleException(e);
		}

		return ResponseEntity.ok(tmbOneServiceResponse);
	}



	@LogAround
	@Operation(summary = "Prompt-Pay : Validate")
	@Parameters({
			@Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
			@Parameter(name = "X-CRM-ID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
			@Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
			@Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
			@Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
			@Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
			@Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
			@Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
	})
	@PostMapping(value = "/transfer/promptpay-validate")
	public ResponseEntity<TmbOneServiceResponse<TransferVerifyResponse>> promptPayValidate(
			@RequestBody TransferOtherBankVerifyRequest otherBankVerifyRequest,
			@RequestHeader HttpHeaders header
	) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
		TmbOneServiceResponse<TransferVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
		try {
			TransferVerifyResponse promptPayVerifyResponse = transferPromptPayService.validate(otherBankVerifyRequest, header);

			tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
			tmbOneServiceResponse.setData(promptPayVerifyResponse);

		} catch (Exception e) {
			logger.error("Failed to process promptPayValidate error : {}", e);
			PaymentServiceUtils.handleException(e);
		}

		return ResponseEntity.ok(tmbOneServiceResponse);
	}

}
