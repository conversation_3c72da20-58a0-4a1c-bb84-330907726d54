package com.tmb.oneapp.paymentexpservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.financialcert.FinancialDirectDebitConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.financialcert.FinancialDirectDebitConfirmResponse;
import com.tmb.oneapp.paymentexpservice.service.v1.V1FinancialService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;


import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;

@RestController
@Tag(name = "V1 Financial Controller")
@AllArgsConstructor
public class V1FinancialController {

    public static final TMBLogger<V1FinancialController> logger = new TMBLogger<>(V1FinancialController.class);
    private final V1FinancialService v1FinancialService;

    @Operation(summary = "API to Confirm deduct payment transaction")
    @PostMapping(value = "/financial/direct-debit/confirm", consumes = "application/json", produces = "application/json")
    public ResponseEntity<TmbServiceResponse<FinancialDirectDebitConfirmResponse>> financialDirectDebitConfirm(
            @Parameter(description = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader(HEADER_CORRELATION_ID) final String correlationId,
            @Parameter(description = X_CRMID, required = true) @RequestHeader(X_CRMID) final String crmId,
            @RequestBody FinancialDirectDebitConfirmRequest request
    )
            throws TMBCommonException {
        logger.info("directDebitConfirm method start time {}", System.currentTimeMillis());
        try {
            TmbServiceResponse<FinancialDirectDebitConfirmResponse> response
                    = v1FinancialService.getFinancialDirectDebitConfirm(correlationId, crmId, request);
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);
        }catch (TMBCommonException ex) {
            throw ex;
        } catch (Exception e) {
            throw new TMBCommonException(ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
    }

}
