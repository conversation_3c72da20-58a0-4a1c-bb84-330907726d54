package com.tmb.oneapp.paymentexpservice.controller.v1;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.controller.InternationalTransferController;
import com.tmb.oneapp.paymentexpservice.model.foreignexchange.AccountCurrencyExchangeResponse;
import com.tmb.oneapp.paymentexpservice.service.v1.foreignexchange.CurrencyExchangeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_TIMESTAMP;
import static com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils.getCustomMessage;
import static com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils.getResponseSuccess;

@RestController
@Tag(name = "APIs for foreign exchange module")
@RequiredArgsConstructor
public class V1ForeignExchangeController {
    private static final TMBLogger<InternationalTransferController> logger = new TMBLogger<>(InternationalTransferController.class);

    private final CurrencyExchangeService currencyExchangeService;

    @LogAround
    @Operation(summary = "Get account list for currency exchange")
    @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da")
    @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000000031310")
    @GetMapping(value = "/accounts/currency-exchange", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<AccountCurrencyExchangeResponse>> getAccountsForCurrencyExchange(
            @Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId) {
        HttpHeaders respHeaders = new HttpHeaders();
        TmbOneServiceResponse<AccountCurrencyExchangeResponse> response = new TmbOneServiceResponse<>();
        try {
            AccountCurrencyExchangeResponse accountCurrencyExchangeResponse = currencyExchangeService.getAllAccountForCurrencyExchange(crmId, correlationId);
            response.setStatus(getResponseSuccess());
            response.setData(accountCurrencyExchangeResponse);
            respHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
            return ResponseEntity.ok().headers(respHeaders).body(response);
        } catch (Exception e) {
            String errMsg = getCustomMessage(e);
            logger.error("Failed to get account for currency exchange: {}", errMsg, e);
            response.setStatus(new TmbStatus(
                    ResponseCode.FAILED.getCode(),
                    errMsg,
                    ResponseCode.FAILED.getService(),
                    ResponseCode.FAILED.getDescription()
            ));
            return ResponseEntity.badRequest().headers(respHeaders).body(response);
        }
    }
}
