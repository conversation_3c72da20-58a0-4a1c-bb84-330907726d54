package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.directdebit.BillerDirectDebit;
import com.tmb.oneapp.paymentexpservice.model.directdebit.BillerDirectDebitCategory;
import com.tmb.oneapp.paymentexpservice.model.directdebit.BillerDirectDebitDetail;
import com.tmb.oneapp.paymentexpservice.model.directdebit.BillerModel;
import com.tmb.oneapp.paymentexpservice.model.directdebit.DirectDebitConsentRequest;
import com.tmb.oneapp.paymentexpservice.model.directdebit.DirectDebitConsentResponse;
import com.tmb.oneapp.paymentexpservice.model.directdebit.DirectDebitRegisterRequest;
import com.tmb.oneapp.paymentexpservice.model.directdebit.DirectDebitRegisterResponse;
import com.tmb.oneapp.paymentexpservice.model.directdebit.InquiryConsentReq;
import com.tmb.oneapp.paymentexpservice.model.directdebit.InquiryConsentRes;
import com.tmb.oneapp.paymentexpservice.service.DirectDebitService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.D_ALPHABET;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.N_ALPHABET;

@RestController
@RequiredArgsConstructor
@Tag(name = "Controller for Direct Debit")
public class DirectDebitController {
    private static final TMBLogger<DirectDebitController> logger = new TMBLogger<>(DirectDebitController.class);
    private static final String GET_BILLER_LIST_URL = "/direct-debit/biller-search";
    private static final String GET_BILLER_DETAIL_URL = "/direct-debit/biller-detail";
    private static final String POST_REGISTER_URL = "/direct-debit/register";
    private static final String POST_CANCEL_URL = "/direct-debit/cancel";
    private static final String POST_CONSENT_LIST_URL = "/direct-debit/consent-list";

    private final DirectDebitService directDebitService;

    /**
     * This method is using to Get Category list of Direct debit
     */
    @Operation(summary = "Get Category List")
    @LogAround
    @GetMapping("/direct-debit/category-list")
    public ResponseEntity<TmbOneServiceResponse<List<BillerDirectDebitCategory>>> getBillerCategory(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId) {
        TmbOneServiceResponse<List<BillerDirectDebitCategory>> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            List<BillerDirectDebitCategory> billerCategoryRes = directDebitService.getBillerCategory(correlationId);
            response.setData(billerCategoryRes);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.error("Unable to getBillerCategory : {} ", e.getMessage(), e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

            return ResponseEntity.badRequest().headers(responseHeaders).body(response);
        }
    }

    @Operation(summary = "Inquiry consented data of Direct debit")
    @LogAround
    @PostMapping(value = "/direct-debit/inquiry-consent-request",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<InquiryConsentRes>> inquiryConsent(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "X-CRMID", example = "001100000000000000000002307246", required = true)
            @Valid @RequestHeader("X-CRMID") @RequestHeaderNonNull String crmId,
            @RequestHeader Map<String, String> headers,
            @RequestBody InquiryConsentReq inquiryConsentReq) {
        TmbOneServiceResponse<InquiryConsentRes> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        try {
            List<BillerModel> data = new ArrayList<>();
            String compId = "9628";
            BillerDirectDebitDetail billerDirectDebitDetail = directDebitService.getBillerDetail(correlationId, compId);

            BillerModel billerModel = BillerModel.builder()
                    .channelApply("MB")
                    .compId("1111")
                    .compAccountNo("0123456")
                    .reference1("1111")
                    .reference2("2222")
                    .billerNameTh("ค่าไฟฟ้า")
                    .billerNameEn("TEST")
                    .aliasConsent("จ่ายค่าโทรศัพท์")
                    .build();

            if (billerDirectDebitDetail != null) {
                billerModel.setReference1Label(billerDirectDebitDetail.getRef1Label());
                billerModel.setReference2Label(billerDirectDebitDetail.getRef2Label());
            }

            data.add(billerModel);
            response.setData(InquiryConsentRes.builder().compList(data).build());
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));

            directDebitService.initApplyDirectDebitLog(headers, inquiryConsentReq, data, ResponseCode.SUCCESS.getCode().equals(response.getStatus().getCode()));

            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.error("Unable to InquiryConsent : {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

            return ResponseEntity.badRequest().headers(responseHeaders).body(response);
        }
    }

    @LogAround
    @Operation(summary = "Search Biller of Direct debit")
    @GetMapping(GET_BILLER_LIST_URL)
    public ResponseEntity<TmbOneServiceResponse<List<BillerDirectDebit>>> searchBiller(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @RequestParam(name = "suggest_flag", required = false) boolean suggestFlag,
            @RequestParam(name = "category", required = false) String category,
            @RequestParam(name = "key_word", required = false) String keyWord) {
        TmbOneServiceResponse<List<BillerDirectDebit>> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            response.setData(directDebitService.searchBiller(correlationId, suggestFlag, category, keyWord));
            response.setStatus(getStatusSuccess());
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.error("Unable to InquiryConsent : {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    ResponseCode.FAILED.getDescription()));
            return ResponseEntity.badRequest().headers(responseHeaders).body(response);
        }
    }

    @LogAround
    @Operation(summary = "Get Biller Detail of Direct debit")
    @GetMapping(GET_BILLER_DETAIL_URL)
    public ResponseEntity<TmbOneServiceResponse<BillerDirectDebitDetail>> getBillerDetail(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @RequestParam("compId") String compId) {
        logger.error("getBillerDetail compId : {} ", compId);
        TmbOneServiceResponse<BillerDirectDebitDetail> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            response.setData(directDebitService.getBillerDetail(correlationId, compId));
            response.setStatus(getStatusSuccess());
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.error("Unable to getBillerDetail : {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    ResponseCode.FAILED.getDescription()));
            return ResponseEntity.badRequest().headers(responseHeaders).body(response);
        }
    }

    @LogAround
    @Operation(summary = "Direct debit registration")
    @PostMapping(POST_REGISTER_URL)
    public ResponseEntity<TmbOneServiceResponse<DirectDebitRegisterResponse>> register(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000000031310", required = true)
            @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @RequestHeader Map<String, String> headers,
            @Valid @RequestBody DirectDebitRegisterRequest directDebitRegisterRequest) {
        TmbOneServiceResponse<DirectDebitRegisterResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            if (isNotExpectedFlag(N_ALPHABET, directDebitRegisterRequest.getOperationFlag())) {
                response.setStatus(getStatusBadRequest());
                return ResponseEntity.badRequest().headers(responseHeaders).body(response);
            }

            response.setData(directDebitService.register(headers, correlationId, crmId, directDebitRegisterRequest));
            response.setStatus(getStatusSuccess());
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.error("Unable to register : {} ", e);
            response.setStatus(getStatusFailedWithDescription(e.getMessage()));
            return ResponseEntity.badRequest().headers(responseHeaders).body(response);
        }
    }

    @LogAround
    @Operation(summary = "Cancel direct debit registration")
    @PostMapping(POST_CANCEL_URL)
    public ResponseEntity<TmbOneServiceResponse<DirectDebitRegisterResponse>> cancel(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000000031310", required = true)
            @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @RequestHeader Map<String, String> headers,
            @Valid @RequestBody DirectDebitRegisterRequest directDebitRegisterRequest) {
        TmbOneServiceResponse<DirectDebitRegisterResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            if (isNotExpectedFlag(D_ALPHABET, directDebitRegisterRequest.getOperationFlag())) {
                response.setStatus(getStatusBadRequest());
                return ResponseEntity.badRequest().headers(responseHeaders).body(response);
            }

            response.setData(directDebitService.register(headers, correlationId, crmId, directDebitRegisterRequest));
            response.setStatus(getStatusSuccess());
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            response.setStatus(getStatusFailedWithDescription(e.getMessage()));
            logger.error("Unable to cancel direct debit : {} ", e);
            return ResponseEntity.badRequest().headers(responseHeaders).body(response);
        }
    }

    @LogAround
    @Operation(summary = "Get direct debit consent list")
    @PostMapping(POST_CONSENT_LIST_URL)
    public ResponseEntity<TmbOneServiceResponse<List<DirectDebitConsentResponse>>> getConsentList(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000000031310", required = true)
            @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @RequestHeader Map<String, String> headers,
            @Valid @RequestBody DirectDebitConsentRequest directDebitRegisterRequest) {
        TmbOneServiceResponse<List<DirectDebitConsentResponse>> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            response.setData(directDebitService.getDirectDebitConsentList(headers, correlationId, crmId, directDebitRegisterRequest));
            response.setStatus(getStatusSuccess());
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.error("Unable to get consent list : {} ", e);
            if (ResponseCode.ATS_NOT_ALLOW_REGISTER.getCode().equalsIgnoreCase(e.getMessage())) {
                response.setStatus(new TmbStatus(
                        ResponseCode.ATS_NOT_ALLOW_REGISTER.getCode(),
                        ResponseCode.ATS_NOT_ALLOW_REGISTER.getMessage(),
                        ResponseCode.ATS_NOT_ALLOW_REGISTER.getService(),
                        ResponseCode.ATS_NOT_ALLOW_REGISTER.getDescription()
                ));
                return ResponseEntity.ok().headers(responseHeaders).body(response);
            }
            response.setStatus(getStatusFailedWithDescription(e.getMessage()));
            return ResponseEntity.badRequest().headers(responseHeaders).body(response);
        }
    }

    private TmbStatus getStatusSuccess() {
        return new TmbStatus(ResponseCode.SUCCESS.getCode(),
                ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(),
                ResponseCode.SUCCESS.getDescription());
    }

    private TmbStatus getStatusBadRequest() {
        return new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
                ResponseCode.BAD_REQUEST.getMessage(),
                ResponseCode.BAD_REQUEST.getService(),
                ResponseCode.BAD_REQUEST.getDescription());
    }

    private TmbStatus getStatusFailedWithDescription(String description) {
        return new TmbStatus(ResponseCode.FAILED.getCode(),
                ResponseCode.FAILED.getMessage(),
                ResponseCode.FAILED.getService(),
                description);
    }

    private boolean isNotExpectedFlag(String expected, String operationFlag) {
        return !StringUtils.equalsIgnoreCase(expected, operationFlag);
    }
}
