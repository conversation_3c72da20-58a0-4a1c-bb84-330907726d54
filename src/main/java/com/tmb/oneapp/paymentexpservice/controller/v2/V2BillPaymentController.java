package com.tmb.oneapp.paymentexpservice.controller.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.MEAValidationResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.MWAValidationResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.PEAValidationResponse;
import com.tmb.oneapp.paymentexpservice.model.v2.BillPaymentModule;
import com.tmb.oneapp.paymentexpservice.service.BillPayTransactionSelectService;
import com.tmb.oneapp.paymentexpservice.service.BillPaymentValidationMEAService;
import com.tmb.oneapp.paymentexpservice.service.BillPaymentValidationMWAService;
import com.tmb.oneapp.paymentexpservice.service.BillPaymentValidationPEAService;
import com.tmb.oneapp.paymentexpservice.service.CustomBillPaymentConfirmService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import com.tmb.oneapp.paymentexpservice.validator.v2.BillPaymentValidator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for handling bill payment through One App system
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "BillPayment V2 Controller")
public class V2BillPaymentController {
    private static final TMBLogger<V2BillPaymentController> logger = new TMBLogger<>(V2BillPaymentController.class);

    private final BillPayTransactionSelectService billPayTransactionSelectService;
    private final BillPaymentValidationPEAService billPaymentValidationPEAService;
    private final BillPaymentValidationMEAService billPaymentValidationMEAService;
    private final CustomBillPaymentConfirmService customBillPaymentConfirmService;
    private final BillPaymentValidationMWAService billPaymentValidationMWAService;
    private final BillPaymentValidator billPaymentValidator;

    /**
     * API for validating bill payment information
     *
     * @param crmId customer ID
     * @param correlationId correlation ID for tracking transactions
     * @param topUpVerifyRequest bill payment information
     * @param headers HTTP headers
     * @return validation result
     */
    @LogAround
    @Operation(summary = "Bill Payment Validation")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb")
    })
    @PostMapping(value = "/bill-payment/validation")
    public ResponseEntity<TmbOneServiceResponse<TopUpVerifyResponse>> billPayValidation(
            @Valid @RequestHeader(name = "X-CRMID") @RequestHeaderNonNull String crmId,
            @Valid @RequestHeader(name = "X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Valid @RequestBody TopUpVerifyRequest topUpVerifyRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpVerifyResponse> response = new TmbOneServiceResponse<>();

        try {
            billPaymentValidator.validateTopUpVerifyRequest(topUpVerifyRequest);
            TopUpVerifyResponse verifyResponse = billPayTransactionSelectService.verifyBillPayment(
                    crmId,
                    correlationId,
                    topUpVerifyRequest,
                    headers);

            response.setStatus(PaymentServiceUtils.getResponseSuccess());
            response.setData(verifyResponse);

        } catch (Exception e) {
            logger.error("Failed to process billPayValidation: {}", e.getMessage(), e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(response);
    }

    /**
     * API for confirming bill payment
     *
     * @param crmId Customer ID
     * @param topUpConfirmRequest Payment confirmation information
     * @param correlationId Transaction tracking ID
     * @param headers HTTP headers
     * @return Payment confirmation result
     */
    @LogAround
    @Operation(summary = "Bill Payment Confirm")
    @Parameters({
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675")
    })
    @PostMapping(value = "/bill-payment/confirm")
    public ResponseEntity<TmbOneServiceResponse<TopUpConfirmResponse>> billPayConfirm(
            @Valid @RequestHeader(name = "X-CRMID") @RequestHeaderNonNull String crmId,
            @Valid @RequestHeader(name = "X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Valid @RequestBody TopUpConfirmRequest topUpConfirmRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpConfirmResponse> response = new TmbOneServiceResponse<>();

        try {
            TopUpConfirmResponse confirmResponse = billPayTransactionSelectService.confirmBillPayment(
                    crmId,
                    correlationId,
                    topUpConfirmRequest,
                    headers
            );

            response.setStatus(PaymentServiceUtils.getResponseSuccess());
            response.setData(confirmResponse);

        } catch (Exception e) {
            logger.error("Failed to process billPayConfirm: {}", e.getMessage(), e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(response);
    }

    /**
     * API for validating MEA payment information
     *
     * @param headers HTTP headers
     * @param topUpVerifyRequest payment information
     * @return validation result
     */
    @LogAround
    @Operation(summary = "MEA payment Validation")
    @Parameters({
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da")
    })
    @PostMapping(value = "/bill-payment/mea/validation")
    public ResponseEntity<TmbOneServiceResponse<MEAValidationResponse>> validationOfMEA(
            @RequestBody @Valid TopUpVerifyRequest topUpVerifyRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<MEAValidationResponse> response = new TmbOneServiceResponse<>();

        try {
            MEAValidationResponse verifyResponse = (MEAValidationResponse) billPaymentValidationMEAService.validation(
                    topUpVerifyRequest,
                    headers
            );

            response.setData(verifyResponse);
            response.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            logger.error("Failed to process validationOfMEA: {}", e.getMessage(), e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(response);
    }

    /**
     * API for validating PEA electricity payment information
     *
     * @param headers HTTP headers
     * @param topUpVerifyRequest payment information
     * @return validation result
     */
    @Operation(summary = "PEA payment Validation")
    @LogAround
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb")
    })
    @PostMapping(value = "/bill-payment/pea/validation")
    public ResponseEntity<TmbOneServiceResponse<PEAValidationResponse>> validationOfPEA(
            @Valid @RequestBody TopUpVerifyRequest topUpVerifyRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<PEAValidationResponse> response = new TmbOneServiceResponse<>();

        try {
            PEAValidationResponse verifyResponse = (PEAValidationResponse) billPaymentValidationPEAService.validation(
                    topUpVerifyRequest,
                    headers
            );

            response.setStatus(PaymentServiceUtils.getResponseSuccess());
            response.setData(verifyResponse);

        } catch (Exception e) {
            logger.error("Failed to process validationOfPEA: {}", e.getMessage(), e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(response);
    }

    /**
     * API for validating MWA water bill payment information
     *
     * @param headers HTTP headers
     * @param topUpVerifyRequest payment information
     * @return validation result
     */
    @Operation(summary = "MWA payment Validation")
    @Parameters({
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb")
    })
    @LogAround
    @PostMapping(value = "/bill-payment/mwa/validation")
    public ResponseEntity<TmbOneServiceResponse<MWAValidationResponse>> validationOfMWA(
            @Valid @RequestBody TopUpVerifyRequest topUpVerifyRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<MWAValidationResponse> response = new TmbOneServiceResponse<>();

        try {
            MWAValidationResponse verifyResponse = (MWAValidationResponse) billPaymentValidationMWAService.validation(
                    topUpVerifyRequest,
                    headers
            );

            response.setData(verifyResponse);
            response.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            logger.error("Failed to process validationOfMWA: {}", e.getMessage(), e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(response);
    }

    /**
     * API for confirming bill payments (PEA, MEA, MWA)
     *
     * @param module Service type (pea, mea, mwa)
     * @param headers HTTP headers
     * @param topUpConfirmRequest Payment confirmation information
     * @return Payment confirmation result
     */
    @Operation(summary = "Custom payment Confirm")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "1111d3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb")
    })
    @LogAround
    @PostMapping(value = "/bill-payment/{module}/confirm")
    public ResponseEntity<TmbOneServiceResponse<TopUpConfirmResponse>> confirmModule(
            @PathVariable String module,
            @Valid @RequestBody TopUpConfirmRequest topUpConfirmRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpConfirmResponse> response = new TmbOneServiceResponse<>();

        try {
            if (!BillPaymentModule.isValidModule(module)) {
                return ResponseEntity.badRequest().headers(headers).body(response);
            }

            TopUpConfirmResponse confirmResponse = customBillPaymentConfirmService.paymentConfirm(
                    topUpConfirmRequest,
                    headers,
                    "CUSTOM_BILL_PAY"
            );

            response.setStatus(PaymentServiceUtils.getResponseSuccess());
            response.setData(confirmResponse);

        } catch (Exception e) {
            logger.error("Failed to process confirm module = {}: {}", module, e.getMessage(), e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(response);
    }
}