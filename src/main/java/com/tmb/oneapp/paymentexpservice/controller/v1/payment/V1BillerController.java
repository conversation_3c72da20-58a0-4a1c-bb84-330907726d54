package com.tmb.oneapp.paymentexpservice.controller.v1.payment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBEventLog;
import com.tmb.common.logger.TTBEventMonitoringType;
import com.tmb.common.logger.TTBEventStatus;
import com.tmb.common.logger.TTBPayloadType;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.paymentexpservice.service.v1.payment.V1BillerService;
import com.tmb.oneapp.paymentexpservice.utils.CircuitBreakerUtils;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

@RestController
@RequiredArgsConstructor
public class V1BillerController {
    private static final TMBLogger<V1BillerController> logger = new TMBLogger<>(V1BillerController.class);
    private static final String BILLER_URI = "/biller/";
    private final V1BillerService billerService;

    @LogAround
    @GetMapping("/biller/by-biller-id/{billerId}")
    @Operation(summary = "Get Biller Top Up Detail")
    public ResponseEntity<TmbOneServiceResponse<BillerTopUpDetailResponse>> getBillerByBillerId(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @Parameter(description = "billerId", example = "2704", required = true) @PathVariable(value = "billerId") String billerId
    ) throws TMBCommonException, JsonProcessingException {
        logger.info("common-service getBillerByBillerId with Correlation ID {}, billerId: {}", correlationId, billerId);
        TmbOneServiceResponse<BillerTopUpDetailResponse> response = new TmbOneServiceResponse<>();

        long startTime = System.currentTimeMillis();
        logger.payload(TTBPayloadType.INBOUND, Collections.emptyMap(), new ObjectMapper().writeValueAsString(Collections.emptyMap()));

        try {
            BillerTopUpDetailResponse billerTopUpDetailResponse = billerService.getBillerDetailBillPayByBillerId(billerId, correlationId);

            response.setStatus(getResponseSuccess());
            response.setData(billerTopUpDetailResponse);

            logger.event(new TTBEventLog(
                    TTBEventMonitoringType.BUSINESS,
                    "/biller/by-biller-id/" + billerId,
                    TTBEventStatus.SUCCESS,
                    200,
                    (int) (System.currentTimeMillis() - startTime)
            ));

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.event(new TTBEventLog(
                    TTBEventMonitoringType.BIZ_ERROR,
                    "/biller/by-biller-id/" + billerId,
                    TTBEventStatus.FAIL,
                    200,
                    (int) (System.currentTimeMillis() - startTime)
            ));

            throw dataNotFoundException();
        }
    }

    @LogAround
    @PostMapping({"/biller/bill-pay", "/biller/gamification/bill-pay"})
    @Operation(summary = "Get Biller CompCodes Detail")
    public ResponseEntity<TmbOneServiceResponse<List<BillerTopUpDetailResponse>>> getBillersDetail(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestBody List<String> compCodes
    ) throws TMBCommonException, JsonProcessingException {
        logger.info("common-service get Biller CompCodes Detail with Correlation ID {}", correlationId);
        TmbOneServiceResponse<List<BillerTopUpDetailResponse>> response = new TmbOneServiceResponse<>();
        long startTime = System.currentTimeMillis();
        logger.payload(TTBPayloadType.INBOUND, Collections.emptyMap(), new ObjectMapper().writeValueAsString(Collections.emptyMap()));
        try {
            List<BillerTopUpDetailResponse> billerTopUpDetailResponseList = billerService.getBillersDetailBillPay(compCodes, correlationId);
            response.setStatus(getResponseSuccess());
            response.setData(billerTopUpDetailResponseList);

            logger.event(new TTBEventLog(
                    TTBEventMonitoringType.BUSINESS,
                    BILLER_URI,
                    TTBEventStatus.SUCCESS,
                    200,
                    (int) (System.currentTimeMillis() - startTime)
            ));

            return ResponseEntity.ok(response);
        } catch (CallNotPermittedException ce) {
            logger.error("getBillerTopUpDetail: {}", ce);
            logger.event(new TTBEventLog(
                    TTBEventMonitoringType.BIZ_ERROR,
                    BILLER_URI,
                    TTBEventStatus.FAIL,
                    200,
                    (int) (System.currentTimeMillis() - startTime)
            ));

            throw CircuitBreakerUtils.circuitBreakerExceptionHandling();
        } catch (TMBCommonException e) {
            logger.event(new TTBEventLog(
                    TTBEventMonitoringType.BIZ_ERROR,
                    BILLER_URI,
                    TTBEventStatus.FAIL,
                    200,
                    (int) (System.currentTimeMillis() - startTime)
            ));
            throw e;
        } catch (Exception e) {
            logger.event(new TTBEventLog(
                    TTBEventMonitoringType.BIZ_ERROR,
                    BILLER_URI,
                    TTBEventStatus.FAIL,
                    200,
                    (int) (System.currentTimeMillis() - startTime)
            ));

            throw dataNotFoundException();
        }
    }

    private TMBCommonException dataNotFoundException() {
        return new TMBCommonException(
                ResponseCode.FAILED.getCode(),
                ResponseCode.FAILED.getMessage(),
                ResponseCode.FAILED.getService(),
                HttpStatus.OK,
                null);
    }

    private TmbStatus getResponseSuccess() {
        return new TmbStatus(
                ResponseCode.SUCCESS.getCode(),
                ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(),
                null);
    }
}
