package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import com.tmb.oneapp.paymentexpservice.service.TransactionLimitService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.sql.SQLException;
import java.time.Instant;

@RestController
@Tag(name = "Transfer Exp Controller for Transaction Limt")
public class TransactionLimitController {
	private static final TMBLogger<TransactionLimitController> logger = new TMBLogger<>(
			TransactionLimitController.class);
	final private TransactionLimitService transactionLimitService;

	@Autowired
	public TransactionLimitController(TransactionLimitService transactionLimitService) {
		this.transactionLimitService = transactionLimitService;
	}

	@Operation(summary = "Fetch Transaction Limit")
	@LogAround
	@GetMapping(value = "/transactionLimit")
	public ResponseEntity<TmbOneServiceResponse<CustomerCrmProfile>> fetchTransactionLimitByCrmID(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader("X-Correlation-ID") @Valid @RequestHeaderNonNull String correlationId,
			@Parameter(description = "X-CRMID", example = "001100000000000000000000086006", required = true) @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmID) {
		logger.info("payment-exp-service  transferValidation method start Time : {} ", System.currentTimeMillis());
		TmbOneServiceResponse<CustomerCrmProfile> oneServiceResponse = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			final CustomerCrmProfile output = transactionLimitService.fetchTransactionLimit(correlationId, crmID);
			oneServiceResponse.setData(output);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
			logger.info("payment-exp-service  transferValidation method End Time : {} ", System.currentTimeMillis());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (DataAccessResourceFailureException | SQLException e) {
			logger.error("Error in Validate Transfer : {} ", e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
		}

	}
}
