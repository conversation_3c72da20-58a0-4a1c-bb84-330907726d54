package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.model.myqr.MyQRRequest;
import com.tmb.oneapp.paymentexpservice.model.myqr.MyQRResponse;
import com.tmb.oneapp.paymentexpservice.model.myqr.QrAccountDetail;
import com.tmb.oneapp.paymentexpservice.service.CustomerDeviceStatusService;
import com.tmb.oneapp.paymentexpservice.service.MyQRService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
public class MyQRController {
    private static final TMBLogger<MyQRController> logger = new TMBLogger<>(MyQRController.class);

    private final CustomerDeviceStatusService customerDeviceStatusService;
    private final MyQRService myQRService;

    public MyQRController(CustomerDeviceStatusService customerDeviceStatusService, MyQRService myQRService) {
        this.customerDeviceStatusService = customerDeviceStatusService;
        this.myQRService = myQRService;
    }

    @LogAround
    @Operation(summary = "Generate MY QR Payment")
    @PostMapping(value = "/promptpay/my-qr/generate")
    public ResponseEntity<TmbOneServiceResponse<MyQRResponse>> getPromptPayQrCode(
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @RequestBody MyQRRequest myQRRequest,
            @RequestHeader HttpHeaders headers) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<MyQRResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
            MyQRResponse myQRResponse = myQRService.generateMyQR(myQRRequest, crmId, correlationId, headers);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(myQRResponse);

        } catch (Exception e) {
            logger.error("Failed to process getPromptPayQrCode error : {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "GET Promptpay Account")
    @GetMapping(value = "/promptpay/my-qr/accounts")
    public ResponseEntity<TmbOneServiceResponse<QrAccountDetail>> getPromptPayAccounts(
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<QrAccountDetail> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
            QrAccountDetail qrAccountDetail = myQRService.getQrAccountDetail(crmId, correlationId);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(qrAccountDetail);

        } catch (Exception e) {
            logger.error("Failed to process getPromptPayAccount error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
