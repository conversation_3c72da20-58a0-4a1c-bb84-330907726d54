package com.tmb.oneapp.paymentexpservice.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.PPValidateData;
import com.tmb.oneapp.paymentexpservice.model.PromptPayReqData;
import com.tmb.oneapp.paymentexpservice.model.ResponseData;
import com.tmb.oneapp.paymentexpservice.service.FundTransferPromptPayConfirmationService;
import com.tmb.oneapp.paymentexpservice.service.FundTransferPromptPayValidationServiceImpl;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.time.Instant;
import java.util.Map;

/**
 * 
 * Controller responsible for handling all promptPay and otherBank transfer
 * related endpoints
 *
 */
@RestController
@Tag(name = "Account Service Controller for promptpay and otherBankTransfer")
public class TransferPromptPayController {
	private static final TMBLogger<TransferPromptPayController> logger = new TMBLogger<>(
			TransferPromptPayController.class);

	private final FundTransferPromptPayValidationServiceImpl promptPayService;
	private final FundTransferPromptPayConfirmationService promptPayConfirm;

	@Autowired
	public TransferPromptPayController(FundTransferPromptPayValidationServiceImpl promptPayService,
			FundTransferPromptPayConfirmationService promptPayConfirm) {
		this.promptPayService = promptPayService;
		this.promptPayConfirm = promptPayConfirm;
	}

	/**
	 * Method responsible for controlling API for transfer validation
	 * 
	 * @param correlationId
	 * @param reqBody
	 * @return
	 * @throws Exception
	 * @throws JsonProcessingException
	 * @throws UnsupportedEncodingException
	 */
	@Operation(summary = "Fund transfer validation")
	@LogAround
	@PostMapping(value = "/transfer/promptvalidate")
	public ResponseEntity<TmbServiceResponse<PPValidateData>> validatePromptPay(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
			@Parameter(description = "X-CRMID", example = "001100000000000000000000086006", required = true) @Valid @RequestHeader("X-CRMID") @RequestHeaderNonNull String crmID,
			@Parameter(description = "X-PROXY-TYPE", example = "0", required = true) @Valid @RequestHeader("X-PROXY-TYPE") @RequestHeaderNonNull String proxyType,
			@Parameter(description = "channel", example = "mb", required = true) @Valid @RequestHeader("Channel") @RequestHeaderNonNull String channelType,
			@Valid @RequestBody PromptPayReqData reqBody) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
		logger.info("TransferPromptPayController  transferValidation method start Time : {} ",
				System.currentTimeMillis());
		TmbServiceResponse<PPValidateData> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

		try {
			PPValidateData output = promptPayService.processPromptPayValidate(reqBody, correlationId, crmID, proxyType,
					channelType);
			oneServiceResponse.setData(output);
			oneServiceResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), PaymentServiceUtils.returnSuccessDescription()));
			logger.info("payment-exp-service  transferValidation method End Time : {} ", System.currentTimeMillis());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException | TMBCustomCommonExceptionWithResponse e) {
			logger.error("PromptValidate : Error in TransferPromptPayController : {} ", e);
			throw e;
		} catch (Exception e) {
			logger.error("PromptValidate : Error in TransferPromptPayController : {} ", e);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
		}
	}

	@Operation(summary = "Fund confirmation for other and promptpay")
	@Parameters({
			@Parameter(name = "X-Correlation-ID", description = "Transations ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da"),
			@Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000000086006"),
			@Parameter(name = "Accept-Language", description = "Accept-Language", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "en-US, th-TH"),
			@Parameter(name = "Timestamp", description = "Timestamp", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Timestamp"),
			@Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
			@Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20"),
			@Parameter(name = "Device-Nickname", description = "Device-Nickname", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "test nickname"),
			@Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
			@Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
			@Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
			@Parameter(name = "X-Forward-For", description = "IP-Address", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = ""),
			@Parameter(name = "Flow-Name", description = "Flow-Name", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Mobile"),
			@Parameter(name = "Login-Method", description = "Login-Method", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Fingerprint"),
			@Parameter(name = "X-TRANSFER-ID", description = "X-TRANSFER-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "TRANSFER_001100000000000000000001184383_f6df5e78-bd34-41ba-95cb-9f43f31889e7") })
	@PostMapping(value = "/transfer/promptconfirm")
	public ResponseEntity<TmbServiceResponse<ResponseData>> confirmPromptpay(
			@Parameter(hidden = true) @RequestHeader Map<String, String> reqHeader)
			throws TMBCommonException, TMBCustomCommonExceptionWithResponse, TMBCommonExceptionWithResponse {
		logger.info("TransferPromptPayController fundTransfer method start Time : {} ", System.currentTimeMillis());
		TmbServiceResponse<ResponseData> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			oneServiceResponse.setData(promptPayConfirm.processPromptPayTransfer(reqHeader));
			oneServiceResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), PaymentServiceUtils.returnSuccessDescription()));
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException | TMBCommonExceptionWithResponse | TMBCustomCommonExceptionWithResponse e) {
			logger.error("PromptConfirm : TMBCommonException Error in TransferPromptPayController : {} ", e);
			throw e;
		} catch (Exception e) {
			logger.error("PromptConfirm : Exception Error in TransferPromptPayController : {}", e);
			throw new TMBCommonExceptionWithResponse(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null, null);
		}

	}

}
