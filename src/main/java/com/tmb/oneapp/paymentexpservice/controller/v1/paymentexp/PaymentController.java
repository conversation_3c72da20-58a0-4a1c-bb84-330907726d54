package com.tmb.oneapp.paymentexpservice.controller.v1.paymentexp;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.ScanQrRequest;
import com.tmb.oneapp.paymentexpservice.model.ScanQrResponse;
import com.tmb.oneapp.paymentexpservice.model.v1.V1AccountBillpayResponse;
import com.tmb.oneapp.paymentexpservice.service.CustomerDeviceStatusService;
import com.tmb.oneapp.paymentexpservice.service.v1.AccountBillPayRequest;
import com.tmb.oneapp.paymentexpservice.service.v1.payment.PaymentService;
import com.tmb.oneapp.paymentexpservice.service.v1.payment.ScanQRService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PRE_LOGIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;
import static com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils.getResponseSuccess;

@RestController
@RequiredArgsConstructor
public class PaymentController {
    private static final TMBLogger<PaymentController> logger = new TMBLogger<>(PaymentController.class);
    private final ScanQRService scanQRService;
    private final PaymentService paymentService;
    private final CustomerDeviceStatusService customerDeviceStatusService;

    @PostMapping(value = "/qr-scan")
    public ResponseEntity<TmbOneServiceResponse<ScanQrResponse>> scanQrCode(
            @Parameter(description = "Correlation ID",
                    example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader(name = HEADER_CORRELATION_ID) @SuppressWarnings("unused") String correlationId,
            @Parameter(description = "Device ID",
                    example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76")
            @Valid @RequestHeader(name = DEVICE_ID, required = false) @SuppressWarnings("unused") String deviceId,
            @Parameter(description = "CRM ID",
                    example = "001100000000000000000001184383")
            @Valid @RequestHeader(name = X_CRMID, required = false) @SuppressWarnings("unused") String crmId,
            @RequestHeader HttpHeaders headers,
            @RequestBody ScanQrRequest scanQrRequest
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<ScanQrResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            if (StringUtils.isBlank(crmId) && StringUtils.isNotBlank(deviceId)) {
                crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
                headers.set(PRE_LOGIN, "true");
                headers.set(X_CRMID, crmId);
            }
            ScanQrResponse scanQrResponse = scanQRService.scanQrCode(scanQrRequest, headers);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(scanQrResponse);
        } catch (TMBCommonException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Failed to process scan QR code error: {}", e.getMessage(), e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Get bill pay account list")
    @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8ta")
    @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675")
    @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER)
    @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0")
    @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb")
    @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0")
    @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2821b3a")
    @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S22")
    @PostMapping(value = "/billpay/account-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<V1AccountBillpayResponse>> getBillPayAccountList(
            @RequestHeader HttpHeaders headers,
            @RequestBody AccountBillPayRequest request) {
        TmbOneServiceResponse<V1AccountBillpayResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            tmbOneServiceResponse.setStatus(getResponseSuccess());
            tmbOneServiceResponse.setData(paymentService.getBillPayAccountList(headers, request));
        } catch (Exception ex) {
            logger.error("Failed to get bill pay account list in getBillPayAccountList, with error: {}", ex.getMessage(), ex);
            tmbOneServiceResponse.setStatus(
                    new TmbStatus(
                            ResponseCode.FAILED.getCode(),
                            ex.getMessage(),
                            ResponseCode.FAILED.getService(),
                            null
                    )
            );
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

}
