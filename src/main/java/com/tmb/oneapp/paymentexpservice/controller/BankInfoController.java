package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.BankInfoDataModel;
import com.tmb.oneapp.paymentexpservice.service.BankInfoService;
import feign.FeignException;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.text.ParseException;
import java.time.Instant;
import java.util.List;

/**
 * BankInfoController request mapping will handle apis call and then navigate to
 * respective method to get Bank Information
 *
 */
@RestController
@Tag(name = "API To Fetch All Bank Information")
public class BankInfoController {
	private static final TMBLogger<BankInfoController> logger = new TMBLogger<>(BankInfoController.class);
	private final BankInfoService bankInfoService;

	/**
	 * Constructor
	 * 
	 * @param bankInfoService
	 */
	@Autowired
	public BankInfoController(BankInfoService bankInfoService) {
		this.bankInfoService = bankInfoService;
	}

	/**
	 * Method responsible for controlling API for BankInfo
	 * 
	 * @param correlationId
	 * @return
	 */
	@Operation(summary = "Get All Bank Information")
	@LogAround
	@GetMapping(value = "/transfer/bankInfo")
	public ResponseEntity<TmbOneServiceResponse<List<BankInfoDataModel>>> fetchAllBankInfo(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId) {
		logger.info("BankInfoController  fetchAllBankInfo method start Time : {} ", System.currentTimeMillis());
		TmbOneServiceResponse<List<BankInfoDataModel>> oneServiceResponseForBankInfo = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

		try {
			final List<BankInfoDataModel> transferBankInfoData = bankInfoService.getAllBankInfo(correlationId);
			oneServiceResponseForBankInfo.setData(transferBankInfoData);
			oneServiceResponseForBankInfo
					.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
							ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponseForBankInfo);
		} catch (FeignException | ParseException e) {
			logger.error("Unable to fetch BankInfo : {} ", e);
			oneServiceResponseForBankInfo
					.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
							ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponseForBankInfo);
		}

	}

	@Operation(summary = "Get All Bank Information fot transfer")
	@LogAround
	@GetMapping(value = "/transfer-bank-info")
	public ResponseEntity<TmbOneServiceResponse<List<BankInfoDataModel>>> getBanksInfo(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId) {
		logger.info("BankInfoController getAllBankInfo method start Time : {} ", System.currentTimeMillis());
		TmbOneServiceResponse<List<BankInfoDataModel>> oneServiceResponseForBankInfo = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

		try {
			final List<BankInfoDataModel> transferBankInfoData = bankInfoService.getBanksInfo(correlationId);
			oneServiceResponseForBankInfo.setData(transferBankInfoData);
			oneServiceResponseForBankInfo
					.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
							ResponseCode.SUCCESS.getMessage(),
							ResponseCode.SUCCESS.getService(),
							null));
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponseForBankInfo);
		} catch (Exception e) {
			logger.error("Unable to fetch BankInfo : {} ", e);
			oneServiceResponseForBankInfo
					.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
							ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));
			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponseForBankInfo);
		}
	}
}
