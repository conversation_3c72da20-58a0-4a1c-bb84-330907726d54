package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyResponse;
import com.tmb.oneapp.paymentexpservice.model.transfer.TransferOtherBankVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.transfer.TransferVerifyResponse;
import com.tmb.oneapp.paymentexpservice.service.BillPayTransactionSelectService;
import com.tmb.oneapp.paymentexpservice.service.CustomerDeviceStatusService;
import com.tmb.oneapp.paymentexpservice.service.EWalletPaymentService;
import com.tmb.oneapp.paymentexpservice.service.TransferPromptPayService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PRE_LOGIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;

@RestController
@RequestMapping("/pre-login")
public class PreLoginPaymentValidation {
    private static final TMBLogger<PreLoginPaymentValidation> logger = new TMBLogger<>(PreLoginPaymentValidation.class);

    private final BillPayTransactionSelectService billPayTransactionSelectService;
    private final CustomerDeviceStatusService customerDeviceStatusService;
    private final EWalletPaymentService eWalletPaymentService;
    private final TransferPromptPayService transferPromptPayService;

    public PreLoginPaymentValidation(BillPayTransactionSelectService billPayTransactionSelectService, CustomerDeviceStatusService customerDeviceStatusService, EWalletPaymentService eWalletPaymentService, TransferPromptPayService transferPromptPayService) {
        this.billPayTransactionSelectService = billPayTransactionSelectService;
        this.customerDeviceStatusService = customerDeviceStatusService;
        this.eWalletPaymentService = eWalletPaymentService;
        this.transferPromptPayService = transferPromptPayService;
    }

    @LogAround
    @Operation(summary = "Pre Login Verify Bill Payment")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/bill-payment/validation")
    public ResponseEntity<TmbOneServiceResponse<TopUpVerifyResponse>> billPayVerify(
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Valid @RequestBody TopUpVerifyRequest topUpVerifyRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);

            headers.set(X_CRMID, crmId);
            headers.set(PRE_LOGIN, "true");
            TopUpVerifyResponse topUpVerifyResponse = billPayTransactionSelectService.verifyBillPayment(
                    crmId,
                    correlationId,
                    topUpVerifyRequest,
                    headers);

            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(topUpVerifyResponse);

        } catch (Exception e) {
            logger.error("Failed to process billPayVerify error : {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Pre Login Prompt-Pay TopUp validation")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/transfer/prompt-pay/validate")
    public ResponseEntity<TmbOneServiceResponse<TopUpVerifyResponse>> eWalletValidate(
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Valid @RequestBody TopUpVerifyRequest promptPayRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
            headers.set(X_CRMID, crmId);
            headers.set(PRE_LOGIN, "true");
            TopUpVerifyResponse promptPayVerifyResponse = eWalletPaymentService.validate(crmId, correlationId, promptPayRequest, headers);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(promptPayVerifyResponse);

        } catch (Exception e) {
            logger.error("Failed to process eWalletValidate error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Prompt-Pay : Validate")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/transfer/promptpay-validate")
    public ResponseEntity<TmbOneServiceResponse<TransferVerifyResponse>> promptPayValidate(
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @RequestBody TransferOtherBankVerifyRequest otherBankVerifyRequest,
            @RequestHeader HttpHeaders header
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TransferVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
            header.add(X_CRMID, crmId);
            header.set(PRE_LOGIN, "true");
            TransferVerifyResponse promptPayVerifyResponse = transferPromptPayService.validate(otherBankVerifyRequest, header);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(promptPayVerifyResponse);

        } catch (Exception e) {
            logger.error("Failed to process promptPayValidate error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
