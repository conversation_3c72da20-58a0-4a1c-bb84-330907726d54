package com.tmb.oneapp.paymentexpservice.controller.v1;

import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.service.v1.V1GenerateSequenceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

import java.time.Instant;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DIGITS;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_TIMESTAMP;

@RestController
public class V1GenerateSequenceController {
    public static final TMBLogger<V1GenerateSequenceController> logger = new TMBLogger<>(V1GenerateSequenceController.class);
    private final V1GenerateSequenceService generateSequenceService;

    @Autowired
    public V1GenerateSequenceController(final V1GenerateSequenceService service) {
        this.generateSequenceService = service;
    }

    @Operation(summary = "generating sequence key for financial_reference_id_sequence")
    @PutMapping("/generate-sequence/financial-reference-id-sequence")
    public ResponseEntity<TmbOneServiceResponse<String>> financialReferenceIdSequenceKey(
            @Parameter(description = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = "value", required = true) @RequestBody String digits
    ) throws Exception {
        logger.info("Generating sequence key for financial_reference_id_sequence for id - {} with digits - {}", correlationId, digits);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(HEADER_CORRELATION_ID, correlationId);
        responseHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        String sequenceKey = generateSequenceService.financialReferenceIdSequenceKey(digits);

        TmbOneServiceResponse<String> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
        tmbOneServiceResponse.setData(sequenceKey);
        logger.info("Generating sequence key for financial_reference_id_sequence successfully, key: {}", sequenceKey);

        return ResponseEntity.ok().headers(responseHeaders).body(tmbOneServiceResponse);
    }

    @Operation(summary = "generating sequence key for financial_reference_id_sequence")
    @PutMapping("/generate-transaction-sequence/financial-reference-id-sequence")
    public ResponseEntity<TmbOneServiceResponse<String>> financialReferenceIdSequenceTransaction(
            @Parameter(description = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = "value", required = true) @RequestBody String digits
    ) throws Exception {
        logger.info("Generating transaction id for financial_reference_id_sequence for id - {} with digits - {}", correlationId, digits);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(HEADER_CORRELATION_ID, correlationId);
        responseHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        String transactionId = generateSequenceService.financialReferenceIdSequenceTransaction(digits);

        TmbOneServiceResponse<String> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
        tmbOneServiceResponse.setData(transactionId);
        logger.info("Generating transaction id for financial_reference_id_sequence successfully, transactionId: {}", transactionId);

        return ResponseEntity.ok().headers(responseHeaders).body(tmbOneServiceResponse);
    }


    @Operation(summary = "generating sequence key for pea-trans-ref-sequence")
    @PutMapping("/generate-sequence/pea-trans-ref-sequence")
    public ResponseEntity<TmbOneServiceResponse<String>> peaTransRefSequenceKey(
            @Parameter(description = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = "value", required = true) @RequestBody String digits
    ) throws Exception {
        logger.info("Generating sequence key for pea-trans-ref-sequence for id - {} with digits - {}", correlationId, digits);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(HEADER_CORRELATION_ID, correlationId);
        responseHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        String sequenceKey = generateSequenceService.peaTransRefSequenceKey(digits);

        TmbOneServiceResponse<String> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
        tmbOneServiceResponse.setData(sequenceKey);
        logger.info("Generating sequence key for pea-trans-ref-sequence successfully, key: {}", sequenceKey);

        return ResponseEntity.ok().headers(responseHeaders).body(tmbOneServiceResponse);
    }

    @Operation(summary = "generating sequence key for pea-trans-ref-sequence")
    @PutMapping("/generate-transaction-sequence/pea-trans-ref-sequence")
    public ResponseEntity<TmbOneServiceResponse<String>> peaTransRefSequenceTransaction(
            @Parameter(description = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = "value", required = true) @RequestBody String digits
    ) throws Exception {
        logger.info("Generating transaction id for pea-trans-ref-sequence for id - {} with digits - {}", correlationId, digits);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(HEADER_CORRELATION_ID, correlationId);
        responseHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        String transactionId = generateSequenceService.peaTransRefSequenceTransaction(digits);

        TmbOneServiceResponse<String> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
        tmbOneServiceResponse.setData(transactionId);
        logger.info("Generating transaction id for pea-trans-ref-sequence successfully, transactionId: {}", transactionId);

        return ResponseEntity.ok().headers(responseHeaders).body(tmbOneServiceResponse);
    }

    @Operation(summary = "Update - generate-sequence/online-trans-ref-sequence")
    @PutMapping("/generate-sequence/online-trans-ref-sequence")
    public ResponseEntity<TmbOneServiceResponse<String>> generateSequenceOnlineTransRefSequence(
            @Parameter(description = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = DIGITS, required = true) @RequestBody String digits)
            throws Exception {

        logger.info("Start to generate sequence key for online_trans_ref_sequence, correlation id: {} and digits: {}", correlationId, digits);
        String sequenceKey = generateSequenceService.generateSequenceOnlineTransRefSequence(digits);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(HEADER_CORRELATION_ID, correlationId);
        responseHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
        response.setData(sequenceKey);
        logger.info("Generating sequence key for online_trans_ref_sequence successfully, key: {}", sequenceKey);

        return ResponseEntity.ok().headers(responseHeaders).body(response);
    }

    @Operation(summary = "Update - generate-transaction-sequence/online-trans-ref-sequence")
    @PutMapping("/generate-transaction-sequence/online-trans-ref-sequence")
    public ResponseEntity<TmbOneServiceResponse<String>> generateTransactionSequenceOnlineTransRefSequence(
            @Parameter(description = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = DIGITS, required = true) @RequestBody String digits)
            throws Exception {

        logger.info("Start to generate transaction sequence for online_trans_ref_sequence, correlation id: {} and digits: {}", correlationId, digits);
        String transactionId = generateSequenceService.generateTransactionSequenceOnlineTransRefSequence(digits);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(HEADER_CORRELATION_ID, correlationId);
        responseHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
        response.setData(transactionId);
        logger.info("Generated transaction sequence for online_trans_ref_sequence successfully, transactionId: {}", transactionId);

        return ResponseEntity.ok().headers(responseHeaders).body(response);
    }


    @Operation(summary = "Update - generate-sequence/promptpay-ref-sequence")
    @PutMapping("/generate-sequence/promptpay-ref-sequence")
    public ResponseEntity<TmbOneServiceResponse<String>> generateSequencePromptpayRefSequence(
            @Parameter(description = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = DIGITS, required = true) @RequestBody String digits)
            throws Exception {

        String sequenceKey = generateSequenceService.generateSequencePromptpayRefSequence(digits);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(HEADER_CORRELATION_ID, correlationId);
        responseHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
        response.setData(sequenceKey);

        return ResponseEntity.ok().headers(responseHeaders).body(response);
    }

    @Operation(summary = "Update - generate-transaction-sequence/promptpay-ref-sequence")
    @PutMapping("/generate-transaction-sequence/promptpay-ref-sequence")
    public ResponseEntity<TmbOneServiceResponse<String>> generateTransactionSequencePromptpayRefSequence(
            @Parameter(description = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = DIGITS, required = true) @RequestBody String digits)
            throws Exception {

        String sequenceKey = generateSequenceService.generateTransactionSequencePromptpayRefSequence(digits);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(HEADER_CORRELATION_ID, correlationId);
        responseHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
        response.setData(sequenceKey);

        return ResponseEntity.ok().headers(responseHeaders).body(response);
    }
}