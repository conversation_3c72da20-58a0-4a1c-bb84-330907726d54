package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.model.DepositAccount;
import com.tmb.oneapp.paymentexpservice.model.PreLoginScanQrResponse;
import com.tmb.oneapp.paymentexpservice.model.ScanQrRequest;
import com.tmb.oneapp.paymentexpservice.model.ScanQrResponse;
import com.tmb.oneapp.paymentexpservice.service.AccountService;
import com.tmb.oneapp.paymentexpservice.service.CustomerDeviceStatusService;
import com.tmb.oneapp.paymentexpservice.service.ScanRoutingService;
import com.tmb.oneapp.paymentexpservice.service.v1.payment.ScanQRService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.Map;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;

@RestController
public class ScanQRController {

    private static final TMBLogger<ScanQRController> logger = new TMBLogger<>(ScanQRController.class);

    private final ScanQRService scanQRService;
    private final AccountService accountService;
    private final ScanRoutingService scanRoutingService;
    private final CustomerDeviceStatusService customerDeviceStatusService;

    public ScanQRController(ScanQRService scanQRService,
                            AccountService accountService,
                            ScanRoutingService scanRoutingService,
                            CustomerDeviceStatusService customerDeviceStatusService) {
        this.scanQRService = scanQRService;
        this.accountService = accountService;
        this.scanRoutingService = scanRoutingService;
        this.customerDeviceStatusService = customerDeviceStatusService;
    }

    @PostMapping(value = "/pre-login/bill-payment/scan")
    public ResponseEntity<TmbOneServiceResponse<PreLoginScanQrResponse>> scanQrCodePreLogin(
            @Parameter(description = "Correlation ID",
                    example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Parameter(description = "Device ID",
                    example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76",
                    required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @RequestHeader HttpHeaders headers,
            @RequestBody ScanQrRequest scanQrRequest
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<PreLoginScanQrResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
            headers.set(X_CRMID, crmId);
            ScanQrResponse scanQrResponse = scanRoutingService.scanQrCode(scanQrRequest, headers);

            PreLoginScanQrResponse preLoginScanQrResponse = new PreLoginScanQrResponse();
            BeanUtils.copyProperties(scanQrResponse, preLoginScanQrResponse);
            if (!scanQrResponse.getQrType().equals(PaymentServiceConstant.PAYMENT_QR_TYPE_VERIFY_PAYSLIP)) {
                Map<String, Object> accountDetail = accountService.getAccountDetailPreLogin(
                        scanQrResponse.getQrType(),
                        crmId,
                        correlationId);

                DepositAccount depositAccount = scanQRService.getAccountDetail(crmId, correlationId);
                preLoginScanQrResponse.setDepositAccount(depositAccount);

                preLoginScanQrResponse.setRemaining((String) accountDetail.get("remaining"));
            }

            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(preLoginScanQrResponse);
        } catch (Exception e) {
            logger.error("Failed to process scanQrCodePreLogin error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @PostMapping(value = "/bill-payment/scan")
    public ResponseEntity<TmbOneServiceResponse<ScanQrResponse>> scanQrCode(
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true)
            @SuppressWarnings({"unused", "SpellCheckingInspection"})
            @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID",
                    example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader(name = "X-Correlation-ID")
            @SuppressWarnings("unused") String correlationId,
            @RequestHeader HttpHeaders headers,
            @RequestBody ScanQrRequest scanQrRequest
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<ScanQrResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            ScanQrResponse scanQrResponse = scanRoutingService.scanQrCode(scanQrRequest, headers);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(scanQrResponse);
        } catch (Exception e) {
            logger.error("Failed to process scanQrCode error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
