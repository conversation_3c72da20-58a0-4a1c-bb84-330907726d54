package com.tmb.oneapp.paymentexpservice.controller.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyResponse;
import com.tmb.oneapp.paymentexpservice.service.BillPayTransactionSelectService;
import com.tmb.oneapp.paymentexpservice.service.CustomerDeviceStatusService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PRE_LOGIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;

@RestController
@RequestMapping("/pre-login")
@Tag(name = "PreLogin BillPayment V2 Controller")
public class V2PreLoginBillPaymentController {
    private static final TMBLogger<V2PreLoginBillPaymentController> logger = new TMBLogger<>(V2PreLoginBillPaymentController.class);

    private final BillPayTransactionSelectService billPayTransactionSelectService;
    private final CustomerDeviceStatusService customerDeviceStatusService;
    
    @Autowired
    public V2PreLoginBillPaymentController(BillPayTransactionSelectService billPayTransactionSelectService,
                                           CustomerDeviceStatusService customerDeviceStatusService) {
        this.billPayTransactionSelectService = billPayTransactionSelectService;
        this.customerDeviceStatusService = customerDeviceStatusService;
    }

    @LogAround
    @Operation(summary = "Pre Login Verify Bill Payment")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/bill-payment/validation")
    public ResponseEntity<TmbOneServiceResponse<TopUpVerifyResponse>> billPayValidation(
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Valid @RequestBody TopUpVerifyRequest topUpVerifyRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);

            headers.set(PRE_LOGIN, "true");
            headers.set(X_CRMID, crmId);
            TopUpVerifyResponse topUpVerifyResponse = billPayTransactionSelectService.verifyBillPayment(
                    crmId,
                    correlationId,
                    topUpVerifyRequest,
                    headers);

            tmbOneServiceResponse.setData(topUpVerifyResponse);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            logger.error("Failed to process billPayValidation error : {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }
    
    @LogAround
    @Operation(summary = "Pre Login Bill Payment Confirm")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/bill-payment/confirm")
    public ResponseEntity<TmbOneServiceResponse<TopUpConfirmResponse>> billPayConfirm(
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Valid @RequestBody TopUpConfirmRequest topUpConfirmRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
            headers.set(PRE_LOGIN, "true");
            headers.set(X_CRMID, crmId);
            TopUpConfirmResponse confirmResponse = billPayTransactionSelectService.confirmBillPayment(crmId, correlationId, topUpConfirmRequest, headers);
            tmbOneServiceResponse.setData(confirmResponse);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            logger.error("Failed to process billPayConfirm error : {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
