package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.InternationalTransferConstant;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.InternationalAddressResponse;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.InternationalAddressUpdateReq;
import com.tmb.oneapp.paymentexpservice.service.InternationalAddressService;
import com.tmb.oneapp.paymentexpservice.service.InternationalTransferService;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.Map;

/**
 * InternationalAddressController request mapping will handle apis call and then navigate to
 * respective method to get International Address
 */
@RestController
@Tag(name = "API International Address")
public class InternationalAddressController {
    private static final TMBLogger<InternationalAddressController> logger = new TMBLogger<>(InternationalAddressController.class);
    private final InternationalAddressService internationalAddressService;
    private final InternationalTransferService internationalTransferService;

    /**
     * Constructor
     */
    @Autowired
    public InternationalAddressController(InternationalAddressService internationalAddressService, InternationalTransferService internationalTransferService) {
        this.internationalAddressService = internationalAddressService;
        this.internationalTransferService = internationalTransferService;
    }

    /**
     * Method responsible for controlling API for get InternationalAddress
     */
    @Operation(summary = "Get International Address")
    @LogAround
    @GetMapping(value = "/ott/international-address")
    public ResponseEntity<TmbOneServiceResponse<InternationalAddressResponse>> getInternationalAddress(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @Valid @RequestHeaderNonNull String correlationId,
            @Parameter(description = "X-CRMID", example = "001100000000000000000000086006", required = true)
            @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmID) {

        logger.info("InternationalAddressController  getInternationalAddress method start Time : {} ", System.currentTimeMillis());

        TmbOneServiceResponse<InternationalAddressResponse> oneServiceResponse = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {
            final InternationalAddressResponse output = internationalAddressService.getInternationalAddress(crmID);
            oneServiceResponse.setData(output);
            oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
        } catch (Exception e) {
            logger.error("Unable to fetch International Address : {} ", e);
            return this.buildResponse(ResponseEntity.badRequest(), ResponseCode.FAILED);
        }

    }


    /**
     * Method responsible for controlling API for Add InternationalAddress
     */
    @Operation(summary = "add International Address")
    @LogAround
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
    })
    @PostMapping(value = "/ott/international-address/add")
    public ResponseEntity<TmbOneServiceResponse<Object>> addInternationalAddress(
            @Valid @RequestBody InternationalAddressUpdateReq inputReq,
            @Parameter(hidden = true) @RequestHeader Map<String, String> reqHeader
    ) {
        logger.info("InternationalAddressController addInternationalAddress method start Time : {} ", System.currentTimeMillis());
        try {
            String correlationId = reqHeader.get(PaymentServiceConstant.HEADER_CORRELATION_ID.toLowerCase());
            String crmId = reqHeader.get(PaymentServiceConstant.X_CRMID.toLowerCase());
            String refId = reqHeader.get(InternationalTransferConstant.TRANS_REF_ID_HEADER.toLowerCase());

            internationalTransferService.validatePin(correlationId, crmId, refId);
            internationalAddressService.addInternationalAddress(reqHeader, inputReq);
            return this.buildResponse(ResponseEntity.ok(), ResponseCode.SUCCESS);
        } catch (Exception e) {
            logger.error("Unable to add new international address  : {} ", e);
            return this.buildResponse(ResponseEntity.badRequest(), ResponseCode.FAILED);
        }
    }

    /**
     * Method responsible for controlling API for Edit InternationalAddress
     */
    @Operation(summary = "edit International Address")
    @LogAround
    @PostMapping(value = "/ott/international-address/edit")
    public ResponseEntity<TmbOneServiceResponse<Object>> editInternationalAddress(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "X-CRMID", example = "001100000000000000000000086006", required = true)
            @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmId,
            @RequestBody @Valid InternationalAddressUpdateReq inputReq,
            @RequestHeader HttpHeaders header) {
        logger.info("InternationalAddressController editInternationalAddress method start Time : {} ", System.currentTimeMillis());
        try {
            String refId = header.getFirst(InternationalTransferConstant.TRANS_REF_ID_HEADER);
            internationalTransferService.validatePin(correlationId, crmId, refId);
            internationalAddressService.editInternationalAddress(header, inputReq, crmId, correlationId);
            return this.buildResponse(ResponseEntity.ok(), ResponseCode.SUCCESS);
        } catch (Exception e) {
            logger.error("Unable to edit international address  : {} ", e);
            return this.buildResponse(ResponseEntity.badRequest(), ResponseCode.FAILED);
        }
    }

    /**
     * Method responsible for controlling API for Add InternationalAddress (without pin verify)
     */
    @Operation(summary = "add International Address (without pin verify)")
    @LogAround
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
    })
    @PostMapping(value = "/ott/international-address/add-without-pin-verify")
    public ResponseEntity<TmbOneServiceResponse<Object>> addInternationalAddressWithoutPinVerify(
            @Valid @RequestBody InternationalAddressUpdateReq inputReq,
            @Parameter(hidden = true) @RequestHeader Map<String, String> reqHeader) {
        logger.info("InternationalAddressController addInternationalAddress without pin verify method start Time : {} ", System.currentTimeMillis());
        try {
            internationalAddressService.addInternationalAddress(reqHeader, inputReq);
            return this.buildResponse(ResponseEntity.ok(), ResponseCode.SUCCESS);
        } catch (Exception e) {
            logger.error("Unable to add new international address  : {} ", e);
            return this.buildResponse(ResponseEntity.badRequest(), ResponseCode.FAILED);
        }
    }

    /**
     * Method responsible for controlling API for Edit InternationalAddress (without pin verify)
     */
    @Operation(summary = "edit International Address (without pin verify)")
    @LogAround
    @PostMapping(value = "/ott/international-address/edit-without-pin-verify")
    public ResponseEntity<TmbOneServiceResponse<Object>> editInternationalAddressWithoutPinVerify(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "X-CRMID", example = "001100000000000000000000086006", required = true)
            @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmId,
            @Valid @RequestBody InternationalAddressUpdateReq inputReq,
            @RequestHeader HttpHeaders header) {
        logger.info("InternationalAddressController editInternationalAddress without pin verify method start Time : {} ", System.currentTimeMillis());
        try {
            internationalAddressService.editInternationalAddress(header, inputReq, crmId, correlationId);
            return this.buildResponse(ResponseEntity.ok(), ResponseCode.SUCCESS);
        } catch (Exception e) {
            logger.error("Unable to edit international address  : {} ", e);
            return this.buildResponse(ResponseEntity.badRequest(), ResponseCode.FAILED);
        }
    }

    private <T> ResponseEntity<TmbOneServiceResponse<T>> buildResponse
            (ResponseEntity.BodyBuilder bodyBuilder, ResponseCode responseCode) {
        TmbOneServiceResponse<T> response = new TmbOneServiceResponse<>();
        TmbStatus tmbStatus = new TmbStatus(responseCode.getCode(),
                responseCode.getMessage(),
                responseCode.getService(),
                responseCode.getDescription());
        response.setStatus(tmbStatus);
        return bodyBuilder.headers(new HttpHeaders()).body(response);
    }

}
