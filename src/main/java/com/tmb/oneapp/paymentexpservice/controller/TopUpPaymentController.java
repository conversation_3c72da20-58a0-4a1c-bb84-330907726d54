package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.PaymentDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyResponse;
import com.tmb.oneapp.paymentexpservice.model.billpay.PaymentTransactionLog;
import com.tmb.oneapp.paymentexpservice.model.deeplink.DeepLinkRequest;
import com.tmb.oneapp.paymentexpservice.model.deeplink.DeepLinkResponse;
import com.tmb.oneapp.paymentexpservice.service.BillPayTransactionSelectService;
import com.tmb.oneapp.paymentexpservice.service.BillPaymentDeepLinkService;
import com.tmb.oneapp.paymentexpservice.service.EWalletPaymentService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.regex.Pattern;

@RestController
public class TopUpPaymentController {
    private static final TMBLogger<TopUpPaymentController> logger = new TMBLogger<>(TopUpPaymentController.class);
    private final EWalletPaymentService eWalletPaymentService;
    private final BillPayTransactionSelectService billPayTransactionSelectService;
    private final BillPaymentDeepLinkService billPaymentDeepLinkService;
    private static final String BLACKLIST = "[\"'=<>~\\\\]"; // Regex pattern for the blacklist characters
    private static final String REF_ALLOW = "^$|^[a-zA-Z\\d\\s._\\/\\-]+$";

    @Autowired
    public TopUpPaymentController(EWalletPaymentService eWalletPaymentService, BillPayTransactionSelectService billPayTransactionSelectService, BillPaymentDeepLinkService billPaymentDeepLinkService) {
        this.eWalletPaymentService = eWalletPaymentService;
        this.billPayTransactionSelectService = billPayTransactionSelectService;
        this.billPaymentDeepLinkService = billPaymentDeepLinkService;
    }

    @LogAround
    @Operation(summary = "Verify TopUp Payment")
    @Parameters({
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/bill-payment/validation", produces = "application/json;charset=UTF-8")
    public ResponseEntity<TmbOneServiceResponse<TopUpVerifyResponse>> billPayVerify(
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true) @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @RequestBody TopUpVerifyRequest topUpVerifyRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {

            if (containsBlacklistChars(topUpVerifyRequest.getNote())){
                logger.error("Validated topUpVerifyRequest Note failed. {}",topUpVerifyRequest.getNote(), null);
                throw PaymentServiceUtils.failException(ResponseCode.INCORRECT_MEMO_NOTE);
            }

            if(!checkBillpayChars(topUpVerifyRequest.getReference1())){
                logger.error("Validated topUpVerifyRequest getReference1 failed. {}",topUpVerifyRequest.getReference1(), null);
                throw PaymentServiceUtils.failException(ResponseCode.INCORRECT_REF1_REF2);
            }
            if(!checkBillpayChars(topUpVerifyRequest.getReference2())){
                logger.error("Validated topUpVerifyRequest getReference2 failed. {}",topUpVerifyRequest.getReference2(), null);
                throw PaymentServiceUtils.failException(ResponseCode.INCORRECT_REF1_REF2);
            }
            if(!checkBillpayChars(topUpVerifyRequest.getReference3())){
                logger.error("Validated topUpVerifyRequest getReference3 failed. {}",topUpVerifyRequest.getReference3(), null);
                throw PaymentServiceUtils.failException(ResponseCode.INCORRECT_REF1_REF2);
            }

            TopUpVerifyResponse topUpVerifyResponse;
            topUpVerifyResponse = billPayTransactionSelectService.verifyBillPayment(crmId, correlationId, topUpVerifyRequest, headers);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(topUpVerifyResponse);

        } catch (Exception e) {
            logger.error("Failed to process billPayVerify error : {}", e, null);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Confirm TopUp Payment")
    @Parameters({
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/bill-payment/confirm", produces = "application/json;charset=UTF-8")
    public ResponseEntity<TmbOneServiceResponse<TopUpConfirmResponse>> confirmBillPayment(
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @RequestBody TopUpConfirmRequest topUpConfirmRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            TopUpConfirmResponse topUpConfirmResponse = billPayTransactionSelectService.confirmBillPayment(crmId, correlationId, topUpConfirmRequest, headers);
            tmbOneServiceResponse.setData(topUpConfirmResponse);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            logger.error("Failed to process confirmBillPayment error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "E-Wallet : Validate")
    @Parameters({
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/transfer/prompt-pay/validate")
    public ResponseEntity<TmbOneServiceResponse<TopUpVerifyResponse>> eWalletValidate(
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true) @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fwc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Valid @RequestBody TopUpVerifyRequest promptPayRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            TopUpVerifyResponse data = eWalletPaymentService.validate(crmId, correlationId, promptPayRequest, headers);

            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(data);

        } catch (Exception e) {
            logger.error("promptPayTopUpVerify : Error in TopUpPaymentController : {} ", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "E-Wallet : Confirm")
    @Parameters({
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/transfer/prompt-pay/confirm")
    public ResponseEntity<TmbOneServiceResponse<TopUpConfirmResponse>> eWalletConfirm(
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8va", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @RequestBody TopUpConfirmRequest topUpConfirmRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            TopUpConfirmResponse data = eWalletPaymentService.confirm(
                    topUpConfirmRequest,
                    crmId,
                    correlationId,
                    headers
            );


            tmbOneServiceResponse.setData(data);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            logger.error("promptPayTopUpConfirm confirm top up error {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @PostMapping(value = "/bill-payment/payment-detail")
    public ResponseEntity<TmbOneServiceResponse<PaymentDetailResponse>> billPaymentDetail(
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true) @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Valid @RequestBody TopUpVerifyRequest paymentDetailRequest
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<PaymentDetailResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            PaymentDetailResponse paymentDetailResponse = billPayTransactionSelectService.getBillPaymentDetail(crmId, correlationId, paymentDetailRequest);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(paymentDetailResponse);

        } catch (Exception e) {
            logger.error("Failed to process billPaymentDetail error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }


    @PostMapping(value = "/bill-payment/deep-link-validation")
    public ResponseEntity<TmbOneServiceResponse<DeepLinkResponse>> deepLink(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = "X-CRMID") String crmId,
            @RequestBody DeepLinkRequest deepLinkRequest
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<DeepLinkResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            DeepLinkResponse deepLinkResponse = billPaymentDeepLinkService.deeplinkValidation(deepLinkRequest, crmId, correlationId);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(deepLinkResponse);

        } catch (Exception e) {
            logger.error("Failed to process deppLink error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    public static boolean containsBlacklistChars(String input) {
        if (input == null || input.isEmpty()) {
            return false;
        }
        return Pattern.compile(BLACKLIST).matcher(input).find();
    }

    public static boolean checkBillpayChars(String input) {
        if (input == null || input.isEmpty()) {
            return true;
        }
        return Pattern.compile(REF_ALLOW).matcher(input).matches();
    }

    @LogAround
    @GetMapping({"/bill-payment/payment-transaction-log"})
    @Operation(summary = "Get Payment transaction log list")
    public ResponseEntity<TmbOneServiceResponse<List<PaymentTransactionLog>>> getPaymentTransactionLogList(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @Parameter(description = "Transaction ID", example = "4567")
            @RequestParam(value = "transId", required = false) String transId
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<List<PaymentTransactionLog>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            tmbOneServiceResponse.setData(billPayTransactionSelectService.getPaymentTransactionLogList(correlationId, transId));
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
        } catch (Exception e) {
            logger.error("Failed to process getPaymentTransactionLog, with error : {}", e.getMessage(), e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
