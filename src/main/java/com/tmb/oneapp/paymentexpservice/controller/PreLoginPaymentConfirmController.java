package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.transfer.TransferConfirmResponse;
import com.tmb.oneapp.paymentexpservice.service.BillPayTransactionSelectService;
import com.tmb.oneapp.paymentexpservice.service.CustomerDeviceStatusService;
import com.tmb.oneapp.paymentexpservice.service.EWalletPaymentService;
import com.tmb.oneapp.paymentexpservice.service.TransferOtherBankAndPromptPayConfirmationService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PRE_LOGIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;

@RestController
@RequestMapping("/pre-login")
public class PreLoginPaymentConfirmController {
    private static final TMBLogger<PreLoginPaymentConfirmController> logger = new TMBLogger<>(PreLoginPaymentConfirmController.class);

    private final BillPayTransactionSelectService billPayTransactionSelectService;
    private final CustomerDeviceStatusService customerDeviceStatusService;
    private final EWalletPaymentService eWalletPaymentService;
    private final TransferOtherBankAndPromptPayConfirmationService transferOtherBankAndPromptPayConfirmationService;

    public PreLoginPaymentConfirmController(BillPayTransactionSelectService billPayTransactionSelectService, CustomerDeviceStatusService customerDeviceStatusService, EWalletPaymentService eWalletPaymentService, TransferOtherBankAndPromptPayConfirmationService transferOtherBankAndPromptPayConfirmationService) {
        this.billPayTransactionSelectService = billPayTransactionSelectService;
        this.customerDeviceStatusService = customerDeviceStatusService;
        this.eWalletPaymentService = eWalletPaymentService;
        this.transferOtherBankAndPromptPayConfirmationService = transferOtherBankAndPromptPayConfirmationService;
    }


    @LogAround
    @Operation(summary = "Pre Login Confirm Bill Payment")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/bill-payment/confirm")
    public ResponseEntity<TmbOneServiceResponse<TopUpConfirmResponse>> confirmBillPayment(
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @RequestBody TopUpConfirmRequest topUpConfirmRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);

            headers.set(X_CRMID, crmId);
            headers.set(PRE_LOGIN, "true");
            TopUpConfirmResponse topUpConfirmResponse = billPayTransactionSelectService.confirmBillPayment(crmId, correlationId, topUpConfirmRequest, headers);

            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(topUpConfirmResponse);

        } catch (Exception e) {
            logger.error("Failed to process confirmBillPayment error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Pre Login Confirm e-wallet payment")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S21")
    })
    @PostMapping(value = "/transfer/prompt-pay/confirm")
    public ResponseEntity<TmbOneServiceResponse<TopUpConfirmResponse>> eWalletConfirm(
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492vcbb7609da76", required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbd8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @RequestBody TopUpConfirmRequest topUpConfirmRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
        headers.set(X_CRMID, crmId);
        TmbOneServiceResponse<TopUpConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            TopUpConfirmResponse topUpConfirmResponse = eWalletPaymentService.confirm(
                    topUpConfirmRequest,
                    crmId,
                    correlationId,
                    headers
            );

            tmbOneServiceResponse.setData(topUpConfirmResponse);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            logger.error("Failed to process eWalletConfirm error : {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Pre login Prompt-Pay : Confirm")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/transfer/promptpay-confirm")
    public ResponseEntity<TmbOneServiceResponse<TransferConfirmResponse>> promptPayConfirm(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @RequestBody TopUpConfirmRequest reqBody,
            @RequestHeader HttpHeaders headers) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
        headers.set(X_CRMID, crmId);

        TmbOneServiceResponse<TransferConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            TransferConfirmResponse promptConfirmRes = transferOtherBankAndPromptPayConfirmationService.confirm(reqBody.getTransId(), crmId, correlationId, headers, reqBody.getFrUuid());

            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(promptConfirmRes);

        } catch (Exception e) {
            logger.error("Failed to process promptPay or otherBank confirm error : {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
