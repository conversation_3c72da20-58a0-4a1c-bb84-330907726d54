package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.data.ActivitiesListRequest;
import com.tmb.oneapp.paymentexpservice.data.ActivitiesNotetRequest;
import com.tmb.oneapp.paymentexpservice.data.ActivitiesRequest;
import com.tmb.oneapp.paymentexpservice.data.ActivitiesResponse;
import com.tmb.oneapp.paymentexpservice.data.ActivitiesStatementsData;
import com.tmb.oneapp.paymentexpservice.data.ActivityStatementsData;
import com.tmb.oneapp.paymentexpservice.service.AccountService;
import com.tmb.oneapp.paymentexpservice.service.TransactionActivityService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.netty.util.internal.StringUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@RestController
@Tag(name = "Payment Service Controller for Activity Transactions")
public class TransactionActivityController {

	private static final TMBLogger<TransactionActivityController> logger = new TMBLogger<>(TransactionActivityController.class);
	private final TransactionActivityService transactionActivityService;
	private final AccountService accountService;

	@Autowired
	public TransactionActivityController(TransactionActivityService transactionActivityService, AccountService accountService) {
		super();
		this.transactionActivityService = transactionActivityService;
		this.accountService = accountService;
	}


	/**
	 * This method is using to fetch the account transaction activities
	 * @param correlationId
	 * @param crmId
	 * @param transactionActivityData
	 * @return
	 */
	@Operation(summary = "Fetch Transaction Activities Controller Detail")
	@LogAround
	@PostMapping(value = "/financial/activities", consumes=MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<TmbOneServiceResponse<ActivityStatementsData>> getGetTransactionActivities(
			@Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader("X-Correlation-ID") @Valid String correlationId,
			@Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true) @RequestHeader(name = "X-CRMID", required = true) String crmId,
			@RequestBody ActivitiesListRequest transactionActivityData) {
		logger.info("accounts-service Transaction Acttivities method start Time : {} ", System.currentTimeMillis());
		TmbOneServiceResponse<ActivityStatementsData> oneServiceResponse = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		responseHeaders.set(PaymentServiceConstant.X_CRMID, crmId);
		try {
				long startaccTimeMob = System.nanoTime();
				ResponseCode errorCode = PaymentServiceUtils.validateFieldActivities(transactionActivityData);
				if(null !=  errorCode) {
					oneServiceResponse.setStatus(new TmbStatus(errorCode.getCode(), errorCode.getMessage(),
							errorCode.getService(), errorCode.getMessage()));
					return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
				}
				ActivityStatementsData data = transactionActivityService.getStatement(transactionActivityData, crmId, correlationId);
				oneServiceResponse.setData(data);
				long endaccTimeMob = System.nanoTime();
				long timeaccapsedMoendb = endaccTimeMob - startaccTimeMob;
				logger.info("ETE ====> Fetch Transaction Activities Controller End  : {}", timeaccapsedMoendb);

			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

			logger.info("getGetTransactionActivities method end Time : {} ", System.currentTimeMillis());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (Exception e) {
			logger.error("Unable to fetch data from getGetTransactionActivities  : {}", e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
		}

	}

	/**
	 *
	 * @param crmId
	 * @param activitiesRequestSearch
	 * @return
	 */
	@Operation(summary = "Fetch Transaction Activities Controller Detail")
	@LogAround
	@PostMapping(value = "/financial/activities/search", consumes=MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<TmbOneServiceResponse<ActivitiesStatementsData>> getSearchActivities(
			@Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true) @RequestHeader(name = "X-CRMID", required = true) String crmId,
			@RequestBody ActivitiesRequest activitiesRequestSearch) throws TMBCommonException, ParseException {
		logger.info("getSearchActivities-req ======> {}", activitiesRequestSearch);
		logger.info("finance-service Transaction Activities method start Time : {} ", System.currentTimeMillis());
		TmbOneServiceResponse<ActivitiesStatementsData> oneServiceResponses = new TmbOneServiceResponse<>();
		HttpHeaders responseHeader = new HttpHeaders();
		String uuid = UUID.randomUUID().toString();
		try {
			String acNo = transactionActivityService.validateDecryptAccountNumber(activitiesRequestSearch.getAccountNo());
			logger.info("getSearchActivities-acNo : {}", acNo);
			accountService.validateAccountOwnerByCrmId(uuid, crmId, acNo);

			logger.info("financial search account number decrypted : {}", acNo);
			long startaccTimeMobs = System.nanoTime();
			ActivitiesListRequest transactionActivityETEData = new ActivitiesListRequest();
			transactionActivityETEData.setAccountNo(acNo);
			transactionActivityETEData.setAccountType(activitiesRequestSearch.getAccountType());
			transactionActivityETEData.setLanguage(activitiesRequestSearch.getLanguage());
			transactionActivityETEData.setPage(activitiesRequestSearch.getPage());
			activitiesRequestSearch.setAccountNo(acNo);

			ResponseCode errorCode = PaymentServiceUtils.validateFieldActivitiesSearch(transactionActivityETEData, activitiesRequestSearch);
			if (null != errorCode) {
				oneServiceResponses.setStatus(new TmbStatus(errorCode.getCode(), errorCode.getMessage(),
						errorCode.getService(), errorCode.getMessage()));
				return ResponseEntity.badRequest().headers(responseHeader).body(oneServiceResponses);
			}

			DateFormat format = new SimpleDateFormat("MM/yyyy", Locale.ENGLISH);
			String monthFrom = activitiesRequestSearch.getMonthFrom();
			String monthTo = activitiesRequestSearch.getMonthTo();
			Date today = Calendar.getInstance().getTime();
			String toDate = format.format(today);
			Date todayDate = format.parse(toDate);
			Calendar today2year = Calendar.getInstance();
			today2year.add(Calendar.YEAR, -2);
			Date next2Year = today2year.getTime();
			if (isaSearchError(activitiesRequestSearch, format, monthFrom, todayDate) ||
					isaSearchError2(activitiesRequestSearch, format, monthTo, next2Year)) {
				oneServiceResponses.setStatus(new TmbStatus(ResponseCode.GENERAL_ERROR.getCode(), ResponseCode.GENERAL_ERROR.getMessage(),
						ResponseCode.GENERAL_ERROR.getService(), ResponseCode.GENERAL_ERROR.getMessage()));
				return ResponseEntity.badRequest().headers(responseHeader).body(oneServiceResponses);
			}

			if (!StringUtil.isNullOrEmpty(activitiesRequestSearch.getAmountFrom()) &&
					!StringUtil.isNullOrEmpty(activitiesRequestSearch.getAmountTo()) &&
					new BigDecimal(activitiesRequestSearch.getAmountFrom()).compareTo(new BigDecimal(activitiesRequestSearch.getAmountTo())) > 0) {
				oneServiceResponses.setStatus(new TmbStatus(ResponseCode.GENERAL_ERROR.getCode(), ResponseCode.GENERAL_ERROR.getMessage(),
						ResponseCode.GENERAL_ERROR.getService(), ResponseCode.GENERAL_ERROR.getMessage()));
				return ResponseEntity.badRequest().headers(responseHeader).body(oneServiceResponses);
			}

			CompletableFuture<List<ActivitiesResponse>> dataFromETE = transactionActivityService.eteCall(transactionActivityETEData, activitiesRequestSearch.getMonthFrom(), activitiesRequestSearch.getMonthTo(), crmId);
			ActivitiesStatementsData dataSearch = transactionActivityService.getSearchData(crmId, activitiesRequestSearch, dataFromETE, uuid);
			oneServiceResponses.setData(dataSearch);
			long endaccTimeMobs = System.nanoTime();
			long timeaccapsedMoendbs = endaccTimeMobs - startaccTimeMobs;
			logger.info("ETE ====> Fetch Transaction Activities Controller End  : {}", timeaccapsedMoendbs);

			oneServiceResponses.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

			logger.info("searchTransactionActivities method end Time : {} ", System.currentTimeMillis());
			return ResponseEntity.ok().headers(responseHeader).body(oneServiceResponses);
		}catch(TMBCommonException tem){
			throw tem;
		} catch (Exception e) {
			logger.error("Unable to fetch data from searchTransactionActivities  : {}", e);
			oneServiceResponses.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));

			return ResponseEntity.badRequest().headers(responseHeader).body(oneServiceResponses);
		}

	}

	private boolean isaSearchError2(ActivitiesRequest activitiesRequestSearch, DateFormat format, String monthTo, Date next2Year) throws ParseException {
		return !StringUtil.isNullOrEmpty(activitiesRequestSearch.getMonthTo()) &&
				StringUtil.isNullOrEmpty(activitiesRequestSearch.getMonthFrom()) &&
				next2Year.compareTo(format.parse(monthTo)) > 0;
	}


	private boolean isaSearchError(ActivitiesRequest activitiesRequestSearch, DateFormat format, String monthFrom, Date todayDate) throws ParseException {
		return StringUtil.isNullOrEmpty(activitiesRequestSearch.getMonthTo()) &&
				!StringUtil.isNullOrEmpty(activitiesRequestSearch.getMonthFrom()) &&
				format.parse(monthFrom).compareTo(todayDate) > 0;
	}

	/**
	 * This method is using to Transaction Note Operation ADD, UPDATE, DELETE
	 * @param activitiesNotetRequest
	 * @param crmId
	 *
	 * @return
	 */
	@Operation(summary = "Transaction Note Operation Controller Detail")
	@LogAround
	@PostMapping(value = "/financial/txnmemo", consumes=MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<TmbOneServiceResponse<String>> callTxnNote(
			@Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true) @RequestHeader(name = "X-CRMID", required = true) String crmId,
			@RequestBody ActivitiesNotetRequest activitiesNotetRequest) {
		logger.info("Transaction Note Operation method start Time : {} ", System.currentTimeMillis());
		TmbOneServiceResponse<String> noteServiceResponseNote = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		responseHeaders.set(PaymentServiceConstant.X_CRMID, crmId);
		try {
			long startaccTimeMob = System.nanoTime();
			if (activitiesNotetRequest.getEventId().isEmpty()
					|| !activitiesNotetRequest.getEventId().matches("\\d{20}")) {

				noteServiceResponseNote.setStatus(new TmbStatus(ResponseCode.INVALID_REQUEST.getCode(),
						ResponseCode.INVALID_REQUEST.getMessage(), ResponseCode.INVALID_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(responseHeaders).body(noteServiceResponseNote);
			} else if (activitiesNotetRequest.getEventId() == null || activitiesNotetRequest.getFinTxnMemo() == null) {

				noteServiceResponseNote.setStatus(new TmbStatus(ResponseCode.MISSING_REQUIRED_FIELD.getCode(),
						ResponseCode.MISSING_REQUIRED_FIELD.getMessage(),
						ResponseCode.MISSING_REQUIRED_FIELD.getService()));
				return ResponseEntity.badRequest().headers(responseHeaders).body(noteServiceResponseNote);
			}else{
				logger.info("activity request has event id");
			}

			String data = transactionActivityService.callTxnNote(activitiesNotetRequest, crmId);
			if (data != null && data.equals("1")) {
				noteServiceResponseNote.setData(ResponseCode.SUCCESS.getMessage());
				long endaccTimeMob = System.nanoTime();
				long timeaccapsedMoendb = endaccTimeMob - startaccTimeMob;
				logger.info("DB ====> Transaction Note Operation Contoller End  : {}", timeaccapsedMoendb);

				noteServiceResponseNote
						.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
								ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

				logger.info("callTxnNote method end Time : {} ", System.currentTimeMillis());
				return ResponseEntity.ok().headers(responseHeaders).body(noteServiceResponseNote);

			} else {
				noteServiceResponseNote.setStatus(new TmbStatus(ResponseCode.INVALID_REQUEST.getCode(),
						ResponseCode.INVALID_REQUEST.getMessage(), ResponseCode.INVALID_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(responseHeaders).body(noteServiceResponseNote);
			}

		} catch (Exception e) {
			logger.error("Unable to fetch data from callTxnNote  : {}", e);
			noteServiceResponseNote.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(noteServiceResponseNote);
		}

	}



}
