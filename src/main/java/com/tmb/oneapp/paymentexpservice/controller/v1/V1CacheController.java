package com.tmb.oneapp.paymentexpservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.v1.V1GetCacheTransIdResponse;
import com.tmb.oneapp.paymentexpservice.model.v1.V1PostCacheTransIdRequest;
import com.tmb.oneapp.paymentexpservice.service.v1.V1CacheService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACCEPT_LANGUAGE;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.APP_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_TIMESTAMP;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TRANS_ID;

@RestController
public class V1CacheController {

    public static final TMBLogger<V1CacheController> logger = new TMBLogger<>(V1CacheController.class);
    private final V1CacheService v1CacheService;

    @Autowired
    public V1CacheController(final V1CacheService v1CacheService) {
        this.v1CacheService = v1CacheService;
    }

    @Operation(summary = "Get data from Redis Cache by using {trans-id} as Key")
    @GetMapping("/cache/trans-id")
    public ResponseEntity<TmbOneServiceResponse<V1GetCacheTransIdResponse>> getCacheTransId(
            @Parameter(description = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = TRANS_ID, required = true) @Valid @RequestParam(TRANS_ID) String transId)
            throws TMBCommonException {
        logger.info("Get redis data when key is an trans-id for correlation-id - " + correlationId);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(HEADER_CORRELATION_ID, correlationId);
        responseHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        String value = v1CacheService.getCacheTransId(transId);
        V1GetCacheTransIdResponse data = new V1GetCacheTransIdResponse();
        data.setKey(transId);
        data.setValue(value);

        TmbOneServiceResponse<V1GetCacheTransIdResponse> response = new TmbOneServiceResponse<>();
        response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ""));
        response.setData(data);
        logger.info("get - successfully get data when key is trans-id, key is {} and value is {}", transId, value);

        return ResponseEntity.ok().headers(responseHeaders).body(response);
    }

    @Operation(summary = "Send data to Redis Cache by using {trans-id} as Key")
    @PostMapping(value = "/cache/trans-id", consumes = "application/json", produces = "application/json")
    public ResponseEntity<TmbOneServiceResponse<String>> setCacheTransId(
            @Parameter(description = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader(HEADER_CORRELATION_ID) final String correlationId,
            @Parameter(description = TRANS_ID, required = true) @RequestBody V1PostCacheTransIdRequest request)
            throws Exception {


        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(HEADER_CORRELATION_ID, correlationId);
        responseHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        String value = v1CacheService.setCacheTransId(request.getTransId(), request.getValue());

        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ""));
        logger.info("set - successfully set data when key is trans-id, and value is {}", value);

        return ResponseEntity.ok().headers(responseHeaders).body(response);
    }

    @Operation(summary = "Delete data from Redis Cache by using {trans-id} as Key")
    @DeleteMapping("/cache/trans-id")
    public ResponseEntity<TmbOneServiceResponse<String>> deleteCacheTransId(
            @Parameter(description = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = TRANS_ID, required = true) @Valid @RequestParam(TRANS_ID) String transId)
            throws Exception {
        logger.info("Delete redis data for id - {} key - {}", correlationId, transId);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(HEADER_CORRELATION_ID, correlationId);
        responseHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        v1CacheService.deleteCacheTransId(transId);

        TmbOneServiceResponse<String> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
        logger.info("Delete data successfully, key: {}", transId);

        return ResponseEntity.ok().headers(responseHeaders).body(tmbOneServiceResponse);
    }
}
