package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.model.eslip.ESlipResponse;
import com.tmb.oneapp.paymentexpservice.model.eslip.TopUpBillPayESlipResponse;
import com.tmb.oneapp.paymentexpservice.service.ESlipService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@Tag(name = "E-Slip")
public class ESlipController {
    private static final TMBLogger<ESlipController> logger = new TMBLogger<>(ESlipController.class);
    private final ESlipService eSlipService;

    @Autowired
    public ESlipController(ESlipService eSlipService) {
        this.eSlipService = eSlipService;
    }

    @LogAround
    @Operation(summary = "Get Transfer E-slip")
    @GetMapping(value = "/e-slip/transfer/{refNo}")
    public ResponseEntity<TmbOneServiceResponse<ESlipResponse>> getTransferESlipTransaction(
            @Parameter(description = "CRM ID", example = "001100000000000000000000031310", required = true) @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Parameter(description = "Reference No", example = "202106061800000006", required = true) @PathVariable(value = "refNo") String refNo
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<ESlipResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {

            ESlipResponse response = eSlipService.getTransferESlip(crmId, correlationId, refNo);

            tmbOneServiceResponse.setData(response);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            logger.error("Failed to process getTransferESlipTransaction error : {}", e);

            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Get TopUp E-slip")
    @GetMapping(value = "/e-slip/billpay/{refNo}")
    public ResponseEntity<TmbOneServiceResponse<TopUpBillPayESlipResponse>> getTopUpBillPayESlipTransaction(
            @Parameter(description = "CRM ID", example = "001100000000000000000000031310", required = true) @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Parameter(description = "Reference No", example = "202106061800000006", required = true) @PathVariable(value = "refNo") String refNo
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpBillPayESlipResponse> tmbOneServiceTopUpESlipResponse = new TmbOneServiceResponse<>();

        try {
            TopUpBillPayESlipResponse response = eSlipService.getTopUpESlip(crmId, correlationId, refNo);

            tmbOneServiceTopUpESlipResponse.setData(response);
            tmbOneServiceTopUpESlipResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
        } catch (Exception e) {
            logger.error("Failed to process getTopUpBillPayESlipTransaction error : {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceTopUpESlipResponse);
    }
}
