package com.tmb.oneapp.paymentexpservice.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.DeleteRecipientResponse;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.GetRecipientResponse;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.InternationalCountryResponse;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.RecipientInformationRequest;
import com.tmb.oneapp.paymentexpservice.model.internationaltransfer.RecipientSaveResponse;
import com.tmb.oneapp.paymentexpservice.service.SetUpRecipientService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * SetUpRecipientController request mapping will handle apis call
 * respective method to get Recipient
 */
@RestController
@Tag(name = "API Set up Recipient Information")
public class SetUpRecipientController {
    private static final TMBLogger<SetUpRecipientController> logger = new TMBLogger<>(SetUpRecipientController.class);
    private final SetUpRecipientService setUpRecipientService;


    @Autowired
    public SetUpRecipientController(SetUpRecipientService setUpRecipientService) {
        this.setUpRecipientService = setUpRecipientService;
    }


    /**
     * Method responsible for controlling API for get Recipient
     */
    @Operation(summary = "Get Recipient List")
    @LogAround
    @PostMapping(value = "/ott/recipient")
    public ResponseEntity<TmbOneServiceResponse<List<GetRecipientResponse>>> getRecipientList(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @SuppressWarnings("unused") @RequestHeader("X-Correlation-ID") @Valid @RequestHeaderNonNull String correlationId,
            @Parameter(description = "X-CRMID", example = "001100000000000000000000086006", required = true)
            @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmID) {

        logger.info("SetUpRecipientController  getRecipientList method start Time : {} ", System.currentTimeMillis());
        TmbOneServiceResponse<List<GetRecipientResponse>> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {
            final List<GetRecipientResponse> output = setUpRecipientService.getRecipient(crmID);
            response.setData(output);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(responseHeaders).body(response);

        } catch (Exception e) {
            logger.error("Unable to fetch International Address : {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

            return ResponseEntity.badRequest().headers(responseHeaders).body(response);
        }
    }


    @Operation(summary = "Delete Recipient")
    @LogAround
    @PostMapping(value = "/ott/recipient/delete")
    public ResponseEntity<DeleteRecipientResponse> deleteRecipient(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @Valid @RequestHeaderNonNull String correlationId,
            @Parameter(description = "X-CRMID", example = "001100000000000000000000086006", required = true)
            @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmID,
            @RequestBody RecipientInformationRequest inputReq,
            @RequestHeader HttpHeaders header) {

        logger.info("SetUpRecipientController deleteRecipient method start Time : {} ", System.currentTimeMillis());

        DeleteRecipientResponse response = new DeleteRecipientResponse();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {
            setUpRecipientService.deleteRecipient(inputReq, crmID, correlationId, header);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(responseHeaders).body(response);

        } catch (Exception e) {
            logger.error("Unable to fetch International Address : {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

            return ResponseEntity.badRequest().headers(responseHeaders).body(response);
        }
    }

    /**
     * Method responsible for controlling API for add recipient
     */
    @Operation(summary = "Add recipient Information")
    @LogAround
    @PostMapping(value = "ott/recipient/save")
    public ResponseEntity<TmbOneServiceResponse<RecipientSaveResponse>> addRecipient(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "X-CRMID", example = "001100000000000000000000086006", required = true)
            @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmID,
            @RequestBody RecipientInformationRequest recipientInformationRequest,
            @RequestHeader HttpHeaders header) throws JsonProcessingException {

        logger.info("SetUpRecipientController  add recipient start Time : {} ", System.currentTimeMillis());
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));


        TmbOneServiceResponse<RecipientSaveResponse> response = new TmbOneServiceResponse<>();
        try {
            RecipientSaveResponse result = setUpRecipientService
                    .addRecipient(recipientInformationRequest, crmID, correlationId, header);
            response.setData(result);

            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(httpHeaders).body(response);

        } catch (TMBCommonException e) {
            logger.error("Unable to add recipient : {} ", e);
            if (ResponseCode.MANDATORY_FIELD.getCode().equalsIgnoreCase(e.getErrorCode())
                    || ResponseCode.ALREADY_EXIST.getCode().equalsIgnoreCase(e.getErrorCode())){
                response.setStatus(getStatus(e));
            } else {
                response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(),
                        ResponseCode.FAILED.getMessage(),
                        ResponseCode.FAILED.getService(),
                        ResponseCode.FAILED.getDescription()));
            }
            RecipientSaveResponse saveResponseError =
                    getRecipientSaveResponseError(recipientInformationRequest, e);
            response.setData(saveResponseError);
            return ResponseEntity.badRequest().headers(httpHeaders).body(response);
        }
    }

    @NotNull
    private static TmbStatus getStatus(TMBCommonException e) {
        return new TmbStatus(e.getErrorCode(), e.getMessage(), e.getService(), e.getErrorMessage());
    }

    private static RecipientSaveResponse getRecipientSaveResponseError(RecipientInformationRequest request,
                                                                       TMBCommonException e) {
        return RecipientSaveResponse.builder()
                .uploadStatus(Optional.ofNullable(e.getErrorMessage())
                        .orElse(ResponseCode.MANDATORY_FIELD.getMessage()))
                .beneId(request.getBeneficiaryId())
                .build();
    }


    /**
     * Method responsible for controlling API for get all country
     */
    @Operation(summary = "Get All Country list")
    @LogAround
    @GetMapping(value = "/ott/country")
    public ResponseEntity<TmbOneServiceResponse<InternationalCountryResponse>> getCountry(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @Valid @RequestHeaderNonNull String correlationId) {

        logger.info("Get Country method start Time : {} ", System.currentTimeMillis());
        TmbOneServiceResponse<InternationalCountryResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {

            final InternationalCountryResponse output = setUpRecipientService.getCountry(correlationId);
            response.setData(output);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(responseHeaders).body(response);

        } catch (Exception e) {
            logger.error("Unable to fetch Country : {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

            return ResponseEntity.badRequest().headers(responseHeaders).body(response);
        }
    }


    /**
     * Method responsible for controlling API for get popular country
     */
    @Operation(summary = "Get Popular country list")
    @LogAround
    @GetMapping(value = "/ott/country/popular")
    public ResponseEntity<TmbOneServiceResponse<InternationalCountryResponse>> getPopularCountry(
            @RequestHeader(value = "X-Correlation-ID")
            @Valid @RequestHeaderNonNull String correlationId) {

        logger.info("Get Popular Country method start Time : {} ", System.currentTimeMillis());
        TmbOneServiceResponse<InternationalCountryResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {

            final InternationalCountryResponse output = setUpRecipientService.getPopularCountry(correlationId);
            response.setData(output);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(responseHeaders).body(response);

        } catch (Exception e) {
            logger.error("Unable to fetch Popular Country : {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

            return ResponseEntity.badRequest().headers(responseHeaders).body(response);
        }
    }

}
