package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.model.transactionverify.TransactionVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.transactionverify.TransactionVerifyResponse;
import com.tmb.oneapp.paymentexpservice.service.CustomerDeviceStatusService;
import com.tmb.oneapp.paymentexpservice.service.TransactionVerifyService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;

@RestController
@RequestMapping("/pre-login")
public class TransactionVerifyController {
    private static final TMBLogger<TransactionVerifyController> logger = new TMBLogger<>(TransactionVerifyController.class);

    private final CustomerDeviceStatusService customerDeviceStatusService;
    private final TransactionVerifyService transactionVerifyService;


    public TransactionVerifyController(CustomerDeviceStatusService customerDeviceStatusService, TransactionVerifyService transactionVerifyService) {
        this.customerDeviceStatusService = customerDeviceStatusService;
        this.transactionVerifyService = transactionVerifyService;
    }

    @PostMapping(value = "/verify-slip")
    public ResponseEntity<TmbOneServiceResponse<TransactionVerifyResponse>> transactionVerify(
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @RequestBody TransactionVerifyRequest transactionVerifyRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TransactionVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);

            headers.set(X_CRMID, crmId);
            TransactionVerifyResponse transactionVerifyResponse = transactionVerifyService.verify(headers, transactionVerifyRequest);

            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(transactionVerifyResponse);

        } catch (Exception e) {
            logger.error("Failed to process transactionVerify error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
