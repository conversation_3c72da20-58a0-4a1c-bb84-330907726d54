package com.tmb.oneapp.paymentexpservice.controller.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyResponse;
import com.tmb.oneapp.paymentexpservice.service.CustomerDeviceStatusService;
import com.tmb.oneapp.paymentexpservice.service.EWalletPaymentService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PRE_LOGIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;

@RestController
@RequestMapping("/pre-login")
@Tag(name = "PreLogin TransferPromptPay V2 Controller")
public class V2PreLoginEWalletPaymentController {
    private static final TMBLogger<V2PreLoginEWalletPaymentController> logger = new TMBLogger<>(V2PreLoginEWalletPaymentController.class);

    private final CustomerDeviceStatusService customerDeviceStatusService;
    private final EWalletPaymentService eWalletPaymentService;

    public V2PreLoginEWalletPaymentController(CustomerDeviceStatusService customerDeviceStatusService,
                                              EWalletPaymentService eWalletPaymentService) {
        this.customerDeviceStatusService = customerDeviceStatusService;
        this.eWalletPaymentService = eWalletPaymentService;
    }


    @Operation(summary = "Pre Login Prompt-Pay validate")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @LogAround
    @PostMapping(value = "/transfer/prompt-pay/validate")
    public ResponseEntity<TmbOneServiceResponse<TopUpVerifyResponse>> eWalletValidate(
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Valid @RequestBody TopUpVerifyRequest promptPayRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
            headers.set(PRE_LOGIN, "true");
            headers.set(X_CRMID, crmId);
            TopUpVerifyResponse promptPayVerifyResponse = eWalletPaymentService.validate(crmId, correlationId, promptPayRequest, headers);
            tmbOneServiceResponse.setData(promptPayVerifyResponse);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            logger.error("Failed to process eWalletValidate error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @Operation(summary = "Pre Login Confirm e-wallet payment")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S21")
    })
    @LogAround
    @PostMapping(value = "/transfer/prompt-pay/confirm")
    public ResponseEntity<TmbOneServiceResponse<TopUpConfirmResponse>> eWalletConfirm(
            @Parameter(description = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492vcbb7609da76", required = true) @Valid @RequestHeader("Device-Id") String deviceId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbd8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @RequestBody TopUpConfirmRequest topUpConfirmRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
        headers.set(X_CRMID, crmId);
        TmbOneServiceResponse<TopUpConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            TopUpConfirmResponse topUpConfirmResponse = eWalletPaymentService.confirm(
                    topUpConfirmRequest,
                    crmId,
                    correlationId,
                    headers
            );

            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(topUpConfirmResponse);

        } catch (Exception e) {
            logger.error("Failed to process eWalletConfirm error : {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
