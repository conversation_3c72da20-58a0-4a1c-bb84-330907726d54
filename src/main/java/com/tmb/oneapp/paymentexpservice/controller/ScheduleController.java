package com.tmb.oneapp.paymentexpservice.controller;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.model.schedule.request.ScheduleCancellationRequest;
import com.tmb.oneapp.paymentexpservice.model.schedule.request.ScheduleConfirmationRequest;
import com.tmb.oneapp.paymentexpservice.model.schedule.request.ScheduleValidationTopUpRequest;
import com.tmb.oneapp.paymentexpservice.model.schedule.request.ScheduleValidationTransferRequest;
import com.tmb.oneapp.paymentexpservice.model.schedule.response.ScheduleConfirmationResponse;
import com.tmb.oneapp.paymentexpservice.model.schedule.response.ScheduleResponse;
import com.tmb.oneapp.paymentexpservice.model.schedule.response.ScheduleValidationTopUpResponse;
import com.tmb.oneapp.paymentexpservice.model.schedule.response.ScheduleValidationTransferResponse;
import com.tmb.oneapp.paymentexpservice.service.ScheduleBillPaySelectionValidate;
import com.tmb.oneapp.paymentexpservice.service.ScheduleCancellationService;
import com.tmb.oneapp.paymentexpservice.service.ScheduleConfirmationService;
import com.tmb.oneapp.paymentexpservice.service.ScheduleInquiriesService;
import com.tmb.oneapp.paymentexpservice.service.ScheduleTopUpValidationService;
import com.tmb.oneapp.paymentexpservice.service.ScheduleTransferSelectionService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;


@RestController
public class ScheduleController {
    private static final TMBLogger<ScheduleController> logger = new TMBLogger<>(ScheduleController.class);
    public static final String TRANSFER_SCHEDULE_MODULE = "transfer";
    public static final String TOP_UP_SCHEDULE_MODULE = "topup";
    public static final String BILL_PAY_SCHEDULE_MODULE = "billpay";
    public static final String TRANSFER_SCHEDULE_MODULE_PIN = "Schedule-Transfer";
    public static final String TOP_UP_SCHEDULE_MODULE_PIN = "Schedule-Top Up";
    public static final String BILL_PAY_SCHEDULE_MODULE_PIN = "Schedule-Bill Payment";

    private final ScheduleCancellationService scheduleCancellationService;
    private final ScheduleInquiriesService scheduleInquiriesService;
    private final ScheduleTransferSelectionService scheduleTransferSelectionService;
    private final ScheduleConfirmationService scheduleConfirmationService;
    private final ScheduleBillPaySelectionValidate scheduleBillPaySelectionValidate;
    private final ScheduleTopUpValidationService scheduleTopUpValidationService;

    public ScheduleController(
            ScheduleCancellationService scheduleCancellationService,
            ScheduleInquiriesService scheduleInquiriesService,
            ScheduleTransferSelectionService scheduleTransferSelectionService,
            ScheduleConfirmationService scheduleConfirmationService,
            ScheduleBillPaySelectionValidate scheduleBillPaySelectionValidate,
            ScheduleTopUpValidationService scheduleTopUpValidationService) {
        this.scheduleCancellationService = scheduleCancellationService;
        this.scheduleInquiriesService = scheduleInquiriesService;
        this.scheduleTransferSelectionService = scheduleTransferSelectionService;
        this.scheduleConfirmationService = scheduleConfirmationService;
        this.scheduleBillPaySelectionValidate = scheduleBillPaySelectionValidate;

        this.scheduleTopUpValidationService = scheduleTopUpValidationService;
    }

    @LogAround
    @Operation(summary = "Get All Schedule by type")
    @GetMapping(value = "/schedule")
    public ResponseEntity<TmbOneServiceResponse<List<ScheduleResponse>>> getSchedule(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true) @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "type", example = "topup/billpay/transfer") @RequestParam("type") String type) throws TMBCommonException, TMBCustomCommonExceptionWithResponse, JsonProcessingException {
        TmbOneServiceResponse<List<ScheduleResponse>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            List<ScheduleResponse> scheduleResponses = scheduleInquiriesService.getScheduleByType(crmId, correlationId, type);
            tmbOneServiceResponse.setData(scheduleResponses);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
        } catch (Exception e) {
            logger.error("Failed to process getSchedule error : {}");
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @DeleteMapping(value = "/schedule")
    public ResponseEntity<TmbOneServiceResponse<String>> cancelPaymentSchedule(
            @RequestHeader HttpHeaders header,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true) @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @RequestBody ScheduleCancellationRequest scheduleCancellationRequest) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        try {
            scheduleCancellationService.cancelPaymentSchedule(header, correlationId, crmId, scheduleCancellationRequest);
            response.setStatus(PaymentServiceUtils.getResponseSuccess());
            response.setData(null);
        } catch (Exception e) {
            logger.error("Failed to process cancelPaymentSchedule error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "Schedule of All Transfer : Validation")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/schedule/transfer/validation")
    public ResponseEntity<TmbOneServiceResponse<ScheduleValidationTransferResponse>> scheduleValidationOfTransfer(
            @RequestBody ScheduleValidationTransferRequest request,
            @RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<ScheduleValidationTransferResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            ScheduleValidationTransferResponse response = scheduleTransferSelectionService.validateSelection(request, header);
            tmbOneServiceResponse.setData(response);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
        } catch (Exception e) {
            logger.error("Failed to process scheduleValidationOfTransfer error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Schedule of All Transfer : Confirm")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628cbc8da"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/schedule/{module}/confirm")
    public ResponseEntity<TmbOneServiceResponse<ScheduleConfirmationResponse>> scheduleConfirm(
            @PathVariable String module,
            @RequestBody ScheduleConfirmationRequest request,
            @RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<ScheduleConfirmationResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            if (!module.equals(TRANSFER_SCHEDULE_MODULE) && !module.equals(TOP_UP_SCHEDULE_MODULE) && !module.equals(BILL_PAY_SCHEDULE_MODULE)) {
                return ResponseEntity.badRequest().headers(header).body(tmbOneServiceResponse);
            }
            ScheduleConfirmationResponse response = scheduleConfirmationService.confirm(request, header, module);
            tmbOneServiceResponse.setData(response);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
        } catch (Exception e) {
            logger.error("Failed to process scheduleConfirmOfTransfer error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Schedule of All TopUp : Validation")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f77-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006434675"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S21")
    })
    @PostMapping(value = "/schedule/topup/validation")
    public ResponseEntity<TmbOneServiceResponse<ScheduleValidationTopUpResponse>> scheduleValidationOfTopUp(
            @RequestBody ScheduleValidationTopUpRequest request,
            @RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<ScheduleValidationTopUpResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            ScheduleValidationTopUpResponse response = scheduleTopUpValidationService.validate(request, header);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(response);

        } catch (Exception e) {
            logger.error("Failed to process scheduleValidationOfTopUp error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Schedule of All Bill Pay : Validation")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4b89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006564675"),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/schedule/billpay/validation")
    public ResponseEntity<TmbOneServiceResponse<ScheduleValidationTopUpResponse>> scheduleValidationOfBillPay(
            @RequestBody @Valid ScheduleValidationTopUpRequest request,
            @RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<ScheduleValidationTopUpResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            ScheduleValidationTopUpResponse response = scheduleBillPaySelectionValidate.verifyBillPaymentAddSchedule(header, request);
            tmbOneServiceResponse.setData(response);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            logger.error("Failed to process scheduleValidationOfBillPay error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
