package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.ImgUrl;
import com.tmb.oneapp.paymentexpservice.model.ProductBriefResponse;
import com.tmb.oneapp.paymentexpservice.model.StatusCustomerRequest;
import com.tmb.oneapp.paymentexpservice.model.TermAndConditionResponse;
import com.tmb.oneapp.paymentexpservice.service.ProductBriefService;
import feign.FeignException;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.sql.SQLException;


/**
 * BankInfoController request mapping will handle apis call and then navigate to
 * respective method to get Bank Information
 */
@RestController
@AllArgsConstructor
@Tag(name = "ProductBrief , Terms and Condition")
public class ProductBriefController {
    private static final TMBLogger<ProductBriefController> logger = new TMBLogger<>(ProductBriefController.class);
    private final ProductBriefService productBriefService;

    /**
     * Method responsible for controlling API for Product Brief
     *
     * @param correlationId
     * @return
     */
    @Operation(summary = "Get IMG productBrief Information")
    @LogAround
    @GetMapping(value = "ott/productbrief")
    public ResponseEntity<TmbOneServiceResponse<ImgUrl>> getProductBrief(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId) {

        logger.info("ott/productbrief  get data method start Time : {} ", System.currentTimeMillis());
        HttpHeaders httpHeaders = new HttpHeaders();
        TmbOneServiceResponse<ImgUrl> response = new TmbOneServiceResponse<>();
        try {
            var imgURL = productBriefService.getImgURL(correlationId);

            response.setData(imgURL);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(httpHeaders).body(response);

        } catch (FeignException e) {
            logger.error("Unable to fetch productbrief : {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));
            return ResponseEntity.badRequest().headers(httpHeaders).body(response);
        }
    }

    /**
     * Method responsible for controlling API for Check Flag
     *
     * @param correlationId
     * @param crmId
     * @return
     */
    @Operation(summary = "Validate screen  Terms&Con or Address or Home")
    @LogAround
    @GetMapping(value = "ott/terms-and-con/validation")
    public ResponseEntity<TmbOneServiceResponse<ProductBriefResponse>> validateScreen(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true)
            @RequestHeader(name = "X-CRMID") String crmId
            ){
        logger.info("ott/terms-and-con/validation check flag method start Time : {} ", System.currentTimeMillis());
        HttpHeaders httpHeaders = new HttpHeaders();
        TmbOneServiceResponse<ProductBriefResponse> response = new TmbOneServiceResponse<>();
        try {
            ProductBriefResponse screenName = productBriefService.checkFlagTCAndAddress(crmId,correlationId);
            response.setData(screenName);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(httpHeaders).body(response);

        } catch (FeignException | TMBCommonException | SQLException e) {
            logger.error("Unable to validate screen : {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));
            return ResponseEntity.badRequest().headers(httpHeaders).body(response);
        }
    }

    /**
     * Method responsible for controlling API for T&C
     *
     * @param correlationId
     * @return
     */
    @Operation(summary = "Get terms and con details")
    @LogAround
    @GetMapping(value = "ott/terms-and-con")
    public ResponseEntity<TmbOneServiceResponse<TermAndConditionResponse.TermAndConditions>> getTermsAndCon(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId) {
        logger.info("ott/terms-and-con  get data method start Time : {} ", System.currentTimeMillis());
        HttpHeaders httpHeaders = new HttpHeaders();
        TmbOneServiceResponse<TermAndConditionResponse.TermAndConditions> response = new TmbOneServiceResponse<>();
        try {
            var tcInfo= productBriefService.getTermsAndCon(correlationId);
            response.setData(tcInfo);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(httpHeaders).body(response);

        } catch (FeignException e) {
            logger.error("Unable to terms and condition : {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));
            return ResponseEntity.badRequest().headers(httpHeaders).body(response);
        }
    }
    /**
     * Method responsible for controlling API for T&C
     *
     * @param crmId
     * @return
     */
    @Operation(summary = "update status")
    @LogAround
    @PostMapping(value = "ott/terms-and-con/update")
    public ResponseEntity<TmbOneServiceResponse<String>> updateStatusTC(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true)
            @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Valid @RequestBody StatusCustomerRequest statusCustomerRequest
    )
    {
        logger.info("ott/terms-and-con/update get data method start Time : {} ", System.currentTimeMillis());
        HttpHeaders httpHeaders = new HttpHeaders();
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        try {
            productBriefService.updateStatusUser(crmId, statusCustomerRequest.getTncVersion()
                    , statusCustomerRequest.getUserExistFlag(), correlationId);

            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
            return ResponseEntity.ok().headers(httpHeaders).body(response);

        } catch (Exception e) {
            logger.error("Unable to update status terms and con : {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));
            return ResponseEntity.badRequest().headers(httpHeaders).body(response);
        }
    }

}
