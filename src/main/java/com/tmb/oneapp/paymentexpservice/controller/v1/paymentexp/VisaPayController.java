package com.tmb.oneapp.paymentexpservice.controller.v1.paymentexp;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayVerifyResponse;
import com.tmb.oneapp.paymentexpservice.service.CustomerDeviceStatusService;
import com.tmb.oneapp.paymentexpservice.service.v1.payment.VisaPayService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import static com.tmb.oneapp.paymentexpservice.constant.InternationalTransferConstant.FIN_FAILED_STATUS;
import static com.tmb.oneapp.paymentexpservice.constant.InternationalTransferConstant.FIN_SUBMITTED_STATUS;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.FINANCIAL_TRANSACTION_STATUS;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PRE_LOGIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;

@RestController
@RequiredArgsConstructor
@RequestMapping("/visa-pay")
public class VisaPayController {

    private static final TMBLogger<VisaPayController> logger = new TMBLogger<>(VisaPayController.class);

    private final CustomerDeviceStatusService customerDeviceStatusService;
    private final VisaPayService visaPayService;

    @PostMapping(value = "/validation")
    @Operation(summary = "Verify Visa Payment")
    @Parameter(name = "X-CRMID", description = "CRM ID",
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675")
    @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da")
    @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.HEADER)
    @Parameter(name = "OS-Version", description = "OS-Version", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0")
    @Parameter(name = "Channel", description = "Channel", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb")
    @Parameter(name = "App-Version", description = "App-Version", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0")
    @Parameter(name = "Device-Id", description = "Device-Id",
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a")
    @Parameter(name = "Device-Model", description = "Device-Model", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    public ResponseEntity<TmbOneServiceResponse<VisaQRPayVerifyResponse>> validate(
            @Parameter(description = "CRM ID", example = "001100000000000000000001184383", required = true)
            @Valid @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Parameter(description = "Device ID",
                    example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76")
            @Valid @RequestHeader(name = DEVICE_ID, required = false) String deviceId,
            @RequestBody VisaQRPayVerifyRequest visaQRPayVerifyRequest,
            @RequestHeader HttpHeaders headers)
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<VisaQRPayVerifyResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            if (StringUtils.isBlank(crmId) && StringUtils.isNotBlank(deviceId)) {
                crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
                headers.set(X_CRMID, crmId);
                headers.set(PRE_LOGIN, "true");
            }
            VisaQRPayVerifyResponse response = visaPayService.validate
                    (correlationId, crmId, headers, visaQRPayVerifyRequest);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(response);
        } catch (Exception e) {
            logger.error("Failed to validate CASA error: {}", e.getMessage(), e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @PostMapping(value = "/confirm")
    @Operation(summary = "Confirm Visa Payment")
    @Parameter(name = "X-CRMID", description = "CRM ID",
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675")
    @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da")
    @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.HEADER)
    @Parameter(name = "OS-Version", description = "OS-Version", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0")
    @Parameter(name = "Channel", description = "Channel", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb")
    @Parameter(name = "App-Version", description = "App-Version", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0")
    @Parameter(name = "Device-Id", description = "Device-Id",
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a")
    @Parameter(name = "Device-Model", description = "Device-Model", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    public ResponseEntity<TmbOneServiceResponse<TopUpConfirmResponse>> confirm(
            @Parameter(description = "CRM ID", example = "001100000000000000000025536284", required = true)
            @Valid @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @Parameter(description = "Device ID",
                    example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76")
            @Valid @RequestHeader(name = DEVICE_ID, required = false) String deviceId,
            @RequestBody VisaQRPayConfirmRequest visaQRPayConfirmRequest,
            @RequestHeader HttpHeaders headers)
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TopUpConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        VisaQRPayConfirmResponse response = new VisaQRPayConfirmResponse();
        try {
            logger.info(" visa qr confirm start");

            if (StringUtils.isBlank(crmId) && StringUtils.isNotBlank(deviceId)) {
                crmId = customerDeviceStatusService.getCrmIdFromDeviceId(deviceId);
                headers.set(X_CRMID, crmId);
                headers.set(PRE_LOGIN, "true");
            }

            response = visaPayService.confirm(crmId, correlationId, response, visaQRPayConfirmRequest);

            AtomicInteger retryCount = new AtomicInteger(0);
            String status = visaPayService.pullingResult
                    (correlationId, response.getReferenceID(), retryCount);
            logger.info("Pull visa result status: {}, referenceId: {}",
                    status, response.getReferenceID());
            if ("0".equals(Objects.requireNonNull(status))) {
                response.setStatus(FINANCIAL_TRANSACTION_STATUS);
                TopUpConfirmResponse result = VisaPayService.buildConfirmResponse(response);
                tmbOneServiceResponse.setData(result);
                tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
                        ResponseCode.SUCCESS.getMessage(),
                        ResponseCode.SUCCESS.getService(),
                        ResponseCode.SUCCESS.getDescription()
                ));
            } else {
                if ("TIMEOUT".equals(status)) {
                    response.setStatus(FIN_SUBMITTED_STATUS);
                    throw PaymentServiceUtils.failException(ResponseCode.QR_VISA_ERR_94102);
                } else {
                    response.setStatus(FIN_FAILED_STATUS);
                    throw PaymentServiceUtils.failException(ResponseCode.FAILED);
                }
            }

        } catch (TMBCommonException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Failed to confirm Visa error: {}", e.getMessage(), e);
            PaymentServiceUtils.handleException(e);
        } finally {
            visaPayService.postConfirmProcess(correlationId, crmId, response, headers);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

}
