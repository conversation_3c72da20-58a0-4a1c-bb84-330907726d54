package com.tmb.oneapp.paymentexpservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.AccountBalanceTopUpAndBillRequest;
import com.tmb.oneapp.paymentexpservice.model.AccountBalanceTopUpAndBillResponse;
import com.tmb.oneapp.paymentexpservice.model.v1.V1AccountBillpayResponse;
import com.tmb.oneapp.paymentexpservice.service.v1.AccountBillPayRequest;
import com.tmb.oneapp.paymentexpservice.service.v1.V1AccountBalanceService;
import com.tmb.oneapp.paymentexpservice.service.v1.V1AccountBillPayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Tag(name = "V1AccountController : Get account bill pay and top-up")
@RequestMapping("/accounts")
public class V1AccountsController {
    private final V1AccountBillPayService v1AccountBillPayService;
    private final V1AccountBalanceService v1AccountBalanceService;

    public V1AccountsController(V1AccountBillPayService v1AccountBillPayService, V1AccountBalanceService v1AccountBalanceService) {
        this.v1AccountBillPayService = v1AccountBillPayService;
        this.v1AccountBalanceService = v1AccountBalanceService;
    }


    @LogAround
    @Operation(summary = "Get available balance of Account Bill and Top up")
    @PostMapping(value = "/available-balance", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<AccountBalanceTopUpAndBillResponse>> getAccountBalanceTopUpAndBill(
            @Parameter(name = "CRM ID", example = "001100000000000000000002184383", required = true) @Valid @RequestHeader(name = PaymentServiceConstant.X_CRMID, required = false) String crmId,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a79-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = PaymentServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Parameter(name = "Device ID", required = true)  @RequestHeader(name = PaymentServiceConstant.DEVICE_ID) String deviceId,
            @Parameter(name = "App Version", required = true) @Valid @RequestHeader(name = PaymentServiceConstant.APP_VERSION) String appVersion,
            @Parameter(name = "Device Model", required = true) @RequestHeader(name = PaymentServiceConstant.DEVICE_MODEL) String deviceModel,
            @Parameter(name = "Timestamp", required = true) @RequestHeader(name = PaymentServiceConstant.HEADER_TIMESTAMP) String timestamp,
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody AccountBalanceTopUpAndBillRequest request) throws TMBCommonException {

        AccountBalanceTopUpAndBillResponse data = v1AccountBalanceService.getAccountBalance(request.getAccountNumber(), headers);

        TmbOneServiceResponse<AccountBalanceTopUpAndBillResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(getResponseSuccess());
        tmbOneServiceResponse.setData(data);
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Get Bill pay account")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8ta"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2821b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S22")
    })
    @PostMapping(value = "/billpay", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<V1AccountBillpayResponse>> getBillPayAccount(
            @Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestHeader HttpHeaders headers,
            @RequestBody AccountBillPayRequest request)
            throws TMBCommonException {

        V1AccountBillpayResponse data = v1AccountBillPayService.getAccountBillPay(headers, request);

        TmbOneServiceResponse<V1AccountBillpayResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(getResponseSuccess());
        tmbOneServiceResponse.setData(data);
        return ResponseEntity.ok(tmbOneServiceResponse);
    }


    private TmbStatus getResponseSuccess() {
        return new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(), null);
    }

}
