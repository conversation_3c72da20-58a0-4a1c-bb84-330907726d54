package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.ncbbillpaymentconfirm.NcbBillPaymentConfirmBodyRequest;
import com.tmb.oneapp.paymentexpservice.model.ncbbillpaymentconfirm.NcbBillPaymentConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.ncbbillpaymentvalidation.NcbBillPaymentValidationBodyRequest;
import com.tmb.oneapp.paymentexpservice.model.ncbbillpaymentvalidation.NcbBillPaymentValidationResponse;
import com.tmb.oneapp.paymentexpservice.service.NcbBillPaymentService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * NcbBillPaymentController request mapping will handle apis call and then navigate to
 * respective method to get NCB Bill Payment Information
 *
 */
@RestController
@Tag(name = "API To Fetch NCB Bill Payment Information")
public class NcbBillPaymentController {
	private static final TMBLogger<NcbBillPaymentController> logger = new TMBLogger<>(NcbBillPaymentController.class);
	private final NcbBillPaymentService ncbBillPaymentService;

	/**
	 * Constructor
	 *
	 * @param ncbBillPaymentService NcbBillPaymentValidationService
	 */
	@Autowired
	public NcbBillPaymentController(NcbBillPaymentService ncbBillPaymentService) {
		this.ncbBillPaymentService = ncbBillPaymentService;
	}

	/**
	 * Method responsible for controlling API for NCB Bill Payment Validation
	 *
	 * @param correlationId String
	 * @param ncbBillPaymentValidationBodyRequest NcbBillPaymentValidationBodyRequest
	 * @return ResponseEntity<TmbOneServiceResponse<NcbBillPaymentValidationResponse>>
	 */
	@Operation(summary = "Post NCB Bill Payment Validation Information")
	@LogAround
	@PostMapping(value = "/bill-payment/NCB/validation")
	public ResponseEntity<TmbOneServiceResponse<NcbBillPaymentValidationResponse>> getNcbBillPaymentValidation(
			@Parameter(description = "Correlation ID", example = "ae40d276-35f4-4512-b010-7bde6a32eb8d", required = true) @Valid @RequestHeader(PaymentServiceConstant.HEADER_CORRELATION_ID) String correlationId,
			@Parameter(description = "X-CRMID", example = "001100000000000000000001184383", required = true) @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmID,
			@RequestBody @Valid NcbBillPaymentValidationBodyRequest ncbBillPaymentValidationBodyRequest) {

		TmbOneServiceResponse<NcbBillPaymentValidationResponse> ncbBillPaymentConfirmResponseTmb = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();

		try {
			final NcbBillPaymentValidationResponse ncbBillPaymentValidationResponse = ncbBillPaymentService.getNcbBillPaymentValidation(
					correlationId,
					crmID,
					ncbBillPaymentValidationBodyRequest
			);

			if(ncbBillPaymentValidationResponse.getAppId() == null) {
				ncbBillPaymentValidationResponse.setStatus("exceed_daily_limit");
			} else {
				ncbBillPaymentValidationResponse.setStatus("success");
			}

			ncbBillPaymentConfirmResponseTmb.setData(ncbBillPaymentValidationResponse);

			ncbBillPaymentConfirmResponseTmb 
					.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), 
							ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription())); 
			return ResponseEntity.ok().headers(responseHeaders).body(ncbBillPaymentConfirmResponseTmb); 
		} catch (Exception e) { 
			logger.error("Unable to getNcbBillPaymentValidation : {}", e); 
			ncbBillPaymentConfirmResponseTmb.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), 
					ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService())); 
			return ResponseEntity.badRequest().headers(TMBUtils.getResponseHeaders()).body(ncbBillPaymentConfirmResponseTmb); 
		} 
	}

	/**
	 * Method responsible for controlling API for NCB Bill Payment Confirm
	 *
	 * @param correlationId String
	 * @param ncbBillPaymentConfirmBodyRequest NcbBillPaymentConfirmBodyRequest
	 * @return ResponseEntity<TmbOneServiceResponse<NcbBillPaymentConfirmResponse>>
	 */
	@Operation(summary = "Post NCB Bill Payment Confirm Information")
	@LogAround
	@PostMapping(value = "/bill-payment/NCB/confirm")
	public ResponseEntity<TmbOneServiceResponse<NcbBillPaymentConfirmResponse>> getNcbBillPaymentConfirm(
			@Parameter(description = "Correlation ID", example = "ae40d276-35f4-4512-b010-7bde6a32eb8d", required = true) @Valid @RequestHeader(PaymentServiceConstant.HEADER_CORRELATION_ID) String correlationId,
			@Parameter(description = "X-CRMID", example = "001100000000000000000001184383", required = true) @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmID,
			@RequestBody @Valid NcbBillPaymentConfirmBodyRequest ncbBillPaymentConfirmBodyRequest) {

		TmbOneServiceResponse<NcbBillPaymentConfirmResponse> ncbBillPaymentConfirmResponseTmb = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();

		try {
			final NcbBillPaymentConfirmResponse ncbBillPaymentConfirmResponse = ncbBillPaymentService.getNcbBillPaymentConfirm(
					correlationId,
					crmID,
					ncbBillPaymentConfirmBodyRequest
			);
			ncbBillPaymentConfirmResponseTmb.setData(ncbBillPaymentConfirmResponse);
			ncbBillPaymentConfirmResponseTmb
					.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
							ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
			return ResponseEntity.ok().headers(responseHeaders).body(ncbBillPaymentConfirmResponseTmb);
		} catch (Exception e) { 
			logger.error("Unable to getNcbBillPaymentConfirm : {}", e); 
			ncbBillPaymentConfirmResponseTmb.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), 
					ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService())); 
			return ResponseEntity.badRequest().headers(TMBUtils.getResponseHeaders()).body(ncbBillPaymentConfirmResponseTmb); 
		}
	}
}
