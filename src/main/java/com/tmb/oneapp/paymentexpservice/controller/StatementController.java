package com.tmb.oneapp.paymentexpservice.controller;

import com.google.common.base.Strings;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.data.GeneratePDFRequest;
import com.tmb.oneapp.paymentexpservice.data.GeneratePDFResponse;
import com.tmb.oneapp.paymentexpservice.service.StatementPDFGenerateService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;
import java.util.Map;
import java.util.TreeMap;

@RestController
@Tag(name = "Payment Service Controller for Generate PDF Statement")
public class StatementController {

	private static final TMBLogger<StatementController> logger = new TMBLogger<>(StatementController.class);
	private final StatementPDFGenerateService statementPDFGenerateService;

	@Autowired
	public StatementController(StatementPDFGenerateService statementPDFGenerateService) {
		super();
		this.statementPDFGenerateService = statementPDFGenerateService;
	}
	
	/**
	 * 
	 * @param correlationId
	 * @param crmId
	 * @param generatePDFRequest
	 * @return
	 */
	@Operation(summary = "Generate PDF for statement")
	@Parameters({
		@Parameter(name = "X-Correlation-ID", description = "Transations ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da"),
		@Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000000086033"),
		@Parameter(name = "Accept-Language", description = "Accept-Language", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "en-US, th-TH"),
		@Parameter(name = "Timestamp", description = "Timestamp", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Timestamp"),
		@Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
		@Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20"),
		@Parameter(name = "Device-Nickname", description = "Device-Nickname", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "test nickname"),
		@Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
		@Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
		@Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
		@Parameter(name = "X-Forward-For", description = "IP-Address", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = ""),
		@Parameter(name = "Flow-Name", description = "Flow-Name", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Mobile"),
		@Parameter(name = "Login-Method", description = "Login-Method", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Fingerprint"),
		})
	@LogAround
	@PostMapping(value = "/financial/generate-pdf-statement", consumes=MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<TmbOneServiceResponse<GeneratePDFResponse>> generatePDFStatement(
			@Parameter(hidden = true) @RequestHeader Map<String, String> requestHeaderParameter,
			@Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader("X-Correlation-ID") @Valid String correlationId,
			@Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true) @RequestHeader(name = "X-CRMID", required = true) String crmId,
			@RequestBody GeneratePDFRequest generatePDFRequest) throws TMBCommonException {
		logger.info("generatePDFStatement method start Time : {} ", System.currentTimeMillis());
		TmbOneServiceResponse<GeneratePDFResponse> oneServiceResponse = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		responseHeaders.set(PaymentServiceConstant.X_CRMID, crmId);
		logger.info("GeneratePDFRequest : {} ", PaymentServiceUtils.convertObjectToString(generatePDFRequest));
		try {
				long startaccTimeMob = System.nanoTime();
				ResponseCode errorCode = PaymentServiceUtils.validateFieldPDFStatement(generatePDFRequest);
				if(null !=  errorCode) {
					oneServiceResponse.setStatus(new TmbStatus(errorCode.getCode(), errorCode.getMessage(),
							errorCode.getService(), errorCode.getDescription()));
					logger.info("generatePDFStatement-response :::::::::::: errorCode: {}, errorMessage: {}, errorDescription: {}",
							errorCode.getCode(), errorCode.getMessage(), errorCode.getDescription());
					return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
				}
				if(null==generatePDFRequest.getBranchName()){
					generatePDFRequest.setBranchName("");
				}
				generatePDFRequest.setLanguage(generatePDFRequest.getLanguage().toUpperCase());
				generatePDFRequest.setWithNoteFlag(generatePDFRequest.getWithNoteFlag().toUpperCase());
			Map<String, String> treeMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
			treeMap.putAll(requestHeaderParameter);
				GeneratePDFResponse data = statementPDFGenerateService.getPDFStatement(generatePDFRequest, crmId, correlationId, treeMap);
				if(null == data || Strings.isNullOrEmpty(data.getPdfStatement())) {
					oneServiceResponse.setStatus(new TmbStatus(ResponseCode.DATA_NOT_FOUND_STATEMENT.getCode(), ResponseCode.DATA_NOT_FOUND_STATEMENT.getMessage(),
							ResponseCode.DATA_NOT_FOUND_STATEMENT.getService(), ResponseCode.DATA_NOT_FOUND_STATEMENT.getDescription()));
					logger.info("generatePDFStatement-response :::::::::::: code: {}, message: {}, description: {}",
							ResponseCode.DATA_NOT_FOUND_STATEMENT.getCode(), ResponseCode.DATA_NOT_FOUND_STATEMENT.getMessage(), ResponseCode.DATA_NOT_FOUND_STATEMENT.getDescription());
					return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
				}
				
				oneServiceResponse.setData(data);
				long endaccTimeMob = System.nanoTime();
				long timeaccapsedMoendb = endaccTimeMob - startaccTimeMob;
				logger.info("PDF ====> Fetch generatePDFStatement Contoller End  : {}", timeaccapsedMoendb);
			
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
			
			logger.info("generatePDFStatement method end Time : {} ", System.currentTimeMillis());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		}
		catch (TMBCommonException e) {
			logger.error("getPDFStatement-Error :::::::::::::::::::: {}", e);
			throw e;
		} catch (Exception e) {
			logger.error("Unable to fetch data from generatePDFStatement  : {}", e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
		}

	}
}
