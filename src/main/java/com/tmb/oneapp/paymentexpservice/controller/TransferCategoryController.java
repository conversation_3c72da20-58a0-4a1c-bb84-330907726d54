package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.CategoryInfoDataModel;
import com.tmb.oneapp.paymentexpservice.service.TransferCategoryService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;
import java.util.List;

/**
 * TransferCategoryController request mapping will handle apis call and then
 * navigate to respective method to get Category
 *
 */
@RestController
@Tag(name = "API To Fetch All Category List")
public class TransferCategoryController {
	private static final TMBLogger<TransferCategoryController> logger = new TMBLogger<>(
			TransferCategoryController.class);
	private final TransferCategoryService categoryService;

	/**
	 * Constructor
	 * 
	 * @param categoryService
	 */
	@Autowired
	public TransferCategoryController(TransferCategoryService categoryService) {
		this.categoryService = categoryService;
	}

	/**
	 * Method responsible for controlling API for Category
	 * 
	 * @param correlationId
	 * @return
	 */
	@Operation(summary = "Get All Category")
	@LogAround
	@GetMapping(value = "/category")
	public ResponseEntity<TmbOneServiceResponse<List<CategoryInfoDataModel>>> fetchAllCategory(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId) {
		logger.info("TransferCategoryController  fetchAllCategory method start Time : {} ", System.currentTimeMillis());
		TmbOneServiceResponse<List<CategoryInfoDataModel>> oneServiceResponseForCategory = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

		try {
			final List<CategoryInfoDataModel> transferCategoryData = categoryService.getAllCategory(correlationId);
			oneServiceResponseForCategory.setData(transferCategoryData);
			oneServiceResponseForCategory
					.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
							ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponseForCategory);
		} catch (Exception e) {
			logger.error("Unable to fetch Category : {} ", e);
			oneServiceResponseForCategory
					.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
							ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponseForCategory);
		}

	}

	@Operation(summary = "Get Category for own transfer")
	@LogAround
	@GetMapping(value = {"/transfer-category", "/pre-login/transfer-category"})
	public ResponseEntity<TmbOneServiceResponse<List<CategoryInfoDataModel>>> categoryOwnTransfer(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId) {
		logger.info("TransferCategoryController  getCategoryOwnTransfer method start Time : {} ", System.currentTimeMillis());
		TmbOneServiceResponse<List<CategoryInfoDataModel>> tmbOneServiceResponse = new TmbOneServiceResponse<>();

		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

		try {
			final List<CategoryInfoDataModel> data = categoryService.getCategoryTransfer(correlationId);
			tmbOneServiceResponse.setData(data);

			tmbOneServiceResponse
					.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
							ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
			return ResponseEntity.ok().headers(responseHeaders).body(tmbOneServiceResponse);
		} catch (Exception e) {
			logger.error("Unable to get Category for getAllCategory : {} ", e);
			tmbOneServiceResponse
					.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
							ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(tmbOneServiceResponse);
		}
	}
}
