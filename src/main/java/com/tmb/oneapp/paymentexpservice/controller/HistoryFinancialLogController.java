package com.tmb.oneapp.paymentexpservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.model.financialhistorylog.FinLogHistoryResponse;
import com.tmb.oneapp.paymentexpservice.service.HistoryFinancialLogService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@Tag(name = "History financial log")
public class HistoryFinancialLogController {
    private static final TMBLogger<HistoryFinancialLogController> logger = new TMBLogger<>(HistoryFinancialLogController.class);
    private final HistoryFinancialLogService historyFinancialLogService;

    @Autowired
    public HistoryFinancialLogController(HistoryFinancialLogService historyFinancialLogService) {
        this.historyFinancialLogService = historyFinancialLogService;
    }

    @LogAround
    @Operation(summary = "Get Transfer History Financial Log")
    @GetMapping(value = "/financial/transfer-history-log")
    public ResponseEntity<TmbOneServiceResponse<List<FinLogHistoryResponse>>> getTransferHistoryFinancialActivityLog(
            @Parameter(description = "CRM ID", example = "001100000000000000000012004012", required = true) @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<List<FinLogHistoryResponse>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            List<FinLogHistoryResponse> historyFinLogResponse = historyFinancialLogService.getTransferHistoryFinancialActivityLog(crmId, correlationId);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(historyFinLogResponse);

        } catch (Exception e) {
            logger.error("Error : getTransferHistoryFinancialActivityLog {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Get TopUp History Financial Log")
    @GetMapping(value = "/financial/topup-history-log")
    public ResponseEntity<TmbOneServiceResponse<List<FinLogHistoryResponse>>> getTopUpHistoryFinancialActivityLog(
            @Parameter(description = "CRM ID", example = "001100000000000000000012004012", required = true) @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<List<FinLogHistoryResponse>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            List<FinLogHistoryResponse> historyFinLogResponse = historyFinancialLogService.getTopUpHistoryFinancialActivityLog(crmId, correlationId);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(historyFinLogResponse);

        } catch (Exception e) {
            logger.error("Error : getTopUpHistoryFinancialActivityLog {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Get Bill History Financial Log")
    @GetMapping(value = "/financial/bill-history-log")
    public ResponseEntity<TmbOneServiceResponse<List<FinLogHistoryResponse>>> getBillHistoryFinancialActivityLog(
            @Parameter(description = "CRM ID", example = "001100000000000000000012004012", required = true) @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<List<FinLogHistoryResponse>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            List<FinLogHistoryResponse> historyFinLogResponse = historyFinancialLogService.getBillHistoryFinancialActivityLog(crmId, correlationId);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(historyFinLogResponse);

        } catch (Exception e) {
            logger.error("Error : getBillHistoryFinancialActivityLog {}", e);
            PaymentServiceUtils.handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }
}
