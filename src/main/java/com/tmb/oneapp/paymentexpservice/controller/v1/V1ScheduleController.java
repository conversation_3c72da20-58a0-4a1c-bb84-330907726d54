package com.tmb.oneapp.paymentexpservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.model.schedule.request.ScheduleValidationTopUpRequest;
import com.tmb.oneapp.paymentexpservice.model.schedule.response.ScheduleConfirmationResponse;
import com.tmb.oneapp.paymentexpservice.model.v1.schedule.V1ScheduleConfirmationRequest;
import com.tmb.oneapp.paymentexpservice.model.v1.schedule.V1ScheduleValidationBillPayResponse;
import com.tmb.oneapp.paymentexpservice.model.v1.schedule.V1ScheduleValidationTopUpResponse;
import com.tmb.oneapp.paymentexpservice.service.v1.schedule.V1ScheduleBillPaySelectionValidate;
import com.tmb.oneapp.paymentexpservice.service.v1.schedule.V1ScheduleConfirmationService;
import com.tmb.oneapp.paymentexpservice.service.v1.schedule.V1ScheduleTopUpValidationService;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/schedule")
public class V1ScheduleController {

    private static final TMBLogger<V1ScheduleController> logger = new TMBLogger<>(V1ScheduleController.class);
    private final V1ScheduleConfirmationService v1ScheduleConfirmationService;
    private final V1ScheduleTopUpValidationService v1ScheduleTopUpValidationService;
    private final V1ScheduleBillPaySelectionValidate v1ScheduleBillPaySelectionValidate;


    public V1ScheduleController(V1ScheduleConfirmationService v1ScheduleConfirmationService, V1ScheduleTopUpValidationService v1ScheduleTopUpValidationService, V1ScheduleBillPaySelectionValidate v1ScheduleBillPaySelectionValidate) {
        this.v1ScheduleConfirmationService = v1ScheduleConfirmationService;
        this.v1ScheduleTopUpValidationService = v1ScheduleTopUpValidationService;
        this.v1ScheduleBillPaySelectionValidate = v1ScheduleBillPaySelectionValidate;
    }

    @LogAround
    @Operation(summary = "Schedule of All TopUp : Validation V1")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f77-4a89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006434675"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S21")
    })
    @PostMapping(value = "/topup/validation")
    public ResponseEntity<TmbOneServiceResponse<V1ScheduleValidationTopUpResponse>> scheduleValidationOfTopUp(
            @RequestBody ScheduleValidationTopUpRequest request,
            @RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<V1ScheduleValidationTopUpResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            V1ScheduleValidationTopUpResponse response = v1ScheduleTopUpValidationService.validate(request, header);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
            tmbOneServiceResponse.setData(response);

        } catch (Exception e) {
            logger.error("Failed to process scheduleValidationOfTopUp error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Schedule of All Bill Pay : Validation V1")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4b89-ar39-b4f628fbc8da"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006564675"),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/billpay/validation")
    public ResponseEntity<TmbOneServiceResponse<V1ScheduleValidationBillPayResponse>> scheduleValidationOfBillPay(
            @RequestBody @Valid ScheduleValidationTopUpRequest request,
            @RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<V1ScheduleValidationBillPayResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            V1ScheduleValidationBillPayResponse response = v1ScheduleBillPaySelectionValidate.verifyBillPaymentAddSchedule(header, request);
            tmbOneServiceResponse.setData(response);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());

        } catch (Exception e) {
            logger.error("Failed to process scheduleValidationOfBillPay error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @LogAround
    @Operation(summary = "Schedule of All Transfer : Confirm V1")
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Correlation-ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ar39-b4f628cbc8da"),
            @Parameter(name = "X-CRMID", description = "CRM ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "001100000000000000000006534675"),
            @Parameter(name = "X-Real-Ip", description = "X-Real-Ip", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "OS-Version", description = "OS-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Channel", description = "Channel", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "mb"),
            @Parameter(name = "App-Version", description = "App-Version", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "10.0"),
            @Parameter(name = "Device-Id", description = "Device-Id", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "d28f91e4-881e-4887-a597-4a39z2822b3a"),
            @Parameter(name = "Device-Model", description = "Device-Model", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Samsung Galaxy S20")
    })
    @PostMapping(value = "/{module}/confirm")
    public ResponseEntity<TmbOneServiceResponse<ScheduleConfirmationResponse>> scheduleConfirm(
            @PathVariable String module,
            @RequestBody V1ScheduleConfirmationRequest request,
            @RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<ScheduleConfirmationResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            if (!module.equals(PaymentServiceConstant.TRANSFER_SCHEDULE_MODULE) && !module.equals(PaymentServiceConstant.TOP_UP_SCHEDULE_MODULE) && !module.equals(PaymentServiceConstant.BILL_PAY_SCHEDULE_MODULE)) {
                return ResponseEntity.badRequest().headers(header).body(tmbOneServiceResponse);
            }
            ScheduleConfirmationResponse response = v1ScheduleConfirmationService.confirm(request, header, module);
            tmbOneServiceResponse.setData(response);
            tmbOneServiceResponse.setStatus(PaymentServiceUtils.getResponseSuccess());
        } catch (Exception e) {
            logger.error("Failed to process scheduleConfirmOfTransfer error : {}", e);
            PaymentServiceUtils.handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

}
