package com.tmb.oneapp.paymentexpservice.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Strings;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBPayloadType;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.paymentexpservice.client.CustomerExpFeignClient;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCard;
import com.tmb.oneapp.paymentexpservice.model.creditcard.TransferConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.creditcard.TransferConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.creditcard.TransferReviewRequest;
import com.tmb.oneapp.paymentexpservice.model.creditcard.TransferReviewRespone;
import com.tmb.oneapp.paymentexpservice.service.InstallmentValidationService;
import com.tmb.oneapp.paymentexpservice.service.OauthService;
import com.tmb.oneapp.paymentexpservice.service.TransferMoneyService;
import com.tmb.oneapp.paymentexpservice.service.ValidationAccountService;
import com.tmb.oneapp.paymentexpservice.utils.CacheService;
import com.tmb.oneapp.paymentexpservice.utils.TtbUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACCEPT_LANGUAGE;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACCEPT_LANGUAGE_TH;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.APP_VERSION;

@RestController
@Tag(name = "Credit card service controller for transfer")
@RequiredArgsConstructor
public class TransferCreditCardController {

	private static final TMBLogger<TransferCreditCardController> logger = new TMBLogger<>(
			TransferCreditCardController.class);

	private final TransferMoneyService transferMoneyService;
	private final ValidationAccountService validationAccountService;
	private final InstallmentValidationService installmentValidationService;
	private final CustomerExpFeignClient customerExpFeignClient;
	private final CacheService cacheService;
	private final OauthService oauthService;

	@Value("${default.app-version}")
	private String defaultAppVersion;

	@LogAround
	@PostMapping(value = "/credit-card/cash-transfer-review", produces = MediaType.APPLICATION_JSON_VALUE)
	@Operation(summary = "Create cash transfer review")
	@Parameters({
			@Parameter(name = PaymentServiceConstant.HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
			@Parameter(name = PaymentServiceConstant.X_CRMID, example = "001100000000000000000018593707", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER) })
	public ResponseEntity<TmbOneServiceResponse<TransferReviewRespone>> doCashTransferReview(
			@Parameter(hidden = true) @RequestHeader Map<String, String> headers,
			@RequestBody TransferReviewRequest reviewRequest
	) throws JsonProcessingException {
		logger.payload(TTBPayloadType.INBOUND, headers, TMBUtils.convertJavaObjectToString(reviewRequest));
		TmbOneServiceResponse<TransferReviewRespone> reviewResponse = new TmbOneServiceResponse();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		String correlationId = headers.get(PaymentServiceConstant.HEADER_CORRELATION_ID.toLowerCase());
		String accountId = reviewRequest.getFromAccountId();
		String crmId = headers.get(PaymentServiceConstant.X_CRMID);
		try {
			validationAccountService.verifyAccountType(reviewRequest,correlationId,crmId);

			CompletableFuture<Boolean> isAccountOwner = validationAccountService.validateAccountOwnerAsync( reviewRequest, correlationId, crmId );

			List<CreditCard> creditCards = validationAccountService.validateCreditCardAccount(correlationId, crmId, accountId);
			if( creditCards.isEmpty()
							|| !isAccountOwner.get()
			){
				reviewResponse.setStatus(new TmbStatus(ResponseCode.DATA_NOT_FOUND.getCode(),
						ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService(),
						ResponseCode.DATA_NOT_FOUND.getDescription()));
				return ResponseEntity.badRequest().headers(responseHeaders).body(reviewResponse);
			}

			reviewRequest.setProductCode(creditCards.get(0).getProductCode());

			TransferReviewRespone reviewInfo = transferMoneyService.createTemporaryCashTransfer(reviewRequest);
			reviewResponse.setData(reviewInfo);
			reviewResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
		} catch (Exception e) {
			logger.error(e.toString(), e);
			reviewResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

		}
		return ResponseEntity.ok().headers(responseHeaders).body(reviewResponse);
	}

	@LogAround
	@PostMapping(value = "/credit-card/cash-transfer-confirm", produces = MediaType.APPLICATION_JSON_VALUE)
	@Operation(summary = "Create cash transfer confirm")
	@Parameters({
			@Parameter(name = PaymentServiceConstant.HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
			@Parameter(name = PaymentServiceConstant.X_CRMID, example = "001100000000000000000018593707", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
			@Parameter( name = PaymentServiceConstant.HEADER_REF_ID, required = false, example = "12345", in = ParameterIn.HEADER ),
			@Parameter( name = PaymentServiceConstant.HEADER_MODULE, required = false, example = "/apis/products/loanSubmissionOnline/report/generate", in = ParameterIn.HEADER)})
	public ResponseEntity<TmbOneServiceResponse<TransferConfirmResponse>> doCashTransferConfirm(
			@Parameter(hidden = true) @RequestHeader Map<String, String> headers,
			@RequestBody TransferConfirmRequest confirmRequest
	) {
		TmbOneServiceResponse<TransferConfirmResponse> reviewResponse = new TmbOneServiceResponse();
		String crmId = headers.get(PaymentServiceConstant.X_CRMID);
		String correlationId = headers.get(PaymentServiceConstant.HEADER_CORRELATION_ID);
		String refId = headers.get(PaymentServiceConstant.HEADER_REF_ID);
		String module = headers.get(PaymentServiceConstant.HEADER_MODULE);
		String ipAddress = headers.get(PaymentServiceConstant.HEADER_X_FORWARD_FOR);

		String language = StringUtils.isNotBlank(headers.get(ACCEPT_LANGUAGE))?
				headers.get(ACCEPT_LANGUAGE): ACCEPT_LANGUAGE_TH;
		String appVersion = StringUtils.isNotBlank(headers.get(APP_VERSION))?
				headers.get(APP_VERSION): defaultAppVersion;
		if (Strings.isNullOrEmpty(headers.get(PaymentServiceConstant.CHANNEL))) {
			headers.put(PaymentServiceConstant.CHANNEL, PaymentServiceConstant.MB);
		}
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(PaymentServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

		CommonAuthenVerifyRefRequest commonAuthenRequest = new CommonAuthenVerifyRefRequest();
		commonAuthenRequest.setRefId(refId);
		commonAuthenRequest.setFlowName(module);
		try {

			String reference = confirmRequest.getRefence();
			String reviewRequestCacheKey = transferMoneyService.decryptAes(reference);
			TransferReviewRequest cachedReviewRequest = transferMoneyService.fetchReviewRequest(reviewRequestCacheKey);

			if (!oauthService.commonAuthenVerifyRef(correlationId, crmId, ipAddress,commonAuthenRequest, cachedReviewRequest)) {
				logger.error("Validate PIN Access failed. ref_id: {}", refId);
				reviewResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
						ResponseCode.FAILED.getService(), "Validate PIN Access failed"));
				return ResponseEntity.badRequest().headers(responseHeaders).body(reviewResponse);
			}
			
			if(!installmentValidationService.validateRate( correlationId, language, appVersion, cachedReviewRequest )){
				logger.error("Validate Installment Rate failed. reference: " + confirmRequest.getRefence());
				reviewResponse.setStatus(new TmbStatus(
								ResponseCode.FAILED.getCode(),
								ResponseCode.FAILED.getMessage(),
								ResponseCode.FAILED.getService(),
								ResponseCode.FAILED.getDescription()
				));
				return ResponseEntity.badRequest().headers(responseHeaders).body(reviewResponse);
			}
			
			TransferConfirmResponse confirmResponse = transferMoneyService.confirmCashTransfer(
							headers,
							cachedReviewRequest,
							confirmRequest.getRefence(),
							reviewRequestCacheKey,
							crmId,
							correlationId
			);
			reviewResponse.setData(confirmResponse);
			reviewResponse.setStatus(TtbUtils.toStatus(ResponseCode.SUCCESS));
			
			logger.info("start deleting credit card cache with correlationId: {}, crmId: {}", correlationId, crmId);
			cacheService.handleResponseFromRedis(
					correlationId, language, appVersion, crmId, customerExpFeignClient::deleteCreditCardCache
			);
		} catch (TMBCommonException e) {
			reviewResponse.setStatus(new TmbStatus(e.getErrorCode(), e.getMessage(), ResponseCode.FAILED.getService(),
					e.getErrorMessage()));
			logger.error(e.toString(), e);
		} catch (Exception e) {
			reviewResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));
			logger.error(e.toString(), e);
		}

		return ResponseEntity.ok().headers(responseHeaders).body(reviewResponse);
	}
}
