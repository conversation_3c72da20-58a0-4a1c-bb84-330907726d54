package com.tmb.oneapp.paymentexpservice.validator.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.utils.PaymentServiceUtils;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

@Component
public class BillPaymentValidator {
    private static final TMBLogger<BillPaymentValidator> logger = new TMBLogger<>(BillPaymentValidator.class);
    private static final String BLACKLIST = "[\"'=<>~\\\\]";
    private static final String REF_ALLOW = "^$|^[a-zA-Z\\d\\s._\\/\\-]+$";

    /**
     * Validates the bill payment request
     *
     * @param request the request to be validated
     * @throws TMBCommonException if the request is invalid
     */
    public void validateTopUpVerifyRequest(TopUpVerifyRequest request) throws TMBCommonException {
        validateNote(request.getNote());

        validateReference(request.getReference1(), "Reference1");
        validateReference(request.getReference2(), "Reference2");
        validateReference(request.getReference3(), "Reference3");
    }

    /**
     * Validates the note to check for blacklisted characters
     *
     * @param note the note to be checked
     * @throws TMBCommonException if blacklisted characters are found
     */
    private void validateNote(String note) throws TMBCommonException {
        if (containsBlacklistChars(note)) {
            logger.error("Validation failed: Note contains blacklisted characters: {}", note);
            throw PaymentServiceUtils.failException(ResponseCode.INCORRECT_MEMO_NOTE);
        }
    }

    /**
     * Validate the reference to check for invalid characters
     *
     * @param reference the reference to be checked
     * @param fieldName the field name
     * @throws TMBCommonException if the reference contains invalid characters
     */
    private void validateReference(String reference, String fieldName) throws TMBCommonException {
        if (!checkBillpayChars(reference)) {
            logger.error("Validation failed: {} contains invalid characters: {}", fieldName, reference);
            throw PaymentServiceUtils.failException(ResponseCode.INCORRECT_REF1_REF2);
        }
    }

    /**
     * Check if the input contains blacklisted characters
     *
     * @param input the input to be checked
     * @return true if contains blacklisted characters, false otherwise
     */
    private boolean containsBlacklistChars(String input) {
        if (input == null || input.isEmpty()) {
            return false;
        }
        return Pattern.compile(BLACKLIST).matcher(input).find();
    }

    /**
     * Check if the input matches the allowed pattern
     *
     * @param input the input to be checked
     * @return true if the input matches the allowed pattern, false otherwise
     */
    private boolean checkBillpayChars(String input) {
        if (input == null || input.isEmpty()) {
            return true;
        }
        return Pattern.compile(REF_ALLOW).matcher(input).matches();
    }
}
