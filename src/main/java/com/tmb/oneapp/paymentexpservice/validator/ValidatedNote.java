package com.tmb.oneapp.paymentexpservice.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = ValidatedNoteValidator.class)
public @interface ValidatedNote {
    String message() default "Invalid note format";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}