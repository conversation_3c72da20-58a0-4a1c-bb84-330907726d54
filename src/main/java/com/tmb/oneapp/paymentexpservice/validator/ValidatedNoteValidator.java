package com.tmb.oneapp.paymentexpservice.validator;

import com.tmb.common.logger.TMBLogger;
import org.apache.commons.lang3.StringUtils;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidatedNoteValidator implements ConstraintValidator<ValidatedNote, String> {

    private static final TMBLogger<ValidatedNoteValidator> logger = new TMBLogger<>(ValidatedNoteValidator.class);

    private static final String PATTERN = "^$|^([a-zA-Z\\dก-์๐-๙,|$^{}%\\[\\]*+\\-@#&()\\/_;:!?.]+\\s)*[a-zA-Z\\dก-์,|$^{}%\\[\\]*+\\-@#&()\\/_;:!?.]*$";
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if(StringUtils.isBlank(value)){
            return true;
        }
        String characterFilter = "[^\\p{L}\\p{M}\\p{N}\\p{P}\\p{Z}\\p{Cf}\\p{Cs}\\s<>]";
        String emotionless = value.replaceAll(characterFilter,"");

        if(!emotionless.matches(PATTERN)) {
            logger.error("ValidatedNoteValidator failed: someField not matches {}",value);
        }

        return emotionless.matches(PATTERN);
    }
}