package com.tmb.oneapp.paymentexpservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.paymentexpservice.client.BillPayFleetCardFeignClient;
import com.tmb.oneapp.paymentexpservice.factory.Factory;
import com.tmb.oneapp.paymentexpservice.model.AmountTopUp;
import com.tmb.oneapp.paymentexpservice.model.BillerInfoResponse;
import com.tmb.oneapp.paymentexpservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.DepositAccount;
import com.tmb.oneapp.paymentexpservice.model.OCPBillPayment;
import com.tmb.oneapp.paymentexpservice.model.ReferenceTopUpResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyResponse;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityBillPayVerifyEvent;
import com.tmb.oneapp.paymentexpservice.model.commonauth.VerifyTransactionResult;
import com.tmb.oneapp.paymentexpservice.model.fleetcard.ErrorStatus;
import com.tmb.oneapp.paymentexpservice.model.fleetcard.FleetCard;
import com.tmb.oneapp.paymentexpservice.model.fleetcard.FleetCardETEResponse;
import com.tmb.oneapp.paymentexpservice.model.fleetcard.FleetCardInquiryAndValidateETERequest;
import com.tmb.oneapp.paymentexpservice.model.fleetcard.FleetCardStatus;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import com.tmb.oneapp.paymentexpservice.utils.CacheService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.sql.SQLException;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACTIVITY_LOG_BILL_PAY_VERIFY_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.APP_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_COMP_CODE_TTB_FLEET_CARD;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.CHANNEL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_MODEL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.FAILURE;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.OS_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.SUCCESS;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class FleetCardValidateServiceTest {

    @Mock
    private CommonPaymentService commonPaymentService;

    @Mock
    BillPayFleetCardFeignClient billPayFleetCardFeignClient;

    @Mock
    CacheService cacheService;

    @Mock
    BillPaymentValidateTransaction billPaymentValidateTransaction;

    @Mock
    LogService logService;

    @InjectMocks
    @Spy
    FleetCardValidateService fleetCardValidateService;

    String correlationId;
    String transId;
    TopUpVerifyRequest topUpVerifyRequest;
    ActivityBillPayVerifyEvent activityBillPayVerifyEvent;
    BillerTopUpDetailResponse billerTopUpDetail;
    HttpHeaders headers;
    BillerTopUpDetailResponse billerDetail;
    DepositAccount depositAccount;

    @BeforeEach
    void setUp() {

        correlationId = "correlationId";
        transId = "transId";

        topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setBillerCompCode(BILL_COMP_CODE_TTB_FLEET_CARD);
        topUpVerifyRequest.setReference1("ref1");
        topUpVerifyRequest.setAmount("11.11");


        headers = new HttpHeaders();
        headers.add(X_CRMID, "crmId");
        headers.add(HEADER_CORRELATION_ID, correlationId);
        headers.add(OS_VERSION, "");
        headers.add(CHANNEL, "");
        headers.add(APP_VERSION, "");
        headers.add(DEVICE_ID, "");
        headers.add(DEVICE_MODEL, "");

        billerDetail = Factory.createBillerDetail();

        activityBillPayVerifyEvent = new ActivityBillPayVerifyEvent(correlationId, ACTIVITY_LOG_BILL_PAY_VERIFY_ID, headers, topUpVerifyRequest, billerDetail);
        billerTopUpDetail = new BillerTopUpDetailResponse(new BillerInfoResponse(), new ReferenceTopUpResponse(), new ReferenceTopUpResponse(), new AmountTopUp());
        depositAccount = new DepositAccount();
        depositAccount.setAccountNumber("****************");
    }

    @Test
    void validateSuccessTest() throws TMBCommonException, SQLException {
        mockGetTransactionId();
        mockResponseETEWithSuccessStatus();
        mockFetchTransactionLimit();
        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        Assertions.assertDoesNotThrow(() -> fleetCardValidateService.validate(topUpVerifyRequest, headers, transId, billerTopUpDetail, activityBillPayVerifyEvent, depositAccount));

        Mockito.verify(cacheService, Mockito.times(1)).set(eq(transId), Mockito.any());
        Mockito.verify(logService, Mockito.times(1)).saveLogActivityBillPayEvent(activityBillPayVerifyEvent);

        ArgumentCaptor<FleetCardInquiryAndValidateETERequest> fleetCardInquiryAndValidateETERequestArgumentCaptor = ArgumentCaptor.forClass(FleetCardInquiryAndValidateETERequest.class);
        Mockito.verify(fleetCardValidateService, Mockito.times(1)).callRequestToETE(Mockito.any(), fleetCardInquiryAndValidateETERequestArgumentCaptor.capture());
        FleetCardInquiryAndValidateETERequest request = fleetCardInquiryAndValidateETERequestArgumentCaptor.getValue();
        Assertions.assertEquals("11.11", request.getFleetCard().getCardPayment().getAmounts());

    }

    @Test
    void validateWhenExecuteWithCommonAuthenShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException {
        mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen();

        mockGetTransactionId();
        mockResponseETEWithSuccessStatus();
        mockFetchTransactionLimit();

        TopUpVerifyResponse actual = fleetCardValidateService.validate(topUpVerifyRequest, headers, transId, billerTopUpDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
    }

    @Test
    void validateWhenRequestHaveRef2AndRef1MaskingShouldReplaceRef1WithRef2AndToAccIdShouldNotMaskingSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException {
        mockGetTransactionId();
        mockResponseETEWithSuccessStatus();
        mockFetchTransactionLimit();
        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        String expectedRef2 = "ref2";
        topUpVerifyRequest.setReference1("ref1Maskingxxxx");
        topUpVerifyRequest.setReference2(expectedRef2);
        Assertions.assertDoesNotThrow(() -> fleetCardValidateService.validate(topUpVerifyRequest, headers, transId, billerTopUpDetail, activityBillPayVerifyEvent, depositAccount));

        Mockito.verify(logService, Mockito.times(1)).saveLogActivityBillPayEvent(activityBillPayVerifyEvent);

        ArgumentCaptor<FleetCardInquiryAndValidateETERequest> fleetCardInquiryAndValidateETERequestArgumentCaptor = ArgumentCaptor.forClass(FleetCardInquiryAndValidateETERequest.class);
        Mockito.verify(fleetCardValidateService, Mockito.times(1)).callRequestToETE(Mockito.any(), fleetCardInquiryAndValidateETERequestArgumentCaptor.capture());
        FleetCardInquiryAndValidateETERequest request = fleetCardInquiryAndValidateETERequestArgumentCaptor.getValue();
        Assertions.assertEquals(expectedRef2, request.getFleetCard().getCardId());

        ArgumentCaptor<String> dataToSaveCacheArgumentCaptor = ArgumentCaptor.forClass(String.class);
        Mockito.verify(cacheService, Mockito.times(1)).set(eq(transId), dataToSaveCacheArgumentCaptor.capture());
        OCPBillPayment dataToSaveCache = (OCPBillPayment) TMBUtils.convertStringToJavaObj(dataToSaveCacheArgumentCaptor.getValue(), OCPBillPayment.class);
        Assertions.assertEquals(expectedRef2, dataToSaveCache.getToAccount().getAccountId());
        Assertions.assertEquals(expectedRef2, dataToSaveCache.getPaymentCacheData().getOriginRef1());
        Assertions.assertNull(dataToSaveCache.getPaymentCacheData().getOriginRef2());
        Assertions.assertTrue(dataToSaveCache.getPaymentCacheData().isRequirePin());
    }

    @Test
    void validateWhenRequestHaveRef2AndRef1NotMaskingShouldReplaceRef2WithNullAndToAccIdShouldNotMaskingSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException {
        mockGetTransactionId();
        mockResponseETEWithSuccessStatus();
        mockFetchTransactionLimit();
        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        String ref2 = "ref2";
        String ref1NotMasking = "ref1NotMasking";
        topUpVerifyRequest.setReference1(ref1NotMasking);
        topUpVerifyRequest.setReference2(ref2);
        Assertions.assertDoesNotThrow(() -> fleetCardValidateService.validate(topUpVerifyRequest, headers, transId, billerTopUpDetail, activityBillPayVerifyEvent, depositAccount));

        Mockito.verify(logService, Mockito.times(1)).saveLogActivityBillPayEvent(activityBillPayVerifyEvent);

        ArgumentCaptor<FleetCardInquiryAndValidateETERequest> fleetCardInquiryAndValidateETERequestArgumentCaptor = ArgumentCaptor.forClass(FleetCardInquiryAndValidateETERequest.class);
        Mockito.verify(fleetCardValidateService, Mockito.times(1)).callRequestToETE(Mockito.any(), fleetCardInquiryAndValidateETERequestArgumentCaptor.capture());
        FleetCardInquiryAndValidateETERequest request = fleetCardInquiryAndValidateETERequestArgumentCaptor.getValue();
        Assertions.assertEquals(ref1NotMasking, request.getFleetCard().getCardId());

        ArgumentCaptor<String> dataToSaveCacheArgumentCaptor = ArgumentCaptor.forClass(String.class);
        Mockito.verify(cacheService, Mockito.times(1)).set(eq(transId), dataToSaveCacheArgumentCaptor.capture());
        OCPBillPayment dataToSaveCache = (OCPBillPayment) TMBUtils.convertStringToJavaObj(dataToSaveCacheArgumentCaptor.getValue(), OCPBillPayment.class);
        Assertions.assertEquals(ref1NotMasking, dataToSaveCache.getToAccount().getAccountId());
        Assertions.assertEquals(ref1NotMasking, dataToSaveCache.getPaymentCacheData().getOriginRef1());
        Assertions.assertNull(dataToSaveCache.getPaymentCacheData().getOriginRef2());
    }

    @Test
    void validateFailedBecauseOfETETest() throws TMBCommonException {
        mockGetTransactionId();
        mockResponseETEWithFailureStatus();

        Assertions.assertThrows(TMBCommonException.class, () -> fleetCardValidateService.validate(topUpVerifyRequest, headers, transId, billerTopUpDetail, activityBillPayVerifyEvent, depositAccount));

        Mockito.verify(logService, Mockito.times(1)).saveLogActivityBillPayEvent(activityBillPayVerifyEvent);

        ArgumentCaptor<FleetCardInquiryAndValidateETERequest> fleetCardInquiryAndValidateETERequestArgumentCaptor = ArgumentCaptor.forClass(FleetCardInquiryAndValidateETERequest.class);
        Mockito.verify(fleetCardValidateService, Mockito.times(1)).callRequestToETE(Mockito.any(), fleetCardInquiryAndValidateETERequestArgumentCaptor.capture());
        FleetCardInquiryAndValidateETERequest request = fleetCardInquiryAndValidateETERequestArgumentCaptor.getValue();
        Assertions.assertEquals("11.11", request.getFleetCard().getCardPayment().getAmounts());

    }

    private void mockGetTransactionId() {
        when(commonPaymentService.getTransactionId(Mockito.anyString(), Mockito.anyInt())).thenReturn("00005");
    }

    private void mockResponseETEWithSuccessStatus() {
        FleetCardETEResponse fleetCardETEResponse = getMockFleetCardETEResponse(SUCCESS);
        ResponseEntity<FleetCardETEResponse> response = ResponseEntity.ok(fleetCardETEResponse);

        when(billPayFleetCardFeignClient.fleetCardInquiryAndValidate(Mockito.any(), Mockito.any())).thenReturn(response);
    }

    private void mockResponseETEWithFailureStatus() {
        FleetCardETEResponse fleetCardETEResponse = getMockFleetCardETEResponse(FAILURE);
        ErrorStatus errorStatus = new ErrorStatus();
        errorStatus.setErrorCode("API0007");
        errorStatus.setDescription("Amount exceed max allowedAmount");

        FleetCardStatus fleetCardStatus = new FleetCardStatus();
        fleetCardStatus.setErrorStatus(errorStatus);
        fleetCardStatus.setStatusCode("1");
        fleetCardETEResponse.setStatus(fleetCardStatus);
        ResponseEntity<FleetCardETEResponse> response = ResponseEntity.ok(fleetCardETEResponse);

        when(billPayFleetCardFeignClient.fleetCardInquiryAndValidate(Mockito.any(), Mockito.any())).thenReturn(response);
    }

    private FleetCardETEResponse getMockFleetCardETEResponse(String status) {
        String statusCode = status.equals(SUCCESS) ? "0" : "1";
        FleetCardStatus fleetCardStatus = new FleetCardStatus();
        fleetCardStatus.setStatusCode(statusCode);

        FleetCard fleetCard = new FleetCard();

        FleetCardETEResponse fleetCardETEResponse = new FleetCardETEResponse();
        fleetCardETEResponse.setStatus(fleetCardStatus);
        fleetCardETEResponse.setFleetCard(fleetCard);
        return fleetCardETEResponse;
    }

    private void mockFetchTransactionLimit() throws SQLException {
        when(billPaymentValidateTransaction.fetchTransactionLimit(anyString(), anyString())).thenReturn(new CustomerCrmProfile());
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen() throws TMBCommonException {
        when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(new VerifyTransactionResult(true, null, new CommonAuthenResult()));
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen() throws TMBCommonException {
        CommonAuthenResult commonAuthenResult = new CommonAuthenResult()
                .setRequireCommonAuthen(true)
                .setIsForceFR(true)
                .setPinFree(true);
        VerifyTransactionResult resultWhenExecuteCommonAuthen = new VerifyTransactionResult(false, null, commonAuthenResult);
        when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(resultWhenExecuteCommonAuthen);
    }

}