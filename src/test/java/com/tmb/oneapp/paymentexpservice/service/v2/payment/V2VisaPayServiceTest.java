package com.tmb.oneapp.paymentexpservice.service.v2.payment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.cache.Transaction;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.paymentexpservice.client.PaymentServiceFeignClient;
import com.tmb.oneapp.paymentexpservice.client.RetailLendingBizServiceClient;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.CardStatus;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenticationInformation;
import com.tmb.oneapp.paymentexpservice.model.PaymentCacheData;
import com.tmb.oneapp.paymentexpservice.model.VisaConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.VisaQRPayVerifyResponse;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityVisaQRPayVerifyEvent;
import com.tmb.oneapp.paymentexpservice.model.billpay.visa.ScsResponse;
import com.tmb.oneapp.paymentexpservice.model.billpay.visa.ScsStatus;
import com.tmb.oneapp.paymentexpservice.model.commonauth.CommonAuthenWithPayloadRequest;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCard;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCardInformationResponse;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import com.tmb.oneapp.paymentexpservice.service.BillPaymentValidateTransaction;
import com.tmb.oneapp.paymentexpservice.service.CommonPaymentService;
import com.tmb.oneapp.paymentexpservice.service.LogService;
import com.tmb.oneapp.paymentexpservice.service.OauthService;
import com.tmb.oneapp.paymentexpservice.service.TransactionLimitService;
import com.tmb.oneapp.paymentexpservice.utils.CacheService;
import feign.FeignException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import static com.tmb.oneapp.paymentexpservice.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.paymentexpservice.constant.CommonAuthenticationConstant.COMMON_AUTH_VISA_QR_FEATURE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.InternationalTransferConstant.FIN_FAILED_STATUS;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PRE_LOGIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class V2VisaPayServiceTest {

    private static final String REFERENCE_CODE = "**************000001";

    @InjectMocks
    V2VisaPayService v2VisaPayService;

    @Mock
    BillPaymentValidateTransaction billPaymentValidateTransaction;

    @Mock
    RetailLendingBizServiceClient retailLendingBizServiceClient;

    @Mock
    PaymentServiceFeignClient paymentServiceFeignClient;

    @Mock
    TransactionLimitService transactionLimitService;

    @Mock
    CommonPaymentService commonPaymentService;

    @Mock
    CacheService cacheService;

    @Mock
    OauthService oauthService;

    @Mock
    LogService logService;

    String crmId = "001100000000000000000001184383";
    String correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";

    private HttpHeaders headers;

    @BeforeEach
    void setUp() throws JsonProcessingException {
        headers = new HttpHeaders();
        headers.set(HEADER_CORRELATION_ID, UUID.randomUUID().toString());
        headers.set(X_CRMID, "001100000000000000000025531537");
        headers.set(DEVICE_ID, "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76");
        headers.set(PRE_LOGIN, "false");

        lenient().doNothing().when(logService)
                .saveLogActivityBillPayEvent(any());
        lenient().doNothing().when(commonPaymentService)
                .saveDataToCacheAndSecondary(anyString(), any(), anyString());
    }

    @ParameterizedTest
    @CsvSource({"*************", "****************"})
    void testValidMerchantIdWhenValidateThenSuccess(String merchantId) throws SQLException, TMBCommonException, JsonProcessingException {
        VisaQRPayVerifyRequest request = new VisaQRPayVerifyRequest();
        request.setTransId("transId");
        request.setCardNumber("**************");
        request.setAccountNumber("**************");
        request.setAmount("1000.00");
        request.setNote("test note");

        TmbOneServiceResponse<CreditCardInformationResponse> response = new TmbOneServiceResponse<>();
        CreditCardInformationResponse cardInformationResponse = new CreditCardInformationResponse();
        CreditCard creditCard = new CreditCard();
        creditCard.setCardNo("**************");
        creditCard.setCardName("Test Card");
        creditCard.setAccountId("**************");
        creditCard.setAllowFromForBillPayTopUpEpayment("1");
        creditCard.setAccountStatus("Active");
        CardStatus cardStatus = new CardStatus();
        cardStatus.setCardActiveFlag("Active");
        cardStatus.setAccountStatus("000");
        cardStatus.setPreviousExpiryDate("0000");
        creditCard.setCardStatus(cardStatus);
        cardInformationResponse.setCreditCards(List.of(creditCard));

        response.setData(cardInformationResponse);
        PaymentCacheData paymentCacheData = new PaymentCacheData();
        PaymentCacheData.VisaData visaData = new PaymentCacheData.VisaData();
        request.setAmount("1.00");
        visaData.setFeeFlat("1.00");
        visaData.setTipAmount("03");
        visaData.setCurrencyId("764");
        visaData.setFeePercentage("1.00");
        visaData.setMerchantId(merchantId);
        visaData.setBusinessCode(StringUtils.leftPad("", 4, "0"));
        visaData.setMerchantName(StringUtils.leftPad("", 25, "X"));
        visaData.setChecksum(StringUtils.leftPad("", 4, "0"));
        visaData.setPurchaseIdentifier(StringUtils.leftPad("", 99, "X"));
        paymentCacheData.setVisaData(visaData);

        when(cacheService.get(anyString())).thenReturn(TMBUtils.convertJavaObjectToString(paymentCacheData));

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPaymentAccuUsgAmt(new BigDecimal("1000.00"));

        when(transactionLimitService.fetchTransactionLimit(anyString(), anyString()))
                .thenReturn(customerCrmProfile);

        CommonAuthenResult commonAuthenResult = new CommonAuthenResult();
        commonAuthenResult.setRequireCommonAuthen(true);
        commonAuthenResult.setIsForceFR(true);
        commonAuthenResult.setPinFree(true);

        when(billPaymentValidateTransaction.validateIsRequireCommonAuthen(any(), anyString(), anyBoolean(), any()))
                .thenReturn(commonAuthenResult);

        when(retailLendingBizServiceClient
                .getCustomerCreditCard(anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(ResponseEntity.ok(response));
        
        assertThrows(TMBCommonException.class,
                () -> v2VisaPayService.validate(correlationId, crmId, headers, request));
        
        request.setCardNumber(creditCard.getCardNo());
        request.setAccountNumber(creditCard.getAccountId());
        
        VisaQRPayVerifyResponse result = v2VisaPayService.validate(correlationId, crmId, headers, request);
        
        assertNotNull(result);
        assertEquals(request.getTransId(), result.getTransId());
        assertEquals(visaData.getMerchantId(), result.getMerchantId());
        assertEquals(visaData.getMerchantName(), result.getMerchantName());
        assertEquals(creditCard.getCardName(), result.getCardEmbossingName());
        assertEquals(request.getAmount(), result.getAmount());
        assertEquals(true, result.getIsRequireCommonAuthen());
        assertNotNull(result.getCommonAuthenticationInformation());
        assertEquals(COMMON_AUTH_VISA_QR_FEATURE_ID, result.getCommonAuthenticationInformation().getFeatureId());
        assertEquals(COMMON_AUTH_BILL_FLOW_NAME, result.getCommonAuthenticationInformation().getFlowName());
        
        verify(commonPaymentService).saveDataToCacheAndSecondary(eq(request.getTransId()), any(PaymentCacheData.class), eq(correlationId));
    }

    @Test
    void testNoCacheDataWhenValidateThenThrowNotFoundException() throws JsonProcessingException {
        VisaQRPayVerifyRequest request = new VisaQRPayVerifyRequest();
        request.setTransId("nonExistentTransId");

        when(cacheService.get(anyString())).thenReturn(null);
        
        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v2VisaPayService.validate(correlationId, crmId, headers, request));

        assertEquals(ResponseCode.NOT_FOUND.getCode(), exception.getErrorCode());
    }

    @Test
    void testInvalidCurrencyCodeWhenValidateThenThrowException() throws JsonProcessingException {
        VisaQRPayVerifyRequest request = new VisaQRPayVerifyRequest();
        request.setTransId("transId");
        request.setCardNumber("**************");
        request.setAccountNumber("**************");
        request.setAmount("1000.00");

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        PaymentCacheData.VisaData visaData = new PaymentCacheData.VisaData();
        visaData.setCurrencyId("840");
        visaData.setMerchantId("*************");
        paymentCacheData.setVisaData(visaData);

        when(cacheService.get(anyString())).thenReturn(TMBUtils.convertJavaObjectToString(paymentCacheData));
        
        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v2VisaPayService.validate(correlationId, crmId, headers, request));

        assertEquals(ResponseCode.QR_VISA_ERR_1111.getCode(), exception.getErrorCode());
    }

    @Test
    void testLongTag62DataWhenValidateThenThrowException() throws JsonProcessingException {
        VisaQRPayVerifyRequest request = new VisaQRPayVerifyRequest();
        request.setTransId("transId");
        request.setCardNumber("**************");
        request.setAccountNumber("**************");
        request.setAmount("1000.00");

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        PaymentCacheData.VisaData visaData = new PaymentCacheData.VisaData();
        visaData.setCurrencyId("764");
        visaData.setMerchantId("*************");
        visaData.setPurchaseIdentifier(StringUtils.leftPad("", 50, "X"));
        visaData.setMerchantRefId(StringUtils.leftPad("", 20, "X"));
        visaData.setTerminalId(StringUtils.leftPad("", 20, "X"));
        visaData.setSenderContract(StringUtils.leftPad("", 20, "X"));

        paymentCacheData.setVisaData(visaData);

        when(cacheService.get(anyString())).thenReturn(TMBUtils.convertJavaObjectToString(paymentCacheData));
        
        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v2VisaPayService.validate(correlationId, crmId, headers, request));

        assertEquals(ResponseCode.QR_VISA_ERR_1111.getCode(), exception.getErrorCode());
    }

    @Test
    void testInvalidMerchantIdWhenValidateThenThrowException() throws JsonProcessingException {
        VisaQRPayVerifyRequest request = new VisaQRPayVerifyRequest();
        request.setTransId("transId");
        request.setCardNumber("**************");
        request.setAccountNumber("**************");
        request.setAmount("1000.00");

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        PaymentCacheData.VisaData visaData = new PaymentCacheData.VisaData();
        visaData.setCurrencyId("764");
        visaData.setMerchantId("**********");
        visaData.setPurchaseIdentifier("test");

        paymentCacheData.setVisaData(visaData);

        when(cacheService.get(anyString())).thenReturn(TMBUtils.convertJavaObjectToString(paymentCacheData));
        
        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v2VisaPayService.validate(correlationId, crmId, headers, request));

        assertEquals(ResponseCode.QR_VISA_ERR_1111.getCode(), exception.getErrorCode());
    }

    @Test
    void testCreditCardFetchFailsWhenValidateThenThrowException() throws JsonProcessingException, TMBCommonException, SQLException {
        VisaQRPayVerifyRequest request = new VisaQRPayVerifyRequest();
        request.setTransId("transId");
        request.setCardNumber("**************");
        request.setAccountNumber("**************");
        request.setAmount("1000.00");

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        PaymentCacheData.VisaData visaData = new PaymentCacheData.VisaData();
        visaData.setCurrencyId("764");
        visaData.setMerchantId("*************");
        visaData.setPurchaseIdentifier("test");

        paymentCacheData.setVisaData(visaData);

        when(cacheService.get(anyString())).thenReturn(TMBUtils.convertJavaObjectToString(paymentCacheData));

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        when(transactionLimitService.fetchTransactionLimit(anyString(), anyString()))
                .thenReturn(customerCrmProfile);

        CommonAuthenResult commonAuthenResult = new CommonAuthenResult();
        when(billPaymentValidateTransaction.validateIsRequireCommonAuthen(any(), anyString(), anyBoolean(), any()))
                .thenReturn(commonAuthenResult);
        
        when(retailLendingBizServiceClient
                .getCustomerCreditCard(anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenThrow(FeignException.FeignServerException.class);
        
        assertThrows(TMBCommonException.class,
                () -> v2VisaPayService.validate(correlationId, crmId, headers, request));
    }

    @Test
    void testCreditCardNotFoundWhenValidateThenThrowException() throws JsonProcessingException, TMBCommonException, SQLException {
        VisaQRPayVerifyRequest request = new VisaQRPayVerifyRequest();
        request.setTransId("transId");
        request.setCardNumber("**************");
        request.setAccountNumber("**************");
        request.setAmount("1000.00");

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        PaymentCacheData.VisaData visaData = new PaymentCacheData.VisaData();
        visaData.setCurrencyId("764");
        visaData.setMerchantId("*************");
        visaData.setPurchaseIdentifier("test");

        paymentCacheData.setVisaData(visaData);

        when(cacheService.get(anyString())).thenReturn(TMBUtils.convertJavaObjectToString(paymentCacheData));

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        when(transactionLimitService.fetchTransactionLimit(anyString(), anyString()))
                .thenReturn(customerCrmProfile);

        CommonAuthenResult commonAuthenResult = new CommonAuthenResult();
        when(billPaymentValidateTransaction.validateIsRequireCommonAuthen(any(), anyString(), anyBoolean(), any()))
                .thenReturn(commonAuthenResult);
        
        TmbOneServiceResponse<CreditCardInformationResponse> response = new TmbOneServiceResponse<>();
        CreditCardInformationResponse cardInformationResponse = new CreditCardInformationResponse();
        cardInformationResponse.setCreditCards(Collections.emptyList());
        response.setData(cardInformationResponse);

        when(retailLendingBizServiceClient
                .getCustomerCreditCard(anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(ResponseEntity.ok(response));
        
        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v2VisaPayService.validate(correlationId, crmId, headers, request));

        assertEquals(ResponseCode.CREDIT_CARD_NOT_ELIGIBLE.getCode(), exception.getErrorCode());
    }

    @Test
    void testFlatFeeWhenValidateThenUseFlatFee() throws SQLException, TMBCommonException, JsonProcessingException {
        VisaQRPayVerifyRequest request = new VisaQRPayVerifyRequest();
        request.setTransId("transId");
        request.setCardNumber("**************");
        request.setAccountNumber("**************");
        request.setAmount("1000.00");

        TmbOneServiceResponse<CreditCardInformationResponse> response = new TmbOneServiceResponse<>();
        CreditCardInformationResponse cardInformationResponse = new CreditCardInformationResponse();
        CreditCard creditCard = new CreditCard();
        creditCard.setCardNo("**************");
        creditCard.setCardName("Test Card");
        creditCard.setAccountId("**************");
        creditCard.setAllowFromForBillPayTopUpEpayment("1");
        creditCard.setAccountStatus("Active");
        CardStatus cardStatus = new CardStatus();
        cardStatus.setCardActiveFlag("Active");
        cardStatus.setAccountStatus("000");
        cardStatus.setPreviousExpiryDate("0000");
        creditCard.setCardStatus(cardStatus);
        cardInformationResponse.setCreditCards(List.of(creditCard));
        response.setData(cardInformationResponse);

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        PaymentCacheData.VisaData visaData = new PaymentCacheData.VisaData();
        visaData.setCurrencyId("764");
        visaData.setMerchantId("*************");
        visaData.setMerchantName("Test Merchant");
        visaData.setFeeFlat("25.00");
        visaData.setFeePercentage(null);

        paymentCacheData.setVisaData(visaData);

        when(cacheService.get(anyString())).thenReturn(TMBUtils.convertJavaObjectToString(paymentCacheData));

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        when(transactionLimitService.fetchTransactionLimit(anyString(), anyString()))
                .thenReturn(customerCrmProfile);

        CommonAuthenResult commonAuthenResult = new CommonAuthenResult();
        commonAuthenResult.setRequireCommonAuthen(false);
        when(billPaymentValidateTransaction.validateIsRequireCommonAuthen(any(), anyString(), anyBoolean(), any()))
                .thenReturn(commonAuthenResult);

        when(retailLendingBizServiceClient
                .getCustomerCreditCard(anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(ResponseEntity.ok(response));
        
        VisaQRPayVerifyResponse result = v2VisaPayService.validate(correlationId, crmId, headers, request);
        
        assertNotNull(result);
        assertEquals(new BigDecimal("25.00"), result.getFee());
    }

    @Test
    void testPercentageFeeWhenValidateThenCalculateFromAmount() throws SQLException, TMBCommonException, JsonProcessingException {
        VisaQRPayVerifyRequest request = new VisaQRPayVerifyRequest();
        request.setTransId("transId");
        request.setCardNumber("**************");
        request.setAccountNumber("**************");
        request.setAmount("1000.00");

        TmbOneServiceResponse<CreditCardInformationResponse> response = new TmbOneServiceResponse<>();
        CreditCardInformationResponse cardInformationResponse = new CreditCardInformationResponse();
        CreditCard creditCard = new CreditCard();
        creditCard.setCardNo("**************");
        creditCard.setCardName("Test Card");
        creditCard.setAccountId("**************");
        creditCard.setAllowFromForBillPayTopUpEpayment("1");
        creditCard.setAccountStatus("Active");
        CardStatus cardStatus = new CardStatus();
        cardStatus.setCardActiveFlag("Active");
        cardStatus.setAccountStatus("000");
        cardStatus.setPreviousExpiryDate("0000");
        creditCard.setCardStatus(cardStatus);
        cardInformationResponse.setCreditCards(List.of(creditCard));
        response.setData(cardInformationResponse);

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        PaymentCacheData.VisaData visaData = new PaymentCacheData.VisaData();
        visaData.setCurrencyId("764");
        visaData.setMerchantId("*************");
        visaData.setMerchantName("Test Merchant");
        visaData.setFeeFlat(null);
        visaData.setFeePercentage("2.5");

        paymentCacheData.setVisaData(visaData);

        when(cacheService.get(anyString())).thenReturn(TMBUtils.convertJavaObjectToString(paymentCacheData));

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        when(transactionLimitService.fetchTransactionLimit(anyString(), anyString()))
                .thenReturn(customerCrmProfile);

        CommonAuthenResult commonAuthenResult = new CommonAuthenResult();
        commonAuthenResult.setRequireCommonAuthen(false);
        when(billPaymentValidateTransaction.validateIsRequireCommonAuthen(any(), anyString(), anyBoolean(), any()))
                .thenReturn(commonAuthenResult);

        when(retailLendingBizServiceClient
                .getCustomerCreditCard(anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(ResponseEntity.ok(response));
        
        VisaQRPayVerifyResponse result = v2VisaPayService.validate(correlationId, crmId, headers, request);
        
        assertNotNull(result);
        assertEquals(new BigDecimal("25.00"), result.getFee());
    }

    @Test
    void testConfirmRequestWhenConfirmThenSuccess() throws JsonProcessingException, TMBCommonException {
        VisaQRPayConfirmRequest visaQRPayConfirmRequest = new VisaQRPayConfirmRequest();
        visaQRPayConfirmRequest.setTransId("TEST");

        VisaQRPayConfirmResponse response = new VisaQRPayConfirmResponse();

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        PaymentCacheData.VisaData visaData = new PaymentCacheData.VisaData();
        visaData.setMerchantId("*************");
        visaData.setBusinessCode("1234");
        visaData.setCurrencyId("764");
        visaData.setMerchantCountry("TH");
        visaData.setMerchantName("Test Merchant");
        visaData.setMerchantCity("Bangkok");
        visaData.setPurchaseIdentifier("purchaseId");
        visaData.setMerchantRefId("refId");
        visaData.setTerminalId("termId");
        visaData.setAccountId("**************");
        visaData.setFeeFlat("25.00");

        paymentCacheData.setVisaData(visaData);
        paymentCacheData.setAmount("1000.00");
        paymentCacheData.setRequireCommonAuthentication(false);

        when(cacheService.get(eq("TEST")))
                .thenReturn(TMBUtils.convertJavaObjectToString(paymentCacheData));

        when(commonPaymentService.getCurrentDateTime()).thenReturn("**************");

        TmbOneServiceResponse<Void> voidResponseSuccess = new TmbOneServiceResponse<>();
        voidResponseSuccess.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService()));

        ResponseEntity<TmbOneServiceResponse<Void>> resResultSuccess = new ResponseEntity<>(
                voidResponseSuccess, HttpStatus.OK);

        when(paymentServiceFeignClient.visaConfirm(any(), any(), any())).thenReturn(resResultSuccess);

        try (MockedStatic<Transaction> transactionStatic = mockStatic(Transaction.class)) {
            transactionStatic
                    .when(() -> Transaction.getTransactionId(PaymentServiceConstant.TRANSFER_REFERENCE_NUMBER_PREFIX,
                            PaymentServiceConstant.EIGHT_INT))
                    .thenReturn(REFERENCE_CODE);
            
            VisaQRPayConfirmResponse result = v2VisaPayService.confirm(crmId, correlationId, headers, response, visaQRPayConfirmRequest);
            
            assertNotNull(result);
            assertEquals(REFERENCE_CODE, result.getReferenceID());
            assertEquals("**************", result.getTransactionDateTime());
            assertNotNull(result.getPaymentCacheData());
        }
    }

    @Test
    void testCommonAuthRequiredWhenConfirmThenVerifyAuthentication() throws JsonProcessingException, TMBCommonException {
        VisaQRPayConfirmRequest visaQRPayConfirmRequest = new VisaQRPayConfirmRequest();
        visaQRPayConfirmRequest.setTransId("TEST");

        VisaQRPayConfirmResponse response = new VisaQRPayConfirmResponse();

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        PaymentCacheData.VisaData visaData = new PaymentCacheData.VisaData();
        visaData.setMerchantId("*************");
        visaData.setBusinessCode("1234");
        visaData.setCurrencyId("764");
        visaData.setMerchantCountry("TH");
        visaData.setMerchantName("Test Merchant");
        visaData.setMerchantCity("Bangkok");
        visaData.setPurchaseIdentifier("purchaseId");
        visaData.setMerchantRefId("refId");
        visaData.setTerminalId("termId");
        visaData.setAccountId("**************");
        visaData.setFeeFlat("25.00");

        paymentCacheData.setVisaData(visaData);
        paymentCacheData.setAmount("1000.00");
        paymentCacheData.setRequireCommonAuthentication(true);

        CommonAuthenticationInformation authInfo = new CommonAuthenticationInformation();
        authInfo.setFlowName(COMMON_AUTH_BILL_FLOW_NAME);
        paymentCacheData.setCommonAuthenticationInformation(authInfo);

        when(cacheService.get(eq("TEST")))
                .thenReturn(TMBUtils.convertJavaObjectToString(paymentCacheData));

        when(commonPaymentService.getCurrentDateTime()).thenReturn("**************");
        
        doNothing().when(oauthService).verifyCommonAuthenWithPayload(eq(headers), any(CommonAuthenWithPayloadRequest.class));

        TmbOneServiceResponse<Void> voidResponseSuccess = new TmbOneServiceResponse<>();
        voidResponseSuccess.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService()));

        ResponseEntity<TmbOneServiceResponse<Void>> resResultSuccess = new ResponseEntity<>(
                voidResponseSuccess, HttpStatus.OK);

        when(paymentServiceFeignClient.visaConfirm(any(), any(), any())).thenReturn(resResultSuccess);

        try (MockedStatic<Transaction> transactionStatic = mockStatic(Transaction.class)) {
            transactionStatic
                    .when(() -> Transaction.getTransactionId(PaymentServiceConstant.TRANSFER_REFERENCE_NUMBER_PREFIX,
                            PaymentServiceConstant.EIGHT_INT))
                    .thenReturn(REFERENCE_CODE);
            
            VisaQRPayConfirmResponse result = v2VisaPayService.confirm(crmId, correlationId, headers, response, visaQRPayConfirmRequest);
            
            assertNotNull(result);
            
            verify(oauthService).verifyCommonAuthenWithPayload(eq(headers), any(CommonAuthenWithPayloadRequest.class));
        }
    }

    @Test
    void testNoCacheDataWhenConfirmThenThrowNotFoundException() {
        VisaQRPayConfirmRequest visaQRPayConfirmRequest = new VisaQRPayConfirmRequest();
        visaQRPayConfirmRequest.setTransId("nonExistentTransId");

        VisaQRPayConfirmResponse response = new VisaQRPayConfirmResponse();

        when(cacheService.get(anyString())).thenReturn(null);
        
        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v2VisaPayService.confirm(crmId, correlationId, headers, response, visaQRPayConfirmRequest));

        assertEquals(ResponseCode.NOT_FOUND.getCode(), exception.getErrorCode());
    }

    @Test
    void testFeignExceptionWhenConfirmThenHandleError() throws JsonProcessingException, TMBCommonException {
        VisaQRPayConfirmRequest visaQRPayConfirmRequest = new VisaQRPayConfirmRequest();
        visaQRPayConfirmRequest.setTransId("TEST");

        VisaQRPayConfirmResponse response = new VisaQRPayConfirmResponse();

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        PaymentCacheData.VisaData visaData = new PaymentCacheData.VisaData();
        visaData.setMerchantId("*************");
        visaData.setBusinessCode("1234");
        visaData.setCurrencyId("764");
        visaData.setMerchantCountry("TH");
        visaData.setMerchantName("Test Merchant");
        visaData.setMerchantCity("Bangkok");
        visaData.setPurchaseIdentifier("purchaseId");
        visaData.setAccountId("**************");

        paymentCacheData.setVisaData(visaData);
        paymentCacheData.setAmount("1000.00");
        paymentCacheData.setRequireCommonAuthentication(false);

        when(cacheService.get(eq("TEST")))
                .thenReturn(TMBUtils.convertJavaObjectToString(paymentCacheData));

        when(commonPaymentService.getCurrentDateTime()).thenReturn("**************");
        
        FeignException.BadRequest badRequestException = mock(FeignException.BadRequest.class);
        TmbOneServiceResponse<Object> errorResponse = new TmbOneServiceResponse<>();
        TmbStatus errorStatus = new TmbStatus();
        errorStatus.setCode(ResponseCode.QR_VISA_ERR_94101.getCode());
        errorStatus.setMessage("Error message");
        errorStatus.setService("card-scan-to-pay-async");
        errorResponse.setStatus(errorStatus);

        ByteBuffer buffer = ByteBuffer.wrap(TMBUtils.convertJavaObjectToString(errorResponse)
                .getBytes(StandardCharsets.UTF_8));

        when(badRequestException.responseBody()).thenReturn(Optional.of(buffer));
        when(paymentServiceFeignClient.visaConfirm(any(), any(), any())).thenThrow(badRequestException);

        try (MockedStatic<Transaction> transactionStatic = mockStatic(Transaction.class)) {
            transactionStatic
                    .when(() -> Transaction.getTransactionId(PaymentServiceConstant.TRANSFER_REFERENCE_NUMBER_PREFIX,
                            PaymentServiceConstant.EIGHT_INT))
                    .thenReturn(REFERENCE_CODE);
            
            TMBCommonException exception = assertThrows(TMBCommonException.class,
                    () -> v2VisaPayService.confirm(crmId, correlationId, headers, response, visaQRPayConfirmRequest));

            assertEquals(ResponseCode.QR_VISA_ERR_94101.getCode(), exception.getErrorCode());
        }
    }

    @Test
    void testConfirmationCompletedWhenPostConfirmProcessThenProcessLogs() {
        PaymentCacheData paymentCacheData = new PaymentCacheData();
        PaymentCacheData.VisaData visaData = new PaymentCacheData.VisaData();
        visaData.setMerchantId("*************");
        visaData.setMerchantName("Test Merchant");
        visaData.setMerchantRefId("refId");
        visaData.setTerminalId("termId");
        visaData.setAccountId("**************");

        paymentCacheData.setVisaData(visaData);
        paymentCacheData.setAmount("1000.00");
        paymentCacheData.setFeeBillpay("25.00");
        paymentCacheData.setCardNumber("**************");

        VisaQRPayConfirmResponse response = new VisaQRPayConfirmResponse();
        response.setPaymentCacheData(paymentCacheData);
        response.setReferenceID("**********");
        response.setTransactionDateTime("**************");
        response.setStatus("success");

        when(commonPaymentService.getCurrentDateTime()).thenReturn("**************");
        
        assertDoesNotThrow(() -> v2VisaPayService.postConfirmProcess(correlationId, crmId, response, headers, new VisaQRPayConfirmRequest()));
        
        verify(logService).saveLogFinancialAndTransactionEvent(eq(correlationId), any(), any());
    }

    @Test
    void testVisaDataWhenExtractTag62DataThenCollectNonBlankFields() throws JsonProcessingException, TMBCommonException {
        VisaQRPayConfirmRequest visaQRPayConfirmRequest = new VisaQRPayConfirmRequest();
        visaQRPayConfirmRequest.setTransId("TEST");

        VisaQRPayConfirmResponse response = new VisaQRPayConfirmResponse();

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        PaymentCacheData.VisaData visaData = new PaymentCacheData.VisaData();
        visaData.setMerchantId("*************");
        visaData.setBusinessCode("1234");
        visaData.setCurrencyId("764");
        visaData.setMerchantCountry("TH");
        visaData.setMerchantName("Test Merchant");
        visaData.setMerchantCity("Bangkok");
        
        visaData.setPurchaseIdentifier("purchaseId");
        visaData.setMobileNumber("");
        visaData.setStoreId("storeId");
        visaData.setLoyaltyNumber(null);
        visaData.setMerchantRefId("refId");
        visaData.setConsumerId("");
        visaData.setTerminalId("termId");
        visaData.setPurposeOfTransaction(null);

        visaData.setAccountId("**************");
        visaData.setFeeFlat("25.00");

        paymentCacheData.setVisaData(visaData);
        paymentCacheData.setAmount("1000.00");
        paymentCacheData.setRequireCommonAuthentication(false);

        when(cacheService.get(eq("TEST")))
                .thenReturn(TMBUtils.convertJavaObjectToString(paymentCacheData));

        when(commonPaymentService.getCurrentDateTime()).thenReturn("**************");

        TmbOneServiceResponse<Void> voidResponseSuccess = new TmbOneServiceResponse<>();
        voidResponseSuccess.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService()));

        ResponseEntity<TmbOneServiceResponse<Void>> resResultSuccess = new ResponseEntity<>(
                voidResponseSuccess, HttpStatus.OK);

        when(paymentServiceFeignClient.visaConfirm(any(), any(), any())).thenReturn(resResultSuccess);

        try (MockedStatic<Transaction> transactionStatic = mockStatic(Transaction.class)) {
            transactionStatic
                    .when(() -> Transaction.getTransactionId(PaymentServiceConstant.TRANSFER_REFERENCE_NUMBER_PREFIX,
                            PaymentServiceConstant.EIGHT_INT))
                    .thenReturn(REFERENCE_CODE);
            
            v2VisaPayService.confirm(crmId, correlationId, headers, response, visaQRPayConfirmRequest);
            
            verify(paymentServiceFeignClient).visaConfirm(eq(correlationId), eq(crmId), any(VisaConfirmRequest.class));
        }
    }

    @Test
    void testConfirmationResponseWhenBuildConfirmResponseThenFormatCorrectly() {
        VisaQRPayConfirmResponse visaResponse = new VisaQRPayConfirmResponse();
        visaResponse.setReferenceID("123456789");
        visaResponse.setTransactionDateTime(String.valueOf(System.currentTimeMillis()));
        
        com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse result = v2VisaPayService.buildConfirmResponse(visaResponse);
        
        assertNotNull(result);
        assertEquals("123456789", result.getReference1());
        assertNull(result.getRemainingBalance());
        assertNull(result.getQr());
        assertNotNull(result.getTopUpCreatedDatetime());
    }

    @Test
    void testStatusInCacheWhenPullingResultThenReturnStatusCode() throws JsonProcessingException, TMBCommonException {
        ScsResponse scsResponse = new ScsResponse();
        ScsStatus status = new ScsStatus();
        status.setStatusCode("0");
        scsResponse.setStatus(status);

        when(cacheService.get(anyString())).thenReturn(TMBUtils.convertJavaObjectToString(scsResponse));

        AtomicInteger retryCount = new AtomicInteger(0);
        
        String result = v2VisaPayService.pullingResult(correlationId, "REF123", retryCount);
        
        assertEquals("0", result);
        assertEquals(1, retryCount.get());
    }

    @Test
    void testNoCacheDataWhenPullingResultThenThrowException() {
        when(cacheService.get(anyString())).thenReturn(null);

        AtomicInteger retryCount = new AtomicInteger(0);
        
        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v2VisaPayService.pullingResult(correlationId, "REF123", retryCount));

        assertEquals("Result data not found in cache", exception.getMessage());
        assertEquals(1, retryCount.get());
    }

    @ParameterizedTest
    @CsvSource({
            "*************,true",   
            "****************,true",
            "123456789,false",      
            "**************56,false"
    })
    void testCardNumberWhenIsValidLuhnThenReturnCorrectResult(String cardNumber, boolean expected) {
        assertEquals(expected, V2VisaPayService.validateLuhnAlgorithm(cardNumber));
    }

    @Test
    void testCrmIdAndMerchantIdWhenGenerateTransactionRefThenProduceCorrectFormat() {
        String crmId = "001100000000000000000001184383";
        String merchantId = "*************";
        
        String result = v2VisaPayService.generateTransactionRef(crmId, merchantId);
        
        assertTrue(result.startsWith("VISA_QR_" + crmId + "_" + merchantId + "_"));
        assertTrue(result.length() > (9 + crmId.length() + merchantId.length()));
    }

    @Test
    void testCacheHitWhenGetPaymentCacheDataThenReturnFromCache() throws JsonProcessingException {
        PaymentCacheData expectedData = new PaymentCacheData();
        expectedData.setAmount("1000.00");

        when(cacheService.get(anyString())).thenReturn(TMBUtils.convertJavaObjectToString(expectedData));
        
        PaymentCacheData result = ReflectionTestUtils.invokeMethod(v2VisaPayService, 
                "retrievePaymentCacheData", correlationId, "cacheKey");
        
        assertNotNull(result);
        assertEquals("1000.00", result.getAmount());
    }

    @Test
    void testCacheMissWhenGetPaymentCacheDataThenRetrieveFromSecondary() {
        when(cacheService.get(anyString())).thenReturn(null);

        PaymentCacheData expectedData = new PaymentCacheData();
        expectedData.setAmount("1000.00");

        TmbOneServiceResponse<PaymentCacheData> secondaryResponse = new TmbOneServiceResponse<>();
        secondaryResponse.setData(expectedData);

        when(paymentServiceFeignClient.<PaymentCacheData>getDraftDataFromSecondary(anyString(), anyString()))
                .thenReturn(ResponseEntity.ok(secondaryResponse));
        
        PaymentCacheData result = ReflectionTestUtils.invokeMethod(v2VisaPayService, 
                "retrievePaymentCacheData", correlationId, "cacheKey");
        
        assertNotNull(result);
        assertEquals("1000.00", result.getAmount());
    }
    
    @Test
    void testQRVisaErrorCodeWhenHandleVisaErrorThenReturnCorrectException() throws JsonProcessingException {
        FeignException.BadRequest exception = mock(FeignException.BadRequest.class);

        TmbOneServiceResponse<Object> serviceResponse = new TmbOneServiceResponse<>();
        TmbStatus tmbStatus = new TmbStatus();
        tmbStatus.setCode(ResponseCode.QR_VISA_ERR_94101.getCode());
        serviceResponse.setStatus(tmbStatus);

        ByteBuffer buffer = ByteBuffer.wrap(TMBUtils.convertJavaObjectToString(serviceResponse)
                .getBytes(StandardCharsets.UTF_8));
        when(exception.responseBody()).thenReturn(Optional.of(buffer));

        VisaQRPayConfirmResponse response = new VisaQRPayConfirmResponse();
        
        TMBCommonException result = V2VisaPayService.handleVisaError(exception, response);
        
        assertEquals(ResponseCode.QR_VISA_ERR_94101.getCode(), result.getErrorCode());
    }

    @Test
    void testCardScanToPayAsyncServiceWhenHandleVisaErrorThenReturnFailedException() throws JsonProcessingException {
        FeignException.BadRequest exception = mock(FeignException.BadRequest.class);

        TmbOneServiceResponse<Object> serviceResponse = new TmbOneServiceResponse<>();
        TmbStatus tmbStatus = new TmbStatus();
        tmbStatus.setCode("SOME_ERROR");
        tmbStatus.setService("card-scan-to-pay-async");
        serviceResponse.setStatus(tmbStatus);

        ByteBuffer buffer = ByteBuffer.wrap(TMBUtils.convertJavaObjectToString(serviceResponse)
                .getBytes(StandardCharsets.UTF_8));
        when(exception.responseBody()).thenReturn(Optional.of(buffer));

        VisaQRPayConfirmResponse response = new VisaQRPayConfirmResponse();
        
        TMBCommonException result = V2VisaPayService.handleVisaError(exception, response);
        
        assertEquals(ResponseCode.FAILED.getCode(), result.getErrorCode());
        assertEquals(FIN_FAILED_STATUS, response.getStatus());
    }

    @Test
    void testValidateVisaDataWithReflection() {
        VisaQRPayVerifyRequest validRequest = new VisaQRPayVerifyRequest();
        validRequest.setTransId("transId");
        validRequest.setCardNumber("**************");
        validRequest.setAccountNumber("**************");
        validRequest.setAmount("1000.00");

        PaymentCacheData.VisaData validVisaData = new PaymentCacheData.VisaData();
        validVisaData.setCurrencyId("764");
        validVisaData.setMerchantId("*************");
        validVisaData.setPurchaseIdentifier("purchaseId");
        validVisaData.setMerchantRefId("refId");
        validVisaData.setTerminalId("termId");
        validVisaData.setSenderContract("contract");
        
        assertDoesNotThrow(() ->
                ReflectionTestUtils.invokeMethod(v2VisaPayService, "performVisaDataValidation", validRequest, validVisaData)
        );
        
        PaymentCacheData.VisaData invalidCurrencyData = new PaymentCacheData.VisaData();
        invalidCurrencyData.setCurrencyId("840");
        invalidCurrencyData.setMerchantId("*************");

        try {
            ReflectionTestUtils.invokeMethod(v2VisaPayService, "performVisaDataValidation", validRequest, invalidCurrencyData);
            fail("Expected exception");
        } catch (Exception ex) {
            Throwable cause = ex;
            if (ex instanceof java.lang.reflect.UndeclaredThrowableException) {
                cause = ex.getCause();
            }
            assertTrue(cause instanceof TMBCommonException);
            assertEquals(ResponseCode.QR_VISA_ERR_1111.getCode(), ((TMBCommonException)cause).getErrorCode());
        }
        
        PaymentCacheData.VisaData longTag62Data = new PaymentCacheData.VisaData();
        longTag62Data.setCurrencyId("764");
        longTag62Data.setMerchantId("*************");
        longTag62Data.setPurchaseIdentifier(StringUtils.leftPad("", 60, "X"));
        longTag62Data.setMerchantRefId(StringUtils.leftPad("", 20, "X"));
        longTag62Data.setTerminalId(StringUtils.leftPad("", 20, "X"));
        longTag62Data.setSenderContract(StringUtils.leftPad("", 20, "X"));

        try {
            ReflectionTestUtils.invokeMethod(v2VisaPayService, "performVisaDataValidation", validRequest, longTag62Data);
            fail("Expected exception");
        } catch (Exception ex) {
            Throwable cause = ex;
            if (ex instanceof java.lang.reflect.UndeclaredThrowableException) {
                cause = ex.getCause();
            }
            assertTrue(cause instanceof TMBCommonException);
            assertEquals(ResponseCode.QR_VISA_ERR_1111.getCode(), ((TMBCommonException)cause).getErrorCode());
        }
        
        PaymentCacheData.VisaData invalidMerchantIdData = new PaymentCacheData.VisaData();
        invalidMerchantIdData.setCurrencyId("764");
        invalidMerchantIdData.setMerchantId("**********");

        try {
            ReflectionTestUtils.invokeMethod(v2VisaPayService, "performVisaDataValidation", validRequest, invalidMerchantIdData);
            fail("Expected exception");
        } catch (Exception ex) {
            Throwable cause = ex;
            if (ex instanceof java.lang.reflect.UndeclaredThrowableException) {
                cause = ex.getCause();
            }
            assertTrue(cause instanceof TMBCommonException);
            assertEquals(ResponseCode.QR_VISA_ERR_1111.getCode(), ((TMBCommonException)cause).getErrorCode());
        }
        
        VisaQRPayVerifyRequest invalidRequest = new VisaQRPayVerifyRequest();

        try (MockedStatic<Validation> validationMock = mockStatic(Validation.class)) {
            ValidatorFactory mockFactory = mock(ValidatorFactory.class);
            Validator mockValidator = mock(Validator.class);
            Set<ConstraintViolation<VisaQRPayVerifyRequest>> violations = mock(Set.class);

            validationMock.when(Validation::buildDefaultValidatorFactory).thenReturn(mockFactory);
            when(mockFactory.getValidator()).thenReturn(mockValidator);
            when(mockValidator.validate(invalidRequest)).thenReturn(violations);
            when(violations.isEmpty()).thenReturn(false);

            try {
                ReflectionTestUtils.invokeMethod(v2VisaPayService, "performVisaDataValidation", invalidRequest, validVisaData);
                fail("Expected exception");
            } catch (Exception ex) {
                Throwable cause = ex;
                if (ex instanceof java.lang.reflect.UndeclaredThrowableException) {
                    cause = ex.getCause();
                }
                assertTrue(cause instanceof TMBCommonException);
                assertEquals(ResponseCode.QR_VISA_ERR_1111.getCode(), ((TMBCommonException)cause).getErrorCode());
            }
        }
        
        try (MockedStatic<Validation> validationMock = mockStatic(Validation.class)) {
            validationMock.when(Validation::buildDefaultValidatorFactory).thenThrow(new RuntimeException("Validation factory error"));

            try {
                ReflectionTestUtils.invokeMethod(v2VisaPayService, "performVisaDataValidation", validRequest, validVisaData);
                fail("Expected exception");
            } catch (Exception ex) {
                Throwable cause = ex;
                if (ex instanceof java.lang.reflect.UndeclaredThrowableException) {
                    cause = ex.getCause();
                }
                assertTrue(cause instanceof TMBCommonException);
                assertEquals(ResponseCode.QR_VISA_ERR_1111.getCode(), ((TMBCommonException)cause).getErrorCode());
            }
        }
    }
    
    @Test
    void testOtherErrorWhenHandleVisaErrorThenReturnBillPayError() throws JsonProcessingException {
        FeignException.BadRequest exception = mock(FeignException.BadRequest.class);

        TmbOneServiceResponse<Object> serviceResponse = new TmbOneServiceResponse<>();
        TmbStatus tmbStatus = new TmbStatus();
        tmbStatus.setCode("SOME_ERROR");
        tmbStatus.setService("some-other-service");
        serviceResponse.setStatus(tmbStatus);

        ByteBuffer buffer = ByteBuffer.wrap(TMBUtils.convertJavaObjectToString(serviceResponse)
                .getBytes(StandardCharsets.UTF_8));
        when(exception.responseBody()).thenReturn(Optional.of(buffer));

        VisaQRPayConfirmResponse response = new VisaQRPayConfirmResponse();
        
        TMBCommonException result = V2VisaPayService.handleVisaError(exception, response);
        
        assertEquals(ResponseCode.PAY_BILL_ERR_2300.getCode(), result.getErrorCode());
        assertEquals(FIN_FAILED_STATUS, response.getStatus());
    }

    @ParameterizedTest
    @CsvSource({
            "true,true,true,true",
            "true,false,true,false",
            "false,true,false,true",
            "false,false,false,false"
    })
    void testCommonAuthenResultWhenSetDdpFlagAndPinFreeFlagThenSetCorrectly(
            Boolean ddpFlag, boolean pinFree, Boolean expectedDdp, boolean expectedPinFree) {
        ActivityVisaQRPayVerifyEvent activityEvent = new ActivityVisaQRPayVerifyEvent("TEST", new HttpHeaders());

        CommonAuthenResult commonAuthenResult = new CommonAuthenResult();
        commonAuthenResult.setIsForceFR(ddpFlag);
        commonAuthenResult.setPinFree(pinFree);
        
        activityEvent.setDdpFlagAndPinFreeFlag(commonAuthenResult);
        
        assertEquals(String.valueOf(expectedDdp), activityEvent.getIsForceFr());
        assertEquals(String.valueOf(expectedPinFree), activityEvent.getPinFreeFlag());
    }

    @Test
    void handlePullingResultFailure() throws JsonProcessingException {
        TmbOneServiceResponse<ScsResponse> response = new TmbOneServiceResponse<>();
        response.setData(new ScsResponse());
        when(cacheService.get(anyString()))
                .thenReturn(TMBUtils.convertJavaObjectToString(new ScsResponse()))
                .thenReturn(null)
                .thenThrow(FeignException.class);
        lenient().when(paymentServiceFeignClient.<ScsResponse>getDraftDataFromSecondary(anyString(), any()))
                .thenReturn(ResponseEntity.ok(response))
                .thenReturn(null)
                .thenThrow(FeignException.class);
        assertNotNull(v2VisaPayService.handlePullingResultFailure
                (new TMBCommonException(""), correlationId, "", new AtomicInteger()));
        assertNotNull(v2VisaPayService.handlePullingResultFailure
                (new TMBCommonException(""), correlationId, "", new AtomicInteger()));
        assertNotNull(v2VisaPayService.handlePullingResultFailure
                (new TMBCommonException(""), correlationId, "", new AtomicInteger()));
        assertNotNull(v2VisaPayService.handlePullingResultFailure
                (new TMBCommonException(""), correlationId, "", new AtomicInteger()));
    }

}