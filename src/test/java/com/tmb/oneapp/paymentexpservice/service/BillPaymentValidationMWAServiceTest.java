package com.tmb.oneapp.paymentexpservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.paymentexpservice.client.CustomerExpFeignClient;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.factory.Factory;
import com.tmb.oneapp.paymentexpservice.model.ActivityTopUpEvent;
import com.tmb.oneapp.paymentexpservice.model.AdditionalParam;
import com.tmb.oneapp.paymentexpservice.model.BillerInfoResponse;
import com.tmb.oneapp.paymentexpservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.CreditCardAccount;
import com.tmb.oneapp.paymentexpservice.model.CreditCardResponse;
import com.tmb.oneapp.paymentexpservice.model.DepositAccount;
import com.tmb.oneapp.paymentexpservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.paymentexpservice.model.OCPAccountPayment;
import com.tmb.oneapp.paymentexpservice.model.OCPBalance;
import com.tmb.oneapp.paymentexpservice.model.OCPBillPayment;
import com.tmb.oneapp.paymentexpservice.model.OCPBillPaymentConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.OCPFee;
import com.tmb.oneapp.paymentexpservice.model.ReferenceTopUpResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityBillPayVerifyEvent;
import com.tmb.oneapp.paymentexpservice.model.commonauth.VerifyTransactionResult;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CardInfo;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCardDetail;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.EteOnlinePaymentResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.MWABillPaymentDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.MWAValidationResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.OnlinePmt;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.OnlinePmtRec;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import com.tmb.oneapp.paymentexpservice.service.v1.V1AccountTopUpService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_COMP_CODE_MWA;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ONLINE_TRANS_REF_SEQUENCE;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TOP_UP_PAYMENT_SEQUENCE_DIGIT;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_UTILITIES_LEGACY_ENTER_PAGE;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_UTILITIES_LEGACY_REVIEW_PAGE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;

@ExtendWith(MockitoExtension.class)
class BillPaymentValidationMWAServiceTest {

    @Mock
    BillPaymentLegacyService billPaymentLegacyService;

    @Mock
    BillPaymentCustomBillerOCPService billPaymentCustomBillerOCPService;

    @Mock
    CommonPaymentService commonPaymentService;

    @Mock
    LogService logService;

    @Mock
    BillPaymentValidateTransaction billPaymentValidateTransaction;

    @Mock
    BillPaymentCreditCardService billPaymentCreditCardService;

    @Mock
    CustomerExpFeignClient customerExpFeignClient;

    @Mock
    V1AccountTopUpService v1AccountTopUpService;

    @InjectMocks
    BillPaymentValidationMWAService billPaymentValidationMWAService;

    String crmId;
    String correlationId;
    String transId;
    String paymentRequestRef1;
    HttpHeaders headers;
    ActivityTopUpEvent activityEvent;

    @BeforeEach
    void setUp() {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        transId = "BILLPAY_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        paymentRequestRef1 = "**********";
        activityEvent = new ActivityTopUpEvent("", "", "");
        headers = new HttpHeaders();
        headers.add(X_CRMID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);
    }

    private AdditionalParam generateAdditionalParam(String name, String value) {
        AdditionalParam additionalParam = new AdditionalParam();
        additionalParam.setName(name);
        additionalParam.setValue(value);
        return additionalParam;
    }

    private void mockGetTransRefBillPay() {
        Mockito.when(commonPaymentService.genTransRefBillPayRef(crmId, "1", "1", BILL_COMP_CODE_MWA))
                .thenReturn("trnas-**********");
    }

    private void mockGetCreditCareByAccountId() throws TMBCommonException {
        CreditCardDetail creditCardDetail = new CreditCardDetail();
        creditCardDetail.setCardId("477025XXXXXX0167");
        creditCardDetail.setProductId("VSOCHI");
        CardInfo cardInfo = new CardInfo();
        cardInfo.setCardEmbossingName1("test test");
        cardInfo.setExpiredBy("2906");
        creditCardDetail.setCardInfo(cardInfo);
        Mockito.when(billPaymentCreditCardService.getCreditCardDetailByAccountId("0000000050082630661000344", correlationId)).thenReturn(creditCardDetail);
    }

    private void mockGetAccountCreditCard() {
        TmbServiceResponse<CreditCardResponse> creditCardResponse = new TmbServiceResponse<>();
        CreditCardAccount creditCardAccount = new CreditCardAccount();
        creditCardAccount.setAccountId("0000000050082630661000344");
        creditCardResponse.setData(new CreditCardResponse(
                List.of(creditCardAccount),
                Collections.emptyList()
        ));
        Mockito.when(customerExpFeignClient.getAccountsCreditCard(correlationId, crmId)).thenReturn(ResponseEntity.ok(creditCardResponse));
    }

    private void mockGetTransactionID() {
        Mockito.when(commonPaymentService.getTransactionId(Mockito.anyString(), eq(TOP_UP_PAYMENT_SEQUENCE_DIGIT)))
                .thenReturn("pea000000001");
    }

    private void mockFetchBillDetails() throws TMBCommonException {
        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        billerInfoResponse.setNameEn("MWA");
        billerInfoResponse.setFee(new BigDecimal("10.00"));
        billerInfoResponse.setToAccountId("**********");
        billerInfoResponse.setPaymentMethod("1");
        billerInfoResponse.setBillerMethod("1");
        billerInfoResponse.setBillerCompCode(BILL_COMP_CODE_MWA);
        billerInfoResponse.setBillerCategoryCode("11");
        billerInfoResponse.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        ReferenceTopUpResponse referenceTopUpResponse = new ReferenceTopUpResponse();
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfoResponse);
        billerDetail.setRef1(referenceTopUpResponse);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(BILL_COMP_CODE_MWA, correlationId)).thenReturn(billerDetail);
    }

    private void mockCallOCPValidation() throws TMBCommonException {
        OCPBillPaymentConfirmRequest ocpBillPaymentConfirmRequest = new OCPBillPaymentConfirmRequest();
        ocpBillPaymentConfirmRequest.setMerchant(null);
        ocpBillPaymentConfirmRequest.setFromAccount(new OCPAccountPayment());

        String msg = "4|1|3301967|0|6102.49|04|2|1114503|0|6624.22|04|3|2666055|0|6940.42|04|4|3301967|0.98|14.02|02";
        OnlinePmt onlinePmtKeys = new OnlinePmt();
        onlinePmtKeys.setRef2("11.00");
        onlinePmtKeys.setEffDt("2021-06-10T18:53:52+07:00");
        onlinePmtKeys.setAmt("300.00");
        onlinePmtKeys.setMsg(msg);
        onlinePmtKeys.setOcpBillPaymentConfirmRequest(ocpBillPaymentConfirmRequest);

        OnlinePmtRec onlinePmtRec = new OnlinePmtRec();
        onlinePmtRec.setOnlinePmtInfo(onlinePmtKeys);
        onlinePmtRec.setOnlinePmtKeys(onlinePmtKeys);

        AdditionalParam additionalParam = new AdditionalParam();
        additionalParam.setName("Message");
        additionalParam.setValue(msg);

        AdditionalParam additionalParamAmt = new AdditionalParam();
        additionalParamAmt.setName("TotalNetAmount");
        additionalParamAmt.setValue("300.00");
        List<AdditionalParam> additionalParamList = List.of(additionalParam, additionalParamAmt);

        OCPBalance balance = new OCPBalance("300.00", "300.00");
        OCPBillPayment ocpBillPaymentResponse = new OCPBillPayment()
                .setRbaNo("********")
                .setRequestId("a962003a-6fa7-48b1-81d2-5df3329f4ff7")
                .setRequestDateTime("2022-06-20T10:48:26+07:00")
                .setBankRefId("2022062010482618300")
                .setPaymentId("2022062010482618300")
                .setRef1("*********")
                .setRef2("************")
                .setRef3("0.00")
                .setRef4("")
                .setAmount("40.90")
                .setCurrency("THB")
                .setFromAccount(new OCPAccountPayment())
                .setToAccount(new OCPAccountPayment())
                .setFee(new OCPFee())
                .setAdditionalParams(additionalParamList)
                .setBalance(balance);
        onlinePmtRec.setOcpBillPayment(ocpBillPaymentResponse);

        EteOnlinePaymentResponse eteOnlinePaymentResponse = new EteOnlinePaymentResponse();
        eteOnlinePaymentResponse.setOnlinePmtRec(onlinePmtRec);
        Mockito.when(billPaymentCustomBillerOCPService.callOCPValidation(Mockito.any())).thenReturn(onlinePmtRec);
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen() throws TMBCommonException {
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(HttpHeaders.class), any(BigDecimal.class), anyBoolean(), any())).thenReturn(new VerifyTransactionResult(true, new FaceRecognizeResponse().setIsRequireFr(true).setPaymentAccuUsgAmt(BigDecimal.ZERO), new CommonAuthenResult()));
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen() throws TMBCommonException {
        CommonAuthenResult commonAuthenResult = new CommonAuthenResult()
                .setRequireCommonAuthen(true)
                .setIsForceFR(true)
                .setPinFree(true);
        VerifyTransactionResult resultWhenExecuteCommonAuthen = new VerifyTransactionResult(false, null, commonAuthenResult);
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any())).thenReturn(resultWhenExecuteCommonAuthen);
    }

    @Test
    void getPaymentDetailShouldSuccessTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, JsonProcessingException {
        Mockito.when(commonPaymentService.getTransactionId(ONLINE_TRANS_REF_SEQUENCE, TOP_UP_PAYMENT_SEQUENCE_DIGIT))
                .thenReturn("ref000000001");

        BillerTopUpDetailResponse billerTopUpDetailResponse = new BillerTopUpDetailResponse();
        billerTopUpDetailResponse.setBillerInfo(new BillerInfoResponse()
                .setExpiredDate("9999-12-31T00:00:00.000000+07:00")
        );
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(Mockito.anyString(), Mockito.anyString())).thenReturn(billerTopUpDetailResponse);

        AdditionalParam msg = generateAdditionalParam("Message", "4|1|3301967|0|6102.49|04|2|1114503|0|6624.22|04|3|2666055|0|6940.42|04|4|3301967|0.98|14.02|02");
        AdditionalParam cName = generateAdditionalParam("CustomerName", "customer name");
        AdditionalParam cAddr = generateAdditionalParam("CustomerAddress", "address");

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setBalance(new OCPBalance("100.00", "100.00"));
        ocpBillPayment.setRef2("1.00");
        ocpBillPayment.setAdditionalParams(List.of(msg, cName, cAddr));

        OnlinePmtRec response = new OnlinePmtRec();
        response.setOcpBillPayment(ocpBillPayment);
        Mockito.when(billPaymentCustomBillerOCPService.callOCPValidation(Mockito.any())).thenReturn(response);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setBillerCompCode("2699");
        MWABillPaymentDetailResponse actual = (MWABillPaymentDetailResponse) billPaymentValidationMWAService.getPaymentDetail(correlationId, topUpVerifyRequest);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals("99.00", actual.getAmount());
        Assertions.assertEquals("1.00", actual.getVat());
        Assertions.assertEquals("3301967", actual.getBillNumber());
        Assertions.assertEquals("address", actual.getAddress());
        Assertions.assertEquals("customer name", actual.getCustomerName());
        Assertions.assertNull(actual.getScheduleConfig().getTabsOfSchedule());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isOnce());
        Assertions.assertFalse(actual.getScheduleConfig().getFrequency().isWeekly());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isMonthly());
        Assertions.assertNull(actual.getScheduleConfig().getPhrase().getTitleEnterPage());
        Assertions.assertEquals(PHRASE_BILLER_UTILITIES_LEGACY_ENTER_PAGE, actual.getScheduleConfig().getPhrase().getDetailEnterPage());
        Assertions.assertEquals(PHRASE_BILLER_UTILITIES_LEGACY_REVIEW_PAGE, actual.getScheduleConfig().getPhrase().getReviewPage());
    }

    @Test
    void validationShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        billerInfoResponse.setNameEn("MEA");
        billerInfoResponse.setFee(new BigDecimal("10.00"));
        billerInfoResponse.setToAccountId("**********");
        billerInfoResponse.setPaymentMethod("1");
        billerInfoResponse.setBillerMethod("1");
        billerInfoResponse.setBillerCompCode("2699");
        billerInfoResponse.setBillerCategoryCode("11");
        billerInfoResponse.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        ReferenceTopUpResponse referenceTopUpResponse = new ReferenceTopUpResponse();
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfoResponse);
        billerDetail.setRef1(referenceTopUpResponse);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail("2699", correlationId)).thenReturn(billerDetail);

        DepositAccount depositAccount = Factory.createDepositAccount();
        depositAccount.setWaiveFeeForBillpay("0");

        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(
                eq(crmId),
                eq(correlationId),
                eq("**********")
        )).thenReturn(depositAccount);

        Mockito.when(commonPaymentService.getTransactionId(ONLINE_TRANS_REF_SEQUENCE, TOP_UP_PAYMENT_SEQUENCE_DIGIT))
                .thenReturn("ref000000001");

        mockCallOCPValidation();

        CustomerCrmProfile dailyLimit = new CustomerCrmProfile();
        Mockito.when(billPaymentValidateTransaction.fetchTransactionLimit(correlationId, crmId)).thenReturn(dailyLimit);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        Mockito.when(commonPaymentService.genTransRefBillPayRef(crmId, "1", "1", "2699"))
                .thenReturn("trnas-**********");

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2699");
        request.setAccountNumber("**********");
        request.setToFavoriteName("ReqToFavoriteName");
        request.setAmount("300.00");
        request.setReference2("Have value some time if scan from QR");
        MWAValidationResponse actual = (MWAValidationResponse) billPaymentValidationMWAService.validation(request, headers);

        Assertions.assertEquals("310.00", actual.getNetAmount());
        Assertions.assertEquals(4, actual.getBill().size());
        Assertions.assertEquals("3301967", actual.getBill().get(0).getBillNumber());
        Assertions.assertEquals("6102.49", actual.getBill().get(0).getAmount());
        Assertions.assertEquals("0.00", actual.getBill().get(0).getVat());
        Assertions.assertEquals("1114503", actual.getBill().get(1).getBillNumber());
        Assertions.assertEquals("6624.22", actual.getBill().get(1).getAmount());
        Assertions.assertEquals("0.00", actual.getBill().get(1).getVat());

        ArgumentCaptor<OnlinePmt> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OnlinePmt.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.anyString(), dataSaveToCacheCaptor.capture());

        OnlinePmt actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals(request.getToFavoriteName(), actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        Assertions.assertEquals(depositAccount.getAccountName(), actualDataSaveToCache.getPaymentCacheData().getFromAccountName());
        Assertions.assertEquals(depositAccount.getProductNickname(), actualDataSaveToCache.getPaymentCacheData().getFromAccountNickname());

        ArgumentCaptor<ActivityBillPayVerifyEvent> activityEventCapture = ArgumentCaptor.forClass(ActivityBillPayVerifyEvent.class);
        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(activityEventCapture.capture());
        Assertions.assertNull(activityEventCapture.getValue().getReference2());
    }

    @Test
    void validationMWAWhenExecuteCommonAuthenticationShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen();

        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        billerInfoResponse.setNameEn("MEA");
        billerInfoResponse.setFee(new BigDecimal("10.00"));
        billerInfoResponse.setToAccountId("**********");
        billerInfoResponse.setPaymentMethod("1");
        billerInfoResponse.setBillerMethod("1");
        billerInfoResponse.setBillerCompCode("2699");
        billerInfoResponse.setBillerCategoryCode("11");
        billerInfoResponse.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        ReferenceTopUpResponse referenceTopUpResponse = new ReferenceTopUpResponse();
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfoResponse);
        billerDetail.setRef1(referenceTopUpResponse);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail("2699", correlationId)).thenReturn(billerDetail);

        DepositAccount depositAccount = Factory.createDepositAccount();
        depositAccount.setWaiveFeeForBillpay("0");

        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(
                eq(crmId),
                eq(correlationId),
                eq("**********")
        )).thenReturn(depositAccount);

        Mockito.when(commonPaymentService.getTransactionId(ONLINE_TRANS_REF_SEQUENCE, TOP_UP_PAYMENT_SEQUENCE_DIGIT))
                .thenReturn("ref000000001");

        mockCallOCPValidation();

        CustomerCrmProfile dailyLimit = new CustomerCrmProfile();
        Mockito.when(billPaymentValidateTransaction.fetchTransactionLimit(correlationId, crmId)).thenReturn(dailyLimit);

        Mockito.when(commonPaymentService.genTransRefBillPayRef(crmId, "1", "1", "2699"))
                .thenReturn("trnas-**********");

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2699");
        request.setAccountNumber("**********");
        request.setToFavoriteName("ReqToFavoriteName");
        request.setAmount("300.00");
        request.setReference2("Have value some time if scan from QR");

        MWAValidationResponse actual = (MWAValidationResponse) billPaymentValidationMWAService.validation(request, headers);

        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
    }

    @Test
    void getPaymentDetailMWAWhenBillerExpireShouldThrowsExceptionTest() throws TMBCommonException {
        BillerTopUpDetailResponse billerTopUpDetailResponse = new BillerTopUpDetailResponse();
        billerTopUpDetailResponse.setBillerInfo(new BillerInfoResponse()
                .setExpiredDate("2000-12-31T00:00:00.000000+07:00")
        );
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(Mockito.anyString(), Mockito.anyString())).thenReturn(billerTopUpDetailResponse);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setBillerCompCode("2700");
        TMBCommonException actualException = Assertions.assertThrows(TMBCommonException.class,
                () -> billPaymentValidationMWAService.getPaymentDetail(correlationId, topUpVerifyRequest));

        Assertions.assertEquals(ResponseCode.BILLER_EXPIRED.getCode(), actualException.getErrorCode());
    }

    @Test
    void validationMWAWhenBillerExpireShouldThrowsExceptionTest() throws TMBCommonException {
        BillerTopUpDetailResponse billerTopUpDetailResponse = new BillerTopUpDetailResponse();
        billerTopUpDetailResponse.setBillerInfo(new BillerInfoResponse()
                .setExpiredDate("2000-12-31T00:00:00.000000+07:00")
        );
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(Mockito.anyString(), Mockito.anyString())).thenReturn(billerTopUpDetailResponse);

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2700");
        TMBCommonException actualException = Assertions.assertThrows(TMBCommonException.class,
                () -> billPaymentValidationMWAService.validation(request, headers));

        Assertions.assertEquals(ResponseCode.BILLER_EXPIRED.getCode(), actualException.getErrorCode());
    }

    @Test
    void validationMWAWhenFromAccountCreditCardShouldSuccessTest() throws TMBCommonException, JsonProcessingException, SQLException, TMBCustomCommonExceptionWithResponse {
        mockFetchBillDetails();

        mockGetAccountCreditCard();

        mockGetCreditCareByAccountId();

        mockGetTransactionID();

        mockCallOCPValidation();

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        mockGetTransRefBillPay();

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode(BILL_COMP_CODE_MWA);
        request.setAccountNumber("0000000050082630661000344");
        request.setToFavoriteName("ReqToFavoriteName");
        request.setAmount("300.00");
        request.setIsCreditCard(true);
        MWAValidationResponse actual = (MWAValidationResponse) billPaymentValidationMWAService.validation(request, headers);

        Assertions.assertNotNull(actual.getIsRequireConfirmPin());
        Assertions.assertNotNull(actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());
        Assertions.assertEquals("300.00", actual.getNetAmount());

        ArgumentCaptor<OnlinePmt> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OnlinePmt.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.anyString(), dataSaveToCacheCaptor.capture());

        OnlinePmt actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertNotNull(actualDataSaveToCache.getOcpBillPaymentConfirmRequest());
    }

    @Test
    void validationPEAWhenFromAccountCreditCardShouldSuccessTest() throws TMBCommonException, JsonProcessingException, SQLException, TMBCustomCommonExceptionWithResponse {
        mockFetchBillDetails();

        mockGetAccountCreditCard();

        mockGetCreditCareByAccountId();

        mockGetTransactionID();

        mockCallOCPValidation();

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        mockGetTransRefBillPay();

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode(BILL_COMP_CODE_MWA);
        request.setAccountNumber("0000000050082630661000344");
        request.setToFavoriteName("ReqToFavoriteName");
        request.setAmount("300.00");
        request.setIsCreditCard(true);
        MWAValidationResponse actual = (MWAValidationResponse) billPaymentValidationMWAService.validation(request, headers);

        Assertions.assertNotNull(actual.getIsRequireConfirmPin());
        Assertions.assertNotNull(actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());
        Assertions.assertEquals("300.00", actual.getNetAmount());

        ArgumentCaptor<OnlinePmt> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OnlinePmt.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.anyString(), dataSaveToCacheCaptor.capture());

        OnlinePmt actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertNotNull(actualDataSaveToCache.getOcpBillPaymentConfirmRequest());
    }
}
