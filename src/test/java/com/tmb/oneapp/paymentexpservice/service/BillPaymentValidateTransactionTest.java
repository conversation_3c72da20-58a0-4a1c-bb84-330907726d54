package com.tmb.oneapp.paymentexpservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.CommonData;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.commonauth.CommonAuthForceFR;
import com.tmb.oneapp.paymentexpservice.model.commonauth.VerifyTransactionResult;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import com.tmb.oneapp.paymentexpservice.utils.CacheService;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.Map;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DAILY_LIMIT_TYPE_BILL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DAILY_LIMIT_TYPE_TRANSFER;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PIN_FREE_TYPE_BILL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PIN_FREE_TYPE_TRANSFER;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PRE_LOGIN;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BillPaymentValidateTransactionTest {
    @Mock
    TransactionLimitService transactionLimitService;

    @Mock
    ValidateDailyLimitPinFree validateDailyLimitPinFree;

    @Mock
    CommonService commonService;

    @Mock
    CacheService cacheService;

    @Mock
    ValidatorFaceRecognizeTransfer validatorFaceRecognizeTransfer;

    @Spy
    @InjectMocks
    BillPaymentValidateTransaction billPaymentValidateTransaction;

    @Mock
    OauthService oauthService;

    String crmId;
    String correlationId;
    String transId;
    String dailyLimitType;
    String pinFreeType;
    CustomerCrmProfile crmProfile;
    HttpHeaders headers;

    @BeforeEach
    void setUp() {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        transId = "TOPUP_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";

        headers = new HttpHeaders();
        headers.add(PaymentServiceConstant.X_CRMID, crmId);
        headers.add(PaymentServiceConstant.HEADER_CORRELATION_ID, correlationId);

        TopUpVerifyRequest billPayRequest = new TopUpVerifyRequest();
        billPayRequest.setAmount("100.50");

        crmProfile = new CustomerCrmProfile();
        crmProfile.setBillpayMaxLimitAmt(1000);
        crmProfile.setBillpayAccuUsgAmt(BigDecimal.valueOf(200.00));
        crmProfile.setPinFreeBpLimit(200.00);
        crmProfile.setPinFreeTxnCount(1);
        crmProfile.setPinFreeSeetingFlag("Y");

        crmProfile.setEbMaxLimitAmtCurrent(1000.00);
        crmProfile.setEbAccuUsgAmtDaily(200.00);
        crmProfile.setPinFreeTrLimit(200.00);
        crmProfile.setPaymentAccuUsgAmt(BigDecimal.ZERO);

        ReflectionTestUtils.setField(billPaymentValidateTransaction, "totalPaymentAccumulateUsageLimit", new BigDecimal(200000));
    }

    @Test
    void validateConfirmTransactionWhenDailyLimitTypeTransferAndPinFreeTypeBillShouldReturnCorrectlyTest() throws SQLException, TMBCommonException {
        dailyLimitType = DAILY_LIMIT_TYPE_TRANSFER;
        pinFreeType = PIN_FREE_TYPE_TRANSFER;

        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(crmProfile);

        when(validateDailyLimitPinFree.fetchPinFreeMaxCount(correlationId)).thenReturn("15");
        Double amount = 200.00;
        Map<String, Object> actual = billPaymentValidateTransaction
                .validateConfirmTransaction(headers, amount, transId, false, false, dailyLimitType, pinFreeType);

        Assertions.assertFalse((boolean) actual.get("isRequirePin"));
        CustomerCrmProfile dailyLimitActual = (CustomerCrmProfile) actual.get("DailyLimit");
        Assertions.assertEquals(1000.00, dailyLimitActual.getEbMaxLimitAmtCurrent());
        Assertions.assertEquals(200.00, dailyLimitActual.getEbAccuUsgAmtDaily());
        Assertions.assertEquals(200.00, dailyLimitActual.getPinFreeTrLimit());
        Assertions.assertEquals(1, dailyLimitActual.getPinFreeTxnCount());
        Assertions.assertEquals("Y", dailyLimitActual.getPinFreeSeetingFlag());
    }

    @Test
    void validateConfirmTransactionWhenDailyLimitTypeBillAndPinFreeTypeBillShouldReturnCorrectlyTest() throws SQLException, TMBCommonException {
        dailyLimitType = DAILY_LIMIT_TYPE_BILL;
        pinFreeType = PIN_FREE_TYPE_BILL;

        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(crmProfile);

        when(validateDailyLimitPinFree.fetchPinFreeMaxCount(correlationId)).thenReturn("15");
        Double amount = 200.00;
        Map<String, Object> actual = billPaymentValidateTransaction
                .validateConfirmTransaction(headers, amount, transId, false, false, dailyLimitType, pinFreeType);

        Assertions.assertFalse((boolean) actual.get("isRequirePin"));
        CustomerCrmProfile dailyLimitActual = (CustomerCrmProfile) actual.get("DailyLimit");
        Assertions.assertEquals(1000, dailyLimitActual.getBillpayMaxLimitAmt());
        Assertions.assertEquals(BigDecimal.valueOf(200.00), dailyLimitActual.getBillpayAccuUsgAmt());
        Assertions.assertEquals(200.00, dailyLimitActual.getPinFreeBpLimit());
        Assertions.assertEquals(1, dailyLimitActual.getPinFreeTxnCount());
        Assertions.assertEquals("Y", dailyLimitActual.getPinFreeSeetingFlag());
    }

    @Test
    void validateConfirmTransactionWhenTransactionLimitExceededAndDailyLimitTypeTransferShouldThrowDailyLimitTest() throws SQLException {
        dailyLimitType = DAILY_LIMIT_TYPE_TRANSFER;
        pinFreeType = PIN_FREE_TYPE_TRANSFER;

        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(crmProfile);

        Double amountMoreThanLimit = 2000.00;
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentValidateTransaction.validateConfirmTransaction(
                        headers, amountMoreThanLimit, transId, false, false, dailyLimitType, pinFreeType)
        );

        Assertions.assertEquals(ResponseCode.DAILY_LIMIT.getCode(), exception.getErrorCode());
    }

    @Test
    void validateConfirmTransactionWhenTransactionLimitExceededAndDailyLimitTypeBillShouldThrowDailyLimitTest() throws SQLException {
        dailyLimitType = DAILY_LIMIT_TYPE_BILL;
        pinFreeType = PIN_FREE_TYPE_BILL;

        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(crmProfile);

        Double amountMoreThanLimit = 20000.00;
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentValidateTransaction.validateConfirmTransaction(
                        headers, amountMoreThanLimit, transId, false, false, dailyLimitType, pinFreeType)
        );

        Assertions.assertEquals(ResponseCode.DAILY_LIMIT.getCode(), exception.getErrorCode());
    }

    @Test
    void validateConfirmTransactionWhenIsRequirePinButNotHavePinInCacheAndDailyLimitTypeTransferShouldThrowPinREQUIREDLimitTest() throws SQLException {
        dailyLimitType = DAILY_LIMIT_TYPE_TRANSFER;
        pinFreeType = PIN_FREE_TYPE_BILL;

        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(crmProfile);

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentValidateTransaction.validateConfirmTransaction(
                        headers, 400.00, transId, false, false, dailyLimitType, pinFreeType)
        );

        Assertions.assertEquals(ResponseCode.PIN_REQUIRED.getCode(), exception.getErrorCode());

    }

    @Test
    void validateConfirmTransactionWhenIsRequirePinButNotHavePinInCacheAndDailyLimitTypeBillShouldThrowPinREQUIREDLimitTest() throws SQLException {
        dailyLimitType = DAILY_LIMIT_TYPE_BILL;
        pinFreeType = PIN_FREE_TYPE_BILL;

        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(crmProfile);

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentValidateTransaction.validateConfirmTransaction(
                        headers, 400.00, transId, false, false, dailyLimitType, pinFreeType)
        );

        Assertions.assertEquals(ResponseCode.PIN_REQUIRED.getCode(), exception.getErrorCode());

    }

    @Test
    void isRequireConfirmPinWhenPinFlagNotAllowShouldReturnTrueTest() throws TMBCommonException {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPinFreeSeetingFlag("N");

        boolean actual = billPaymentValidateTransaction.isRequireConfirmPinAndDailyLimit(
                customerCrmProfile, 400.00, correlationId, false, false, PIN_FREE_TYPE_BILL);

        assertTrue(actual);
    }

    @Test
    void isRequireConfirmPinWhenAmountMoreThanBPLimitAllowAndDailyLimitTypeBillShouldReturnTrueTest() throws TMBCommonException {
        crmProfile.setPinFreeBpLimit(200.00);
        Double amountMoreThanBP = 300.00;

        boolean actual = billPaymentValidateTransaction.isRequireConfirmPinAndDailyLimit(
                crmProfile, amountMoreThanBP, correlationId, false, false, PIN_FREE_TYPE_BILL);

        assertTrue(actual);
    }

    @Test
    void isRequireConfirmPinWhenAmountMoreThanBPLimitAllowAndDailyLimitTypeTransferShouldReturnTrueTest() throws TMBCommonException {
        crmProfile.setPinFreeBpLimit(200.00);
        Double amountMoreThanBP = 300.00;

        boolean actual = billPaymentValidateTransaction.isRequireConfirmPinAndDailyLimit(
                crmProfile, amountMoreThanBP, correlationId, false, false, PIN_FREE_TYPE_TRANSFER);

        assertTrue(actual);
    }

    @Test
    void isRequireConfirmPinWhenFromStepPreLoginShouldReturnTrueTest() throws TMBCommonException {
        boolean isPreLogin = true;

        boolean actual = billPaymentValidateTransaction.isRequireConfirmPinAndDailyLimit(
                crmProfile, 300.00, correlationId, isPreLogin, false, PIN_FREE_TYPE_BILL);

        assertTrue(actual);
    }

    @Test
    void isRequireConfirmPinWhenIsOwnerShouldReturnFalseTest() throws TMBCommonException {
        boolean isPreLogin = false;
        boolean isOwner = true;

        boolean actual = billPaymentValidateTransaction.isRequireConfirmPinAndDailyLimit(
                crmProfile, 300.00, correlationId, isPreLogin, isOwner, PIN_FREE_TYPE_BILL);

        Assertions.assertFalse(actual);
    }

    @Test
    void isRequireConfirmPinWhenPinFreeCountMoreThanPinFreeMaxTransShouldReturnTrueTest() throws TMBCommonException {
        int pinFreeCount = 15;
        String pinFreeMaxTrans = "14";

        when(validateDailyLimitPinFree.fetchPinFreeMaxCount(Mockito.anyString())).thenReturn(pinFreeMaxTrans);

        crmProfile.setPinFreeTxnCount(pinFreeCount);

        boolean actual = billPaymentValidateTransaction.isRequireConfirmPinAndDailyLimit(
                crmProfile, 200.00, correlationId, false, false, PIN_FREE_TYPE_BILL);

        assertTrue(actual);
    }

    @Test
    void isRequireConfirmPinWhenPinFreeCountLessThanPinFreeMaxTransShouldReturnFalseTest() throws TMBCommonException {
        int pinFreeCount = 2;
        String pinFreeMaxTrans = "15";

        when(validateDailyLimitPinFree.fetchPinFreeMaxCount(Mockito.anyString())).thenReturn(pinFreeMaxTrans);

        crmProfile.setPinFreeTxnCount(pinFreeCount);

        boolean actual = billPaymentValidateTransaction.isRequireConfirmPinAndDailyLimit(
                crmProfile, 200.00, correlationId, false, false, PIN_FREE_TYPE_BILL);

        Assertions.assertFalse(actual);
    }

    @Test
    void isRequireConfirmPinAndDailyLimitWhenPinFlagNotAllowShouldReturnTrueTest() throws TMBCommonException {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPinFreeSeetingFlag("N");

        Double amount = 100.00;
        boolean actual = billPaymentValidateTransaction.isRequireConfirmPinAndDailyLimit(
                customerCrmProfile, amount, correlationId, false, false, PIN_FREE_TYPE_BILL);

        assertTrue(actual);
    }

    @Test
    void isRequireConfirmPinAndDailyLimitWhenIsPreLoginIsTrueShouldReturnTrueTest() throws TMBCommonException {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPinFreeSeetingFlag("Y");

        Double amount = 100.00;
        boolean actual = billPaymentValidateTransaction.isRequireConfirmPinAndDailyLimit(
                customerCrmProfile, amount, correlationId, true, false, PIN_FREE_TYPE_BILL);

        assertTrue(actual);
    }

    @Test
    void isRequireConfirmPinAndDailyLimitWhenIsOwnerIsTrueShouldReturnFalseTest() throws TMBCommonException {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPinFreeSeetingFlag("Y");

        Double amount = 100.00;
        boolean actual = billPaymentValidateTransaction.isRequireConfirmPinAndDailyLimit(
                customerCrmProfile, amount, correlationId, false, true, PIN_FREE_TYPE_BILL);

        Assertions.assertFalse(actual);
    }

    @Test
    void isRequireConfirmPinAndDailyLimitWhenAmountMoreThanBPLimitAllowShouldReturnTrueTest() throws TMBCommonException {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPinFreeSeetingFlag("Y");
        customerCrmProfile.setPinFreeBpLimit(200.00);

        Double amount = 300.00;
        boolean actual = billPaymentValidateTransaction.isRequireConfirmPinAndDailyLimit(
                customerCrmProfile, amount, correlationId, false, false, PIN_FREE_TYPE_BILL);

        assertTrue(actual);
    }

    @Test
    void isRequireConfirmPinAndDailyLimitWhenPinFreeCountMoreThanPinFreeMaxTransShouldReturnTrueTest() throws TMBCommonException {
        int pinFreeCount = 15;
        String pinFreeMaxTrans = "14";

        when(validateDailyLimitPinFree.fetchPinFreeMaxCount(Mockito.anyString())).thenReturn(pinFreeMaxTrans);

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPinFreeSeetingFlag("Y");
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(pinFreeCount);

        Double amount = 200.00;
        boolean actual = billPaymentValidateTransaction.isRequireConfirmPinAndDailyLimit(
                customerCrmProfile, amount, correlationId, false, false, PIN_FREE_TYPE_BILL);

        assertTrue(actual);
    }

    @Test
    void isRequireConfirmPinAndDailyLimitWhenPinFreeCountLessThanPinFreeMaxTransShouldReturnFalseTest() throws TMBCommonException {
        int pinFreeCount = 2;
        String pinFreeMaxTrans = "15";

        when(validateDailyLimitPinFree.fetchPinFreeMaxCount(Mockito.anyString())).thenReturn(pinFreeMaxTrans);

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPinFreeSeetingFlag("Y");
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(pinFreeCount);

        Double amount = 200.00;
        boolean actual = billPaymentValidateTransaction.isRequireConfirmPinAndDailyLimit(
                customerCrmProfile, amount, correlationId, false, false, PIN_FREE_TYPE_BILL);

        Assertions.assertFalse(actual);
    }

    @Test
    void isPinCacheWhenHaveDataInCacheShouldReturnTrueTest() throws TMBCommonException {
        String verifyPinRefId = "123456789";
        String keyVerify = "VERIFY_PIN_REF_ID_" + verifyPinRefId;

        when(oauthService.getCache(correlationId, keyVerify)).thenReturn(true);

        boolean actual = billPaymentValidateTransaction.isPinCache(verifyPinRefId, correlationId);

        assertTrue(actual);

        Mockito.verify(oauthService, Mockito.times(1))
                .getCache(Mockito.eq(correlationId), Mockito.eq(keyVerify));
    }

    @ParameterizedTest
    @CsvSource({"true, true", "true, false", "false, false"})
    void validateRequirePinAndNotHavePinInCacheFailsTest(boolean isPinCache, boolean isRequirePin) throws TMBCommonException {
        Mockito.doReturn(isPinCache).when(billPaymentValidateTransaction).isPinCache(transId, correlationId);

        Assertions.assertDoesNotThrow(() -> billPaymentValidateTransaction
                .validateRequirePinAndNotHavePinInCache(isRequirePin, transId, correlationId)
        );
    }

    @ParameterizedTest
    @CsvSource({"false, 1", "true, 0"})
    void updateBillDailyUsageShouldCallUpdatePinFreeAndUpdateDailyLimitCorrectly(boolean isRequirePin, int wantedNumberOfInvocations) {
        Double amount = 200.00;

        crmProfile.setEbMaxLimitAmtCurrent(1000.00);
        crmProfile.setEbAccuUsgAmtDaily(200.00);
        crmProfile.setPinFreeBpLimit(200.00);
        crmProfile.setPinFreeTxnCount(1);
        crmProfile.setPinFreeSeetingFlag("Y");

        billPaymentValidateTransaction.updateBillDailyUsage(crmId, amount, crmProfile, isRequirePin, correlationId);

        Mockito.verify(transactionLimitService, Mockito.times(wantedNumberOfInvocations)).updatePinFreeCount(
                crmId, crmProfile.getPinFreeTxnCount() + 1, crmProfile.getPinFreeSeetingFlag(), false, correlationId);

        Mockito.verify(transactionLimitService, Mockito.times(1))
                .updateBillPayAccumulateUsage(crmId, amount + crmProfile.getEbAccuUsgAmtDaily(), correlationId);
    }

    @Test
    void updateBillDailyUsageAndPinCountShouldCallUpdatePinFreeAndUpdateDailyLimitCorrectlyTest() throws SQLException {
        crmProfile.setEbMaxLimitAmtCurrent(1000.00);
        crmProfile.setEbAccuUsgAmtDaily(200.00);
        crmProfile.setPinFreeBpLimit(200.00);
        crmProfile.setPinFreeTxnCount(1);
        crmProfile.setPinFreeSeetingFlag("Y");
        Mockito.doReturn(crmProfile).when(billPaymentValidateTransaction).fetchTransactionLimit(correlationId, crmId);

        billPaymentValidateTransaction.updateDailyUsageAndPinCount(correlationId, crmId, "100.00", false);

        Mockito.verify(transactionLimitService, Mockito.times(1)).updatePinFreeCount(
                crmId, crmProfile.getPinFreeTxnCount() + 1, crmProfile.getPinFreeSeetingFlag(), false, correlationId);

        Mockito.verify(transactionLimitService, Mockito.times(1))
                .updateBillPayAccumulateUsage(crmId, 100.00 + crmProfile.getEbAccuUsgAmtDaily(), correlationId);
    }

    @ParameterizedTest
    @CsvSource({"false, 1", "true, 0"})
    void updateTransferDailyUsageShouldCallUpdatePinFreeAndUpdateDailyLimitCorrectly(boolean isRequirePin, int wantedNumberOfInvocations) {
        Double amount = 200.00;

        crmProfile.setEbMaxLimitAmtCurrent(1000.00);
        crmProfile.setEbAccuUsgAmtDaily(200.00);
        crmProfile.setPinFreeBpLimit(200.00);
        crmProfile.setPinFreeTxnCount(1);
        crmProfile.setPinFreeSeetingFlag("Y");

        billPaymentValidateTransaction.updateTransferDailyUsage(crmId, amount, crmProfile, isRequirePin, correlationId);

        Mockito.verify(transactionLimitService, Mockito.times(wantedNumberOfInvocations)).updatePinFreeCount(
                crmId, crmProfile.getPinFreeTxnCount() + 1, crmProfile.getPinFreeSeetingFlag(), false, correlationId);

        Mockito.verify(transactionLimitService, Mockito.times(1))
                .updateEBAccountUsageAmountDaily(crmId, amount + crmProfile.getEbAccuUsgAmtDaily(), correlationId);
    }

    @Test
    void updateTransferDailyUsageWhenUpdatePinFreeCountExceptionShouldDoesNotThrows() {
        Mockito.doThrow(FeignException.class).when(transactionLimitService).updatePinFreeCount(crmId, crmProfile.getPinFreeTxnCount() + 1, crmProfile.getPinFreeSeetingFlag(), false, correlationId);

        Assertions.assertDoesNotThrow(() ->
                billPaymentValidateTransaction.updateTransferDailyUsage(crmId, 200.00, crmProfile, false, correlationId));
    }

    @Test
    void updateTransferDailyUsageWhenUpdateEBAccountUsageAmountDailyExceptionShouldDoesNotThrows() {
        Mockito.doThrow(FeignException.class).when(transactionLimitService).updateEBAccountUsageAmountDaily(crmId, 200.00 + crmProfile.getEbAccuUsgAmtDaily(), correlationId);

        Assertions.assertDoesNotThrow(() ->
                billPaymentValidateTransaction.updateTransferDailyUsage(crmId, 200.00, crmProfile, true, correlationId));
    }

    @Test
    void updateDailyUsageAndPinCountShouldCallUpdatePinFreeAndUpdateDailyLimitCorrectlyTest() throws SQLException {
        crmProfile.setEbMaxLimitAmtCurrent(1000.00);
        crmProfile.setEbAccuUsgAmtDaily(200.00);
        crmProfile.setPinFreeBpLimit(200.00);
        crmProfile.setPinFreeTxnCount(1);
        crmProfile.setPinFreeSeetingFlag("Y");
        Mockito.doReturn(crmProfile).when(billPaymentValidateTransaction).fetchTransactionLimit(correlationId, crmId);

        billPaymentValidateTransaction.updateDailyUsageAndPinCount(correlationId, crmId, "100.00", false);

        Mockito.verify(transactionLimitService, Mockito.times(1)).updatePinFreeCount(
                crmId, crmProfile.getPinFreeTxnCount() + 1, crmProfile.getPinFreeSeetingFlag(), false, correlationId);

        Mockito.verify(transactionLimitService, Mockito.times(1))
                .updateBillPayAccumulateUsage(crmId, 100.00 + crmProfile.getEbAccuUsgAmtDaily(), correlationId);
    }

    @Test
    void validateBillConfirmTransactionShouldCallValidateConfirmTransactionTest() throws TMBCommonException, SQLException {
        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(crmProfile);

        when(validateDailyLimitPinFree.fetchPinFreeMaxCount(correlationId)).thenReturn("15");

        Assertions.assertDoesNotThrow(() ->
                billPaymentValidateTransaction.validateBillConfirmTransaction(headers, "100.00", "trans_id", false, false));

        Mockito.verify(billPaymentValidateTransaction, Mockito.times(1))
                .validateConfirmTransaction(Mockito.any(HttpHeaders.class), Mockito.anyDouble(), Mockito.anyString(), Mockito.anyBoolean(), Mockito.anyBoolean(), eq(DAILY_LIMIT_TYPE_BILL), eq(PIN_FREE_TYPE_BILL));
    }

    @Test
    void validateTransactionLimitExceededShouldThrowFailedWhenIsOwnerIsFalseTest() {

        Double amount = 100.00;
        double remaining = 90.00;
        boolean isOwner = false;
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentValidateTransaction.validateTransactionLimitExceeded(remaining, amount, isOwner));

        Assertions.assertEquals(ResponseCode.DAILY_LIMIT.getCode(), exception.getErrorCode());
    }

    @Test
    void validateTransactionLimitExceededShouldNotThrowsFailedWhenIsOwnerIsTrueTest() {

        Double amount = 100.00;
        double remaining = 90.00;
        boolean isOwner = true;
        Assertions.assertDoesNotThrow(() ->
                billPaymentValidateTransaction.validateTransactionLimitExceeded(remaining, amount, isOwner)
        );
    }

    @Test
    void validateIsRequireConfirmPinOfBillAndTopUPShouldCallIsRequireConfirmPinAndDailyLimitWithPinFreeTypeBillTest() throws SQLException, TMBCommonException {
        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(crmProfile);

        when(validateDailyLimitPinFree.fetchPinFreeMaxCount(correlationId)).thenReturn("15");

        Assertions.assertDoesNotThrow(() ->
                billPaymentValidateTransaction.validateIsRequireConfirmPinOfBillAndTopUP(crmId, correlationId, "200.00", false, false));

        Mockito.verify(billPaymentValidateTransaction, Mockito.times(1))
                .isRequireConfirmPinAndDailyLimit(any(), anyDouble(), anyString(), anyBoolean(), anyBoolean(), eq(PIN_FREE_TYPE_BILL));
    }

    @Test
    void validateRequirePinWhenHavePinInCacheShouldDoesNotThrowsAndReturnCorrectlyTest() throws SQLException, TMBCommonException {
        dailyLimitType = DAILY_LIMIT_TYPE_BILL;

        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(crmProfile);

        when(oauthService.getCache(anyString(), anyString())).thenReturn(true);

        Map<String, Object> actual = billPaymentValidateTransaction.validateRequirePin(
                headers, 400.00, transId, true, false);

        assertTrue((Boolean) actual.get("isRequirePin"));
        assertNull(actual.get("DailyLimit"));

    }

    @Test
    void validateRequirePiWhenIsRequirePinButNotHavePinInCacheShouldThrowPinREQUIREDLimitTest() throws SQLException {
        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(crmProfile);

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentValidateTransaction.validateRequirePin(
                        headers, 400.00, transId, false, false)
        );

        Assertions.assertEquals(ResponseCode.PIN_REQUIRED.getCode(), exception.getErrorCode());
    }

    @Test
    void validateIsRequireVerifyTransactionShouldValidateWithCommonAuthenticationTest() throws TMBCommonException {
        String minAppVersionToTriggerCommonAuth = "5.12.0";
        ReflectionTestUtils.setField(billPaymentValidateTransaction, "commonAuthenRequireAppVersion", minAppVersionToTriggerCommonAuth);
        headers.add(PaymentServiceConstant.APP_VERSION, minAppVersionToTriggerCommonAuth);

        Assertions.assertDoesNotThrow(() -> billPaymentValidateTransaction.validateIsRequireVerifyTransaction(headers, new BigDecimal("10000.00"), false, crmProfile));

        Mockito.verify(billPaymentValidateTransaction, Mockito.times(1))
                .validateIsRequireCommonAuthen(headers, "10000.00", false, crmProfile);
    }

    @Test
    void validateIsRequireVerifyTransactionShouldValidateWithLegacyAuthenticationTest() throws TMBCommonException {
        String appVersionLowerThanTrigger = "5.10.0";
        String minAppVersionToTriggerCommonAuth = "5.12.0";
        ReflectionTestUtils.setField(billPaymentValidateTransaction, "commonAuthenRequireAppVersion", minAppVersionToTriggerCommonAuth);
        headers.add(PaymentServiceConstant.APP_VERSION, appVersionLowerThanTrigger);

        Assertions.assertDoesNotThrow(() -> billPaymentValidateTransaction.validateIsRequireVerifyTransaction(headers, new BigDecimal("10000.00"), false, crmProfile));

        Mockito.verify(billPaymentValidateTransaction, Mockito.times(1))
                .isRequireConfirmPinAndDailyLimit(any(), anyDouble(), anyString(), anyBoolean(), anyBoolean(), anyString());
    }

    @Test
    void validateIsRequireVerifyTransactionForTopUpShouldValidateWithCommonAuthenticationTest() throws TMBCommonException {
        String minAppVersionToTriggerCommonAuth = "5.12.0";
        ReflectionTestUtils.setField(billPaymentValidateTransaction, "commonAuthenRequireAppVersion", minAppVersionToTriggerCommonAuth);
        headers.add(PaymentServiceConstant.APP_VERSION, minAppVersionToTriggerCommonAuth);

        Assertions.assertDoesNotThrow(() -> billPaymentValidateTransaction.validateIsRequireVerifyTransactionForTopUp(headers, new BigDecimal("10000.00"), false, crmProfile));

        Mockito.verify(billPaymentValidateTransaction, never()).isRequireConfirmPinAndDailyLimit(any(), anyDouble(), anyString(), anyBoolean(), anyBoolean(), anyString());
        Mockito.verify(validatorFaceRecognizeTransfer, never()).validateFaceRecognize(anyString(), anyString(), anyString(), any());
    }

    @Test
    void validateIsRequireVerifyTransactionForTopUpShouldUsePinFreeBpLimitForBillpayTopupTest() throws Exception {
        String minAppVersionToTriggerCommonAuth = "5.12.0";
        ReflectionTestUtils.setField(billPaymentValidateTransaction, "commonAuthenRequireAppVersion", minAppVersionToTriggerCommonAuth);
        headers.add(PaymentServiceConstant.APP_VERSION, minAppVersionToTriggerCommonAuth);

        crmProfile.setPinFreeBpLimit(15000.00);
        crmProfile.setPinFreeTrLimit(null);
        crmProfile.setPinFreeSeetingFlag("Y");
        crmProfile.setPinFreeTxnCount(0);

        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans("10");
        when(commonService.getCommonConfig(anyString(), anyString())).thenReturn(commonData);

        when(oauthService.getCommonAuthForceFR(anyString(), anyString())).thenReturn(null);

        VerifyTransactionResult result = billPaymentValidateTransaction.validateIsRequireVerifyTransactionForTopUp(headers, new BigDecimal("10000.00"), false, crmProfile);

        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.commonAuthenResult().isRequireCommonAuthen(), "Should not require common authentication when amount is within pinFreeBpLimit");
        Assertions.assertTrue(result.commonAuthenResult().isPinFree(), "Should be pin free when within limit");
        Assertions.assertFalse(result.commonAuthenResult().getIsForceFR(), "Should not be DDP when not in force list");
    }

    @Test
    void validateIsRequireVerifyTransactionForTopUpShouldRequireCommonAuthenWhenAmountExceedsPinFreeBpLimitTest() throws Exception {
        String minAppVersionToTriggerCommonAuth = "5.12.0";
        ReflectionTestUtils.setField(billPaymentValidateTransaction, "commonAuthenRequireAppVersion", minAppVersionToTriggerCommonAuth);
        headers.add(PaymentServiceConstant.APP_VERSION, minAppVersionToTriggerCommonAuth);

        crmProfile.setPinFreeBpLimit(5000.00);
        crmProfile.setPinFreeTrLimit(null);
        crmProfile.setPinFreeSeetingFlag("Y");
        crmProfile.setPinFreeTxnCount(0);

        VerifyTransactionResult result = billPaymentValidateTransaction.validateIsRequireVerifyTransactionForTopUp(headers, new BigDecimal("10000.00"), false, crmProfile);

        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.commonAuthenResult().isRequireCommonAuthen(), "Should require common authentication when amount exceeds pinFreeBpLimit");
        Assertions.assertFalse(result.commonAuthenResult().isPinFree(), "Should not be pin free when exceeding limit");
        Assertions.assertNull(result.commonAuthenResult().getIsForceFR(), "Should be null when not pin free");
    }

    @Test
    void validateIsRequireVerifyTransactionForTopUpShouldValidateWithLegacyAuthenticationTest() throws TMBCommonException {
        String appVersionLowerThanTrigger = "5.10.0";
        String minAppVersionToTriggerCommonAuth = "5.12.0";
        ReflectionTestUtils.setField(billPaymentValidateTransaction, "commonAuthenRequireAppVersion", minAppVersionToTriggerCommonAuth);
        headers.add(PaymentServiceConstant.APP_VERSION, appVersionLowerThanTrigger);

        Assertions.assertDoesNotThrow(() -> billPaymentValidateTransaction.validateIsRequireVerifyTransactionForTopUp(headers, new BigDecimal("10000.00"), false, crmProfile));

        Mockito.verify(billPaymentValidateTransaction, Mockito.times(1))
                .isRequireConfirmPinAndDailyLimit(eq(crmProfile), anyDouble(), eq(correlationId), anyBoolean(), anyBoolean(), anyString());

        Mockito.verify(validatorFaceRecognizeTransfer, times(1))
                .validateFaceRecognize(eq(correlationId), eq(crmId), anyString(), any());
    }

    @Test
    void validateIsRequireCommonAuthenWhenPreLoginShouldRequireCommonAuthenTest() throws TMBCommonException {
        headers.add(PRE_LOGIN, "true");

        CommonAuthenResult actual = billPaymentValidateTransaction.validateIsRequireCommonAuthen(headers, "10000.00", true, crmProfile);

        assertTrue(actual.isRequireCommonAuthen());

    }

    @Test
    void validateIsRequireCommonAuthenWhenTransferToOwnShouldNotRequireCommonAuthenTest() throws TMBCommonException {
        boolean isOwn = true;

        CommonAuthenResult actual = billPaymentValidateTransaction.validateIsRequireCommonAuthen(headers, "10000.00", isOwn, crmProfile);

        assertFalse(actual.isRequireCommonAuthen());
    }

    @Test
    void validateIsRequireCommonAuthenWhenDisablePinFreeSettingAndAmountMoreThanLimitShouldRequireCommonAuthenTest() throws TMBCommonException {
        String amountMoreThanLimit = "10000.00";
        double limit = 1000.00;
        crmProfile.setPinFreeTrLimit(limit);

        CommonAuthenResult actual = billPaymentValidateTransaction.validateIsRequireCommonAuthen(headers, amountMoreThanLimit, false, crmProfile);

        assertTrue(actual.isRequireCommonAuthen());
    }

    @Test
    void validateIsRequireCommonAuthenWhenNotPinFreeShouldRequireCommonAuthenTest() throws TMBCommonException {
        int txnCount = 10;
        String maxCount = "10";
        crmProfile.setPinFreeTrLimit(1000.00);
        crmProfile.setPinFreeTxnCount(txnCount);

        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans(maxCount);
        when(commonService.getCommonConfig(anyString(), anyString())).thenReturn(commonData);

        CommonAuthenResult actual = billPaymentValidateTransaction.validateIsRequireCommonAuthen(headers, "10.00", false, crmProfile);

        assertTrue(actual.isRequireCommonAuthen());
    }

    @Test
    void validateIsRequireCommonAuthenWhenCrmIDInDDPShouldRequireCommonAuthenTest() throws TMBCommonException {
        int txnCount = 2;
        String maxCount = "10";
        crmProfile.setPinFreeTrLimit(1000.00);
        crmProfile.setPinFreeTxnCount(txnCount);

        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans(maxCount);
        when(commonService.getCommonConfig(anyString(), anyString())).thenReturn(commonData);

        when(oauthService.getCommonAuthForceFR(correlationId, crmId)).thenReturn(new CommonAuthForceFR().setCrmId(crmId).setIsForce(true));

        CommonAuthenResult actual = billPaymentValidateTransaction.validateIsRequireCommonAuthen(headers, "10.00", false, crmProfile);

        assertTrue(actual.isRequireCommonAuthen());
    }

    @Test
    void validateIsRequireCommonAuthenWhenCrmIDNotInDDPShouldNotRequireCommonAuthenTest() throws TMBCommonException {
        int txnCount = 2;
        String maxCount = "10";
        crmProfile.setPinFreeTrLimit(1000.00);
        crmProfile.setPinFreeTxnCount(txnCount);
        crmProfile.setPaymentAccuUsgAmt(new BigDecimal(1000));

        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans(maxCount);
        when(commonService.getCommonConfig(anyString(), anyString())).thenReturn(commonData);

        when(oauthService.getCommonAuthForceFR(correlationId, crmId)).thenReturn(null);

        CommonAuthenResult actual = billPaymentValidateTransaction.validateIsRequireCommonAuthen(headers, "10.00", false, crmProfile);

        assertFalse(actual.isRequireCommonAuthen());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenPreLoginThenRequireCommonAuthen() {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(PRE_LOGIN, "true");
        testHeaders.add(PaymentServiceConstant.X_CRMID, crmId);
        testHeaders.add(PaymentServiceConstant.HEADER_CORRELATION_ID, correlationId);

        String reqAmount = "5000.00";
        boolean isOwn = false;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        Method method = ReflectionUtils.findMethod(
                BillPaymentValidateTransaction.class,
                "validateIsRequireCommonAuthen",
                HttpHeaders.class, String.class, boolean.class, CustomerCrmProfile.class, double.class, boolean.class);
        ReflectionUtils.makeAccessible(method);

        CommonAuthenResult result = (CommonAuthenResult) ReflectionUtils.invokeMethod(
                method, billPaymentValidateTransaction, testHeaders, reqAmount, isOwn, crmProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertTrue(result.isRequireCommonAuthen());
        assertNull(result.getIsForceFR());
        assertFalse(result.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenIsOwnThenNotRequireCommonAuthen() {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(PaymentServiceConstant.X_CRMID, crmId);
        testHeaders.add(PaymentServiceConstant.HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(PRE_LOGIN, "false");

        String reqAmount = "5000.00";
        boolean isOwn = true;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        Method method = ReflectionUtils.findMethod(
                BillPaymentValidateTransaction.class,
                "validateIsRequireCommonAuthen",
                HttpHeaders.class, String.class, boolean.class, CustomerCrmProfile.class, double.class, boolean.class);
        ReflectionUtils.makeAccessible(method);

        CommonAuthenResult result = (CommonAuthenResult) ReflectionUtils.invokeMethod(
                method, billPaymentValidateTransaction, testHeaders, reqAmount, isOwn, crmProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertFalse(result.isRequireCommonAuthen());
        assertNull(result.getIsForceFR());
        assertFalse(result.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenPinFreeDisabledThenRequireCommonAuthen() {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(PaymentServiceConstant.X_CRMID, crmId);
        testHeaders.add(PaymentServiceConstant.HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(PRE_LOGIN, "false");

        String reqAmount = "500.00";
        boolean isOwn = false;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("N");

        Method method = ReflectionUtils.findMethod(
                BillPaymentValidateTransaction.class,
                "validateIsRequireCommonAuthen",
                HttpHeaders.class, String.class, boolean.class, CustomerCrmProfile.class, double.class, boolean.class);
        ReflectionUtils.makeAccessible(method);

        CommonAuthenResult result = (CommonAuthenResult) ReflectionUtils.invokeMethod(
                method, billPaymentValidateTransaction, testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertTrue(result.isRequireCommonAuthen());
        assertNull(result.getIsForceFR());
        assertFalse(result.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenAmountMoreThanLimitThenRequireCommonAuthen() {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(PaymentServiceConstant.X_CRMID, crmId);
        testHeaders.add(PaymentServiceConstant.HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(PRE_LOGIN, "false");

        String reqAmount = "2000.00";
        boolean isOwn = false;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");

        Method method = ReflectionUtils.findMethod(
                BillPaymentValidateTransaction.class,
                "validateIsRequireCommonAuthen",
                HttpHeaders.class, String.class, boolean.class, CustomerCrmProfile.class, double.class, boolean.class);
        ReflectionUtils.makeAccessible(method);

        CommonAuthenResult result = (CommonAuthenResult) ReflectionUtils.invokeMethod(
                method, billPaymentValidateTransaction, testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertTrue(result.isRequireCommonAuthen());
        assertNull(result.getIsForceFR());
        assertFalse(result.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenNotPinFreeThenRequireCommonAuthen() throws Exception {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(PaymentServiceConstant.X_CRMID, crmId);
        testHeaders.add(PaymentServiceConstant.HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(PRE_LOGIN, "false");

        String reqAmount = "500.00";
        boolean isOwn = false;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(20);

        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans("15");
        when(commonService.getCommonConfig(anyString(), anyString())).thenReturn(commonData);

        Method method = ReflectionUtils.findMethod(
                BillPaymentValidateTransaction.class,
                "validateIsRequireCommonAuthen",
                HttpHeaders.class, String.class, boolean.class, CustomerCrmProfile.class, double.class, boolean.class);
        ReflectionUtils.makeAccessible(method);

        CommonAuthenResult result = (CommonAuthenResult) ReflectionUtils.invokeMethod(
                method, billPaymentValidateTransaction, testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertTrue(result.isRequireCommonAuthen());
        assertNull(result.getIsForceFR());
        assertFalse(result.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenInDDPThenRequireCommonAuthen() throws Exception {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(PaymentServiceConstant.X_CRMID, crmId);
        testHeaders.add(PaymentServiceConstant.HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(PRE_LOGIN, "false");

        String reqAmount = "500.00";
        boolean isOwn = false;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(5);

        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans("15");
        when(commonService.getCommonConfig(anyString(), anyString())).thenReturn(commonData);

        CommonAuthForceFR forceFR = new CommonAuthForceFR();
        forceFR.setIsForce(true);
        forceFR.setCrmId(crmId);
        when(oauthService.getCommonAuthForceFR(correlationId, crmId)).thenReturn(forceFR);

        Method method = ReflectionUtils.findMethod(
                BillPaymentValidateTransaction.class,
                "validateIsRequireCommonAuthen",
                HttpHeaders.class, String.class, boolean.class, CustomerCrmProfile.class, double.class, boolean.class);
        ReflectionUtils.makeAccessible(method);

        CommonAuthenResult result = (CommonAuthenResult) ReflectionUtils.invokeMethod(
                method, billPaymentValidateTransaction, testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertTrue(result.isRequireCommonAuthen());
        assertTrue(result.getIsForceFR());
        assertTrue(result.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenNotInDDPAndWithinLimitThenNotRequireCommonAuthen() throws Exception {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(PaymentServiceConstant.X_CRMID, crmId);
        testHeaders.add(PaymentServiceConstant.HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(PRE_LOGIN, "false");

        String reqAmount = "500.00";
        boolean isOwn = false;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(5);
        customerProfile.setPaymentAccuUsgAmt(new BigDecimal(1000));

        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans("15");
        when(commonService.getCommonConfig(anyString(), anyString())).thenReturn(commonData);

        when(oauthService.getCommonAuthForceFR(correlationId, crmId)).thenReturn(null);

        Method method = ReflectionUtils.findMethod(
                BillPaymentValidateTransaction.class,
                "validateIsRequireCommonAuthen",
                HttpHeaders.class, String.class, boolean.class, CustomerCrmProfile.class, double.class, boolean.class);
        ReflectionUtils.makeAccessible(method);

        CommonAuthenResult result = (CommonAuthenResult) ReflectionUtils.invokeMethod(
                method, billPaymentValidateTransaction, testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertFalse(result.isRequireCommonAuthen());
        assertFalse(result.getIsForceFR());
        assertTrue(result.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenNotInDDPButExceedPaymentAccumulateLimitThenRequireCommonAuthen() throws Exception {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(PaymentServiceConstant.X_CRMID, crmId);
        testHeaders.add(PaymentServiceConstant.HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(PRE_LOGIN, "false");

        String reqAmount = "1000.00";
        boolean isOwn = false;
        double pinFreeTranLimit = 2000.00;
        boolean paymentAccumulateUsageLimitRule = true;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(5);
        customerProfile.setPaymentAccuUsgAmt(new BigDecimal("199500"));

        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans("15");
        when(commonService.getCommonConfig(anyString(), anyString())).thenReturn(commonData);

        when(oauthService.getCommonAuthForceFR(correlationId, crmId)).thenReturn(null);

        Method method = ReflectionUtils.findMethod(
                BillPaymentValidateTransaction.class,
                "validateIsRequireCommonAuthen",
                HttpHeaders.class, String.class, boolean.class, CustomerCrmProfile.class, double.class, boolean.class);
        ReflectionUtils.makeAccessible(method);

        CommonAuthenResult result = (CommonAuthenResult) ReflectionUtils.invokeMethod(
                method, billPaymentValidateTransaction, testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertTrue(result.isRequireCommonAuthen());
        assertFalse(result.getIsForceFR());
        assertTrue(result.isPinFree());
    }

    @Test
    void validateIsRequireCommonAuthenWhenAmountWithinPinFreeBpLimitShouldNotRequireCommonAuthenTest() throws TMBCommonException {

        crmProfile.setPinFreeBpLimit(20000.00);
        crmProfile.setPinFreeSeetingFlag("Y");
        crmProfile.setPinFreeTxnCount(0);


        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans("10");
        when(commonService.getCommonConfig(Mockito.anyString(), Mockito.anyString())).thenReturn(commonData);


        when(oauthService.getCommonAuthForceFR(Mockito.anyString(), Mockito.anyString())).thenReturn(null);


        Method method = ReflectionUtils.findMethod(
                BillPaymentValidateTransaction.class,
                "validateIsRequireCommonAuthen",
                HttpHeaders.class, String.class, boolean.class, CustomerCrmProfile.class, double.class, boolean.class
        );
        ReflectionUtils.makeAccessible(method);
        CommonAuthenResult result = (CommonAuthenResult) ReflectionUtils.invokeMethod(
                method, billPaymentValidateTransaction, headers, "10000.00", false, crmProfile, crmProfile.getPinFreeBpLimit(), true
        );


        Assertions.assertFalse(result.isRequireCommonAuthen());
        Assertions.assertTrue(result.isPinFree());
        Assertions.assertFalse(result.getIsForceFR());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenNotInDDPAndWithinAccumulateLimitThenNotRequireCommonAuthen() throws Exception {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(PaymentServiceConstant.X_CRMID, crmId);
        testHeaders.add(PaymentServiceConstant.HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(PRE_LOGIN, "false");

        String reqAmount = "1000.00";
        boolean isOwn = false;
        double pinFreeTranLimit = 2000.00;
        boolean paymentAccumulateUsageLimitRule = true;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(5);
        customerProfile.setPaymentAccuUsgAmt(new BigDecimal("190000"));

        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans("15");
        when(commonService.getCommonConfig(anyString(), anyString())).thenReturn(commonData);

        when(oauthService.getCommonAuthForceFR(correlationId, crmId)).thenReturn(null);

        Method method = ReflectionUtils.findMethod(
                BillPaymentValidateTransaction.class,
                "validateIsRequireCommonAuthen",
                HttpHeaders.class, String.class, boolean.class, CustomerCrmProfile.class, double.class, boolean.class);
        ReflectionUtils.makeAccessible(method);

        CommonAuthenResult result = (CommonAuthenResult) ReflectionUtils.invokeMethod(
                method, billPaymentValidateTransaction, testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertFalse(result.isRequireCommonAuthen());
        assertFalse(result.getIsForceFR());
        assertTrue(result.isPinFree());
    }
}
