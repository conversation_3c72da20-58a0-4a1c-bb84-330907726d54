package com.tmb.oneapp.paymentexpservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.paymentexpservice.client.TopUpFleetCardFeignClient;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.factory.Factory;
import com.tmb.oneapp.paymentexpservice.model.ActivityTopUpEvent;
import com.tmb.oneapp.paymentexpservice.model.AdditionalParam;
import com.tmb.oneapp.paymentexpservice.model.BillerInfoResponse;
import com.tmb.oneapp.paymentexpservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.DepositAccount;
import com.tmb.oneapp.paymentexpservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.paymentexpservice.model.OCPAccountPayment;
import com.tmb.oneapp.paymentexpservice.model.OCPBillPayment;
import com.tmb.oneapp.paymentexpservice.model.OCPFee;
import com.tmb.oneapp.paymentexpservice.model.PaymentDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.ScheduleConfigFrequency;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyResponse;
import com.tmb.oneapp.paymentexpservice.model.Transaction;
import com.tmb.oneapp.paymentexpservice.model.WaiveOCP;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityBillPayVerifyEvent;
import com.tmb.oneapp.paymentexpservice.model.billpay.BillpayModel;
import com.tmb.oneapp.paymentexpservice.model.commonauth.VerifyTransactionResult;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CardInfo;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCardDetail;
import com.tmb.oneapp.paymentexpservice.model.deeplink.DeepLinkRequest;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import com.tmb.oneapp.paymentexpservice.utils.CacheService;
import feign.FeignException;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.internal.matchers.apachecommons.ReflectionEquals;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACTIVITY_LOG_BILL_PAY_VERIFY_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.APP_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILLER_PAYMENT_TOPUP;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_COMP_CODE_AIS_ON_TOP_POSTPAID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_COMP_CODE_AIS_ON_TOP_PREPAID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_REF_1_ETE_REQUEST_AIS_ON_TOP;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.CHANNEL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_MODEL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.LOAN_ACCOUNT_PREFIX;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.OS_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TOP_UP_COMP_CODE_TRUE_MOVE_H_DATA_PACKAGE;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_TELCO_DETAIL_ENTER_PAGE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

@ExtendWith(MockitoExtension.class)
class BillPaymentOCPOnlineAndOfflineVerifyServiceTest {
    @Mock
    LogService logService;

    @Mock
    BillPaymentValidateTransaction billPaymentValidateTransaction;

    @Mock
    CommonPaymentService commonPaymentService;

    @Mock
    TopUpETEService topUpETEService;

    @Mock
    TopUpFleetCardFeignClient topUpFleetCardFeignClient;

    @Mock
    BillPaymentCreditCardService billPaymentCreditCardService;

    @Mock
    PaymentConfigurationService paymentConfigurationService;

    @Mock
    CacheService cacheService;

    @Spy
    @InjectMocks
    BillPaymentOCPOnlineAndOfflineVerifyService billPaymentOCPOnlineAndOfflineVerifyService;

    String crmId;
    String correlationId;
    String transId;
    String paymentRequestRef1;
    String paymentRequestRef2;
    String loanAccount;
    HttpHeaders headers;
    ActivityTopUpEvent activityEvent;
    ActivityBillPayVerifyEvent activityBillPayVerifyEvent;
    TopUpVerifyRequest topUpVerifyRequest;
    BillerTopUpDetailResponse billerDetail;

    @BeforeEach
    void setUp() {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        transId = "TOPUP_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        paymentRequestRef1 = "0*********";
        paymentRequestRef2 = "001";
        loanAccount = LOAN_ACCOUNT_PREFIX + paymentRequestRef1 + paymentRequestRef2;
        activityEvent = new ActivityTopUpEvent("", "", "");
        headers = new HttpHeaders();
        headers.add(X_CRMID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);

        headers = new HttpHeaders();
        headers.add(X_CRMID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);
        headers.add(OS_VERSION, "");
        headers.add(CHANNEL, "");
        headers.add(APP_VERSION, "");
        headers.add(DEVICE_ID, "");
        headers.add(DEVICE_MODEL, "");

        topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("110.00");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("12345");
        topUpVerifyRequest.setBillerCompCode("5003");
        topUpVerifyRequest.setAccountNumber("**********");

        billerDetail = Factory.createBillerDetail();

        activityBillPayVerifyEvent = new ActivityBillPayVerifyEvent(
                correlationId,
                ACTIVITY_LOG_BILL_PAY_VERIFY_ID,
                headers,
                topUpVerifyRequest,
                billerDetail
        );
    }

    @Test
    void billPayOnlinePaymentVerifySuccessShouldReturnTopUpVerifyResponseTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        BillerTopUpDetailResponse billerTopUpDetailResponse = mockDataBillerTopUpDetailResponse("True Move", "2", "5");

        mockFetchTransactionLimit();

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        OCPAccountPayment toAccount = new OCPAccountPayment();
        toAccount.setAccountId("**********");
        toAccount.setAccountType("DDA");

        OCPFee topUpFee = new OCPFee();
        topUpFee.setBillPmtFee("10.00");

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setToAccount(toAccount);
        ocpBillPayment.setFee(topUpFee);
        ocpBillPayment.setBankRefId("*********");

        AdditionalParam additionalParam = new AdditionalParam();
        additionalParam.setName("Msg");
        additionalParam.setValue("นายทดสอบ| ");
        ocpBillPayment.setAdditionalParams(Collections.singletonList(additionalParam));
        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenReturn(ocpBillPayment);

        mockGetTransactionId();

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setAccountNumber("*********");
        topUpVerifyRequest.setBillerCompCode("2151");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("");
        topUpVerifyRequest.setNote("");
        topUpVerifyRequest.setToFavoriteName("ReqToFavoriteName");
        topUpVerifyRequest.setIsBillPay(false);

        DepositAccount depositAccount = mockDataDepositAccount();
        TopUpVerifyResponse actual = billPaymentOCPOnlineAndOfflineVerifyService.billPayOnlinePaymentVerify(
                topUpVerifyRequest, headers, transId, billerTopUpDetailResponse, activityBillPayVerifyEvent, depositAccount, "caseValue");

        Assertions.assertNotNull(actual.getFee());
        Assertions.assertNotNull(actual.getTransId());
        Assertions.assertNotNull(actual.getTopUpRef());
        Assertions.assertNotNull(actual.getTopUpAccountName());
        Assertions.assertTrue(actual.getIsRequireConfirmPin());
        Assertions.assertFalse(actual.getIsRequireCommonAuthen());
        Assertions.assertNull(actual.getCommonAuthenticationInformation());

        ArgumentCaptor<OCPBillPayment> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OCPBillPayment.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(eq(transId), dataSaveToCacheCaptor.capture());

        OCPBillPayment actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals("ReqToFavoriteName", actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        Assertions.assertTrue(actualDataSaveToCache.getPaymentCacheData().isRequirePin());
    }

    @Test
    void billPayOnlinePaymentVerifyWhenIsBillPayNullAndBillerIsFleetCardShouldSetTranCodeInCacheTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        BillerTopUpDetailResponse billerTopUpDetailResponse = mockDataBillerTopUpDetailResponse("True Move", "2", "5");

        mockFetchTransactionLimit();

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        OCPAccountPayment toAccount = new OCPAccountPayment();
        toAccount.setAccountId("**********");
        toAccount.setAccountType("DDA");

        OCPFee topUpFee = new OCPFee();
        topUpFee.setBillPmtFee("10.00");

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setToAccount(toAccount);
        ocpBillPayment.setFee(topUpFee);
        ocpBillPayment.setBankRefId("*********");

        AdditionalParam additionalParam = new AdditionalParam();
        additionalParam.setName("Msg");
        additionalParam.setValue("นายทดสอบ| ");
        ocpBillPayment.setAdditionalParams(Collections.singletonList(additionalParam));

        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenReturn(ocpBillPayment);

        Mockito.when(topUpFleetCardFeignClient.fleetCardVerify(Mockito.any(), Mockito.any())).thenReturn(ResponseEntity.ok(new Transaction()));

        mockGetTransactionId();

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setAccountNumber("*********");
        topUpVerifyRequest.setBillerCompCode("0012");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("");
        topUpVerifyRequest.setNote("");

        DepositAccount depositAccount = mockDataDepositAccount();
        billPaymentOCPOnlineAndOfflineVerifyService.billPayOnlinePaymentVerify(
                topUpVerifyRequest, headers, transId, billerTopUpDetailResponse, activityBillPayVerifyEvent, depositAccount, "caseValue");

        ArgumentCaptor<OCPBillPayment> cacheData = ArgumentCaptor.forClass(OCPBillPayment.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(eq(transId), cacheData.capture());

        OCPBillPayment topUpETEPayment = cacheData.getValue();
        Assertions.assertEquals(PaymentServiceConstant.BILL_TRAN_CODE_FOR_FLEET_CARD, topUpETEPayment.getTranCode());
    }

    @Test
    void billPayOnlinePaymentVerifyWhenBillerIsFleetCardCircuitBreakerTrippedShouldThrowTMBCommonExceptionTest() throws TMBCommonException {
        BillerTopUpDetailResponse billerTopUpDetailResponse = mockDataBillerTopUpDetailResponse("True Move", "2", "5");

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setEbMaxLimitAmtCurrent(1000.00);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(1);
        customerCrmProfile.setPinFreeSeetingFlag("Y");

        OCPAccountPayment toAccount = new OCPAccountPayment();
        toAccount.setAccountId("**********");
        toAccount.setAccountType("DDA");

        OCPFee topUpFee = new OCPFee();
        topUpFee.setBillPmtFee("10.00");

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setToAccount(toAccount);
        ocpBillPayment.setFee(topUpFee);
        ocpBillPayment.setBankRefId("*********");

        AdditionalParam additionalParam = new AdditionalParam();
        additionalParam.setName("Msg");
        additionalParam.setValue("นายทดสอบ| ");
        ocpBillPayment.setAdditionalParams(Collections.singletonList(additionalParam));

        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenReturn(ocpBillPayment);

        Mockito.when(topUpFleetCardFeignClient.fleetCardVerify(Mockito.any(), Mockito.any())).thenThrow(CallNotPermittedException.class);

        mockGetTransactionId();

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setAccountNumber("*********");
        topUpVerifyRequest.setBillerCompCode("0012");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("");
        topUpVerifyRequest.setNote("");

        DepositAccount depositAccount = mockDataDepositAccount();
        Assertions.assertThrows(TMBCommonException.class, () -> billPaymentOCPOnlineAndOfflineVerifyService.billPayOnlinePaymentVerify(
                topUpVerifyRequest, headers, transId, billerTopUpDetailResponse, activityBillPayVerifyEvent, depositAccount, "caseValue"));
    }

    @Test
    void billPayOnlinePaymentVerifyWhenBillerIsEasyPassShouldSetTopUpAccountNameInCacheTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        BillerTopUpDetailResponse billerTopUpDetailResponse = mockDataBillerTopUpDetailResponse("True Move", "2", "12");

        mockFetchTransactionLimit();

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        OCPAccountPayment toAccount = new OCPAccountPayment();
        toAccount.setAccountId("**********");
        toAccount.setAccountType("DDA");

        OCPFee topUpFee = new OCPFee();
        topUpFee.setBillPmtFee("10.00");

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setToAccount(toAccount);
        ocpBillPayment.setFee(topUpFee);
        ocpBillPayment.setBankRefId("*********");

        AdditionalParam additionalParam = new AdditionalParam();
        additionalParam.setName("Msg");
        additionalParam.setValue("นายทดสอบ| ");
        ocpBillPayment.setAdditionalParams(Collections.singletonList(additionalParam));
        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenReturn(ocpBillPayment);

        mockGetTransactionId();

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setAccountNumber("*********");
        topUpVerifyRequest.setBillerCompCode("2151");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("");
        topUpVerifyRequest.setNote("");
        topUpVerifyRequest.setIsBillPay(false);

        DepositAccount depositAccount = mockDataDepositAccount();


        billPaymentOCPOnlineAndOfflineVerifyService.billPayOnlinePaymentVerify(
                topUpVerifyRequest, headers, transId, billerTopUpDetailResponse, activityBillPayVerifyEvent, depositAccount, "caseValue");

        ArgumentCaptor<OCPBillPayment> cacheData = ArgumentCaptor.forClass(OCPBillPayment.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(eq(transId), cacheData.capture());

        OCPBillPayment topUpETEPayment = cacheData.getValue();
        Assertions.assertEquals("นายทดสอบ", topUpETEPayment.getPaymentCacheData().getTopUpAccountName());
    }

    @Test
    void billPayOnlinePaymentVerifyWhenBillerIsNotFleetCardAndNotEasyPassShouldNoteSetTranCodeAndTopUpAccountNameInCacheTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        BillerTopUpDetailResponse billerTopUpDetailResponse = mockDataBillerTopUpDetailResponse("True Move", "2", "5");

        mockFetchTransactionLimit();

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        OCPAccountPayment toAccount = new OCPAccountPayment();
        toAccount.setAccountId("**********");
        toAccount.setAccountType("DDA");

        OCPFee topUpFee = new OCPFee();
        topUpFee.setBillPmtFee("10.00");

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setToAccount(toAccount);
        ocpBillPayment.setFee(topUpFee);
        ocpBillPayment.setBankRefId("*********");

        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenReturn(ocpBillPayment);


        Mockito.when(commonPaymentService.getTransactionId(Mockito.anyString(), Mockito.anyInt())).thenReturn(transId);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setAccountNumber("*********");
        topUpVerifyRequest.setBillerCompCode("2704");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("");
        topUpVerifyRequest.setNote("");
        topUpVerifyRequest.setToFavoriteName("ReqToFavoriteName");
        topUpVerifyRequest.setIsBillPay(false);

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setProductNickname("From Nick Name");
        depositAccount.setAccountName("From Name");
        depositAccount.setWaiveFeeForBillpay("0");
        depositAccount.setAccountNumber("*********");
        billPaymentOCPOnlineAndOfflineVerifyService.billPayOnlinePaymentVerify(
                topUpVerifyRequest, headers, transId, billerTopUpDetailResponse, activityBillPayVerifyEvent, depositAccount, "caseValue");

        ArgumentCaptor<OCPBillPayment> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OCPBillPayment.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(eq(transId), dataSaveToCacheCaptor.capture());

        OCPBillPayment actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertNull(actualDataSaveToCache.getPaymentCacheData().getTopUpAccountName());
        Assertions.assertNull(actualDataSaveToCache.getTranCode());
        Assertions.assertEquals("ReqToFavoriteName", actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        Assertions.assertEquals(depositAccount.getProductNickname(), actualDataSaveToCache.getPaymentCacheData().getFromAccountNickname());
        Assertions.assertEquals(depositAccount.getAccountName(), actualDataSaveToCache.getPaymentCacheData().getFromAccountName());
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen() throws TMBCommonException {
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), eq(false), any(CustomerCrmProfile.class))).thenReturn(new VerifyTransactionResult(true, null, new CommonAuthenResult()));
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen() throws TMBCommonException {
        CommonAuthenResult commonAuthenResult = new CommonAuthenResult()
                .setRequireCommonAuthen(true)
                .setIsForceFR(true)
                .setPinFree(true);
        VerifyTransactionResult resultWhenExecuteCommonAuthen = new VerifyTransactionResult(false, null, commonAuthenResult);
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), eq(false), any(CustomerCrmProfile.class))).thenReturn(resultWhenExecuteCommonAuthen);
    }

    private void mockValidateIsRequireVerifyTransactionTopUp() throws TMBCommonException {
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransactionForTopUp(any(), any(), eq(false), any(CustomerCrmProfile.class))).thenReturn(new VerifyTransactionResult(false, new FaceRecognizeResponse().setIsRequireFr(true).setPaymentAccuUsgAmt(BigDecimal.ZERO), new CommonAuthenResult()));
    }

    @Test
    void billPayOnlinePaymentVerifySuccessWhenWaiveFeeShouldReturnTopUpVerifyResponseWithFeeZeroTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        BillerTopUpDetailResponse billerTopUpDetailResponse = mockDataBillerTopUpDetailResponse("True Move", "2", "5");

        mockFetchTransactionLimit();

        mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen();

        OCPAccountPayment toAccount = new OCPAccountPayment();
        toAccount.setAccountId("**********");
        toAccount.setAccountType("DDA");

        OCPFee topUpFee = new OCPFee();
        topUpFee.setBillPmtFee("10.00");

        WaiveOCP waiveOCP = new WaiveOCP();
        waiveOCP.setFlag("Y");
        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setToAccount(toAccount);
        ocpBillPayment.setFee(topUpFee);
        ocpBillPayment.setBankRefId("*********");
        ocpBillPayment.setAdditionalParams(Collections.emptyList());
        ocpBillPayment.setWaive(waiveOCP);
        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenReturn(ocpBillPayment);

        mockGetTransactionId();

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setAccountNumber("*********");
        topUpVerifyRequest.setBillerCompCode("2074");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("");
        topUpVerifyRequest.setNote("");

        DepositAccount depositAccount = mockDataDepositAccount();
        depositAccount.setWaiveFeeForBillpay("1");

        TopUpVerifyResponse actual = billPaymentOCPOnlineAndOfflineVerifyService.billPayOnlinePaymentVerify(
                topUpVerifyRequest, headers, transId, billerTopUpDetailResponse, activityBillPayVerifyEvent, depositAccount, "caseValue");

        Assertions.assertNotNull(actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());
        Assertions.assertNull(actual.getTopUpRef());
        Assertions.assertNull(actual.getTopUpAccountName());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation());
    }

    @Test
    void billPayOnlinePaymentVerifyWhenFromAccountIsCreditCardShouldAddBillerNameInAdditionalParamTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        BillerTopUpDetailResponse billerTopUpDetailResponse = mockDataBillerTopUpDetailResponse("1-2-Call", "1", "12");

        mockFetchTransactionLimit();

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        OCPAccountPayment toAccount = new OCPAccountPayment();
        toAccount.setAccountId("**********");
        toAccount.setAccountType("DDA");

        OCPFee topUpFee = new OCPFee();
        topUpFee.setBillPmtFee("10.00");

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setToAccount(toAccount);
        ocpBillPayment.setFee(topUpFee);
        ocpBillPayment.setBankRefId("*********");

        ocpBillPayment.setAdditionalParams(new ArrayList<>());
        AdditionalParam additionalParam = new AdditionalParam();
        additionalParam.setName("Msg");
        additionalParam.setValue("นายทดสอบ| ");
        ocpBillPayment.getAdditionalParams().add(additionalParam);
        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenReturn(ocpBillPayment);

        mockGetTransactionId();

        CreditCardDetail creditCardDetail = new CreditCardDetail();
        creditCardDetail.setCardId("cardId");
        creditCardDetail.setCardInfo(new CardInfo("cardEmbossingName1", "expiredBy"));
        creditCardDetail.setProductId("productId");
        Mockito.when(billPaymentCreditCardService.getCreditCardDetailByAccountId(Mockito.anyString(), Mockito.anyString())).thenReturn(creditCardDetail);

        Mockito.when(commonPaymentService.getMerChantIdByCompCode(Mockito.anyString(), Mockito.anyString())).thenReturn("merchantId");

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setBillerCompCode("2151");
        topUpVerifyRequest.setReference1("**********");
        topUpVerifyRequest.setReference2("");
        topUpVerifyRequest.setNote("");
        topUpVerifyRequest.setToFavoriteName("ReqToFavoriteName");
        topUpVerifyRequest.setIsBillPay(false);

        topUpVerifyRequest.setIsCreditCard(true);
        topUpVerifyRequest.setAccountNumber("0000000050085330012000690");
        topUpVerifyRequest.setCardNumber("462856XXXXXX0690");

        DepositAccount depositAccount = mockDataDepositAccount();
        TopUpVerifyResponse actual = billPaymentOCPOnlineAndOfflineVerifyService.billPayOnlinePaymentVerify(
                topUpVerifyRequest, headers, transId, billerTopUpDetailResponse, activityBillPayVerifyEvent, depositAccount, "caseValue");

        Assertions.assertNotNull(actual.getFee());
        Assertions.assertNotNull(actual.getTransId());
        Assertions.assertNotNull(actual.getTopUpRef());
        Assertions.assertNotNull(actual.getTopUpAccountName());

        ArgumentCaptor<OCPBillPayment> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OCPBillPayment.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(eq(transId), dataSaveToCacheCaptor.capture());

        OCPBillPayment actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        int lastIndex = actualDataSaveToCache.getAdditionalParams().size() - 1;
        Assertions.assertEquals("billerName", actualDataSaveToCache.getAdditionalParams().get(lastIndex).getName());
        Assertions.assertEquals("1-2-Call", actualDataSaveToCache.getAdditionalParams().get(lastIndex).getValue());
    }

    @Test
    void billPayOnlinePaymentVerifyWhenThrowTMBCommonExceptionShouldCallSaveLogActivityTest() throws TMBCommonException {
        BillerTopUpDetailResponse billerTopUpDetailResponse = mockDataBillerTopUpDetailResponse("True Move", "2", "5");

        TMBCommonException exception = new TMBCommonException("TRUE_10001", ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.OK, null);
        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenThrow(exception);

        mockGetTransactionId();

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setAccountNumber("*********");
        topUpVerifyRequest.setBillerCompCode("2704");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("");
        topUpVerifyRequest.setNote("");

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setProductNickname("Nick Name");
        depositAccount.setAccountNumber("*********");
        Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentOCPOnlineAndOfflineVerifyService.billPayOnlinePaymentVerify(
                        topUpVerifyRequest, headers, transId, billerTopUpDetailResponse, activityBillPayVerifyEvent, depositAccount, "caseValue")
        );

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());
    }

    @Test
    void billPayOnlinePaymentVerifyWhenThrowFeignExceptionShouldCallSaveLogActivityTest() throws TMBCommonException {
        BillerTopUpDetailResponse billerTopUpDetailResponse = mockDataBillerTopUpDetailResponse("True Move", "2", "5");

        mockGetTransactionId();

        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenThrow(FeignException.FeignClientException.class);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setAccountNumber("*********");
        topUpVerifyRequest.setBillerCompCode("2704");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("");
        topUpVerifyRequest.setNote("");

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setProductNickname("Nick Name");
        depositAccount.setAccountNumber("*********");
        Assertions.assertThrows(Exception.class, () ->
                billPaymentOCPOnlineAndOfflineVerifyService.billPayOnlinePaymentVerify(
                        topUpVerifyRequest, headers, transId, billerTopUpDetailResponse, activityBillPayVerifyEvent, depositAccount, "caseValue")
        );

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());
    }

    @Test
    void getTopUpAccountNameFromTransactionShouldReturnNullTest() {
        String actual = billPaymentOCPOnlineAndOfflineVerifyService.getTopUpAccountNameFromTransaction(Collections.emptyList());

        Assertions.assertNull(actual);
    }

    @Test
    void getTopUpAccountNameFromTransactionShouldReturnNameTest() {
        AdditionalParam additionalParam = new AdditionalParam();
        additionalParam.setName("Msg");
        additionalParam.setValue("นาย TMB4 ทดสอบ\n\n|- - -|5|2");
        List<AdditionalParam> additionalParams = Collections.singletonList(additionalParam);

        String actual = billPaymentOCPOnlineAndOfflineVerifyService.getTopUpAccountNameFromTransaction(additionalParams);

        Assertions.assertEquals("นาย TMB4 ทดสอบ", actual);
    }

    @Test
    void getBillPayOnlinePaymentDetailSuccessTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        OCPBillPayment billPayOnlineETETransaction = new OCPBillPayment();

        Mockito.when(commonPaymentService.fetchBillerBillPayDetail("1234", correlationId)).thenReturn(Factory.createBillerDetail());
        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenReturn(billPayOnlineETETransaction);

        Mockito.doReturn("0.00").when(billPaymentOCPOnlineAndOfflineVerifyService).getDebtByAdditional(Mockito.any());

        Mockito.doReturn(true).when(billPaymentOCPOnlineAndOfflineVerifyService).getOptionFullAndSpecifiedOfAmount(Mockito.any(), Mockito.anyString());

        PaymentDetailResponse paymentDetailResponse = new PaymentDetailResponse();
        Mockito.doReturn(paymentDetailResponse).when(billPaymentOCPOnlineAndOfflineVerifyService).initResponse(Mockito.any(), Mockito.anyString(), Mockito.anyBoolean());

        TopUpVerifyRequest paymentDetailRequest = new TopUpVerifyRequest();
        paymentDetailRequest.setBillerCompCode("1234");
        PaymentDetailResponse actual = billPaymentOCPOnlineAndOfflineVerifyService.getBillPayOnlinePaymentDetail(correlationId, paymentDetailRequest);

        Assertions.assertNotNull(actual);
    }

    @Test
    void initResponseWhenOptionFullAndSpecifiedOfAmountTrueShouldReturnAmountFullAndSpecifiedOptionTest() {
        String compCode = "9999";
        String debtOfBiller = "200.00";

        BillerTopUpDetailResponse billerDetail = Factory.createBillerDetail(compCode);
        PaymentDetailResponse actual = billPaymentOCPOnlineAndOfflineVerifyService.initResponse(billerDetail, debtOfBiller, true);

        Assertions.assertEquals(PaymentServiceConstant.BILL_PAYMENT_TYPE_FULL_SPECIFIED, actual.getPaymentType());
        Assertions.assertEquals(debtOfBiller, actual.getFull().getAmount());
        Assertions.assertEquals("0.00", actual.getSpecified().getAmount());
    }

    @Test
    void initResponseWhenOptionFullAndSpecifiedOfAmountFalseShouldReturnAmountFullOptionTest() {
        BillerTopUpDetailResponse billerBillPayDetail = new BillerTopUpDetailResponse();
        billerBillPayDetail.setBillerInfo(new BillerInfoResponse());
        billerBillPayDetail.getBillerInfo().setIsFullPayment(false);
        billerBillPayDetail.getBillerInfo().setBillerCategoryCode("01");
        billerBillPayDetail.getBillerInfo().setBillerCompCode("1234");

        PaymentDetailResponse actual = billPaymentOCPOnlineAndOfflineVerifyService.initResponse(billerBillPayDetail, "200.00", false);

        Assertions.assertEquals(PaymentServiceConstant.BILL_PAYMENT_TYPE_FULL, actual.getPaymentType());
        Assertions.assertNull(actual.getFull());
        Assertions.assertEquals("200.00", actual.getAmount().getAmount());
        Assertions.assertTrue(actual.getAmount().isEditable());
    }

    @Test
    void initResponsePaymentDetailWhenBillerIsTelCoShouldSetFrequencyWeeklyFalseAndTabsOfScheduleNullTest() {
        BillerTopUpDetailResponse billerBillPayDetail = Factory.createBillerDetail("2218");
        billerBillPayDetail.getBillerInfo().setIsFullPayment(false);
        billerBillPayDetail.getBillerInfo().setBillerCategoryCode("07");

        PaymentDetailResponse actual = billPaymentOCPOnlineAndOfflineVerifyService.initResponse(billerBillPayDetail, "200.00", false);

        Assertions.assertNull(actual.getScheduleConfig().getPhrase().getTitleEnterPage());
        Assertions.assertEquals(PHRASE_BILLER_TELCO_DETAIL_ENTER_PAGE, actual.getScheduleConfig().getPhrase().getDetailEnterPage());
        Assertions.assertEquals(PHRASE_BILLER_TELCO_DETAIL_ENTER_PAGE, actual.getScheduleConfig().getPhrase().getReviewPage());

        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isOnce());
        Assertions.assertFalse(actual.getScheduleConfig().getFrequency().isWeekly());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isMonthly());

        Assertions.assertNull(actual.getScheduleConfig().getTabsOfSchedule());
    }

    @Test
    void initResponsePaymentDetailWhenBillerNotTelCoShouldSetFrequencyAllIsTrueAndTabsOfScheduleIsFULLAndShouldNotSetPhraseTest() {
        BillerTopUpDetailResponse billerBillPayDetail = Factory.createBillerDetail("2219");
        billerBillPayDetail.getBillerInfo().setIsFullPayment(false);
        billerBillPayDetail.getBillerInfo().setBillerCategoryCode("21");

        PaymentDetailResponse actual = billPaymentOCPOnlineAndOfflineVerifyService.initResponse(billerBillPayDetail, "200.00", false);

        Assertions.assertNull(actual.getScheduleConfig().getPhrase());

        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isOnce());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isWeekly());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isMonthly());

        Assertions.assertEquals("FULL", actual.getScheduleConfig().getTabsOfSchedule());
    }

    @Test
    void initResponsePaymentDetailWhenBillerIsRDShouldSetFrequencyAllIsTrueAndTabsOfScheduleIsNullAndShouldNotSetPhraseTest() {
        BillerTopUpDetailResponse billerBillPayDetail = Factory.createBillerDetail("2988");
        billerBillPayDetail.getBillerInfo().setIsFullPayment(false);
        billerBillPayDetail.getBillerInfo().setBillerCategoryCode("21");

        PaymentDetailResponse actual = billPaymentOCPOnlineAndOfflineVerifyService.initResponse(billerBillPayDetail, "200.00", false);

        Assertions.assertNull(actual.getScheduleConfig().getPhrase());
        Assertions.assertEquals("FULL", actual.getScheduleConfig().getTabsOfSchedule());
        Assertions.assertTrue(new ReflectionEquals(new ScheduleConfigFrequency(true, true, true))
                .matches(actual.getScheduleConfig().getFrequency()));
    }

    @Test
    void getOptionFullAndSpecifiedOfAmountWhenHaveComeCompOfAmountFullSpecifiedOptionShouldReturnTrueTest() throws TMBCommonException {
        BillpayModel billpayData = new BillpayModel();
        billpayData.setBillerAmountFullSpecified(Collections.singletonList("9999"));

        Mockito.when(paymentConfigurationService.fetchBillpayConfiguration(Mockito.anyString()))
                .thenReturn(billpayData);

        TopUpVerifyRequest paymentDetailRequest = new TopUpVerifyRequest();
        paymentDetailRequest.setBillerCompCode("9999");
        boolean actual = billPaymentOCPOnlineAndOfflineVerifyService.getOptionFullAndSpecifiedOfAmount(paymentDetailRequest, correlationId);

        Assertions.assertTrue(actual);
    }

    @Test
    void getDebtOfBillerWhenAdditionalParamsNullShouldReturnZeroTest() {
        String actual = billPaymentOCPOnlineAndOfflineVerifyService.getDebtByAdditional(null);

        Assertions.assertEquals("0.00", actual);
    }

    @Test
    void getDebtOfBillerWhenDebtHaveValueShouldReturnDebtValueTest() {
        List<AdditionalParam> additionalParams = Collections.singletonList(new AdditionalParam());
        additionalParams.get(0).setName("FullAmt");
        additionalParams.get(0).setValue("200.00");
        String actual = billPaymentOCPOnlineAndOfflineVerifyService.getDebtByAdditional(additionalParams);

        Assertions.assertEquals("200.00", actual);
    }

    @Test
    void getDebtOfBillerWhenDebtNotHaveValueShouldReturnZeroTest() {
        List<AdditionalParam> additionalParams = Collections.singletonList(new AdditionalParam());
        additionalParams.get(0).setName("WrongNameNotFullAmt");
        additionalParams.get(0).setValue("200.00");
        String actual = billPaymentOCPOnlineAndOfflineVerifyService.getDebtByAdditional(additionalParams);

        Assertions.assertEquals("0.00", actual);
    }

    @Test
    void billPayOnlinePaymentVerifyWhenBillerIsEasyPassShouldSetPmtRefIdAndInvoiceNumInDataToSaveToCacheTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        BillerTopUpDetailResponse billerTopUpDetailResponse = Factory.createBillerDetail("2151");

        mockFetchTransactionLimit();

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        OCPAccountPayment toAccount = new OCPAccountPayment();
        toAccount.setAccountId("**********");
        toAccount.setAccountType("DDA");

        OCPFee topUpFee = new OCPFee();
        topUpFee.setBillPmtFee("10.00");

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setToAccount(toAccount);
        ocpBillPayment.setFee(topUpFee);
        ocpBillPayment.setBankRefId("*********");
        ocpBillPayment.setAdditionalParams(Collections.emptyList());
        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenReturn(ocpBillPayment);

        Mockito.when(commonPaymentService.getTransactionId(Mockito.anyString(), Mockito.anyInt())).thenReturn(transId);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setAccountNumber("*********");
        topUpVerifyRequest.setBillerCompCode("2151");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("");
        topUpVerifyRequest.setNote("");

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setProductNickname("From Nick Name");
        depositAccount.setAccountName("From Name");
        depositAccount.setWaiveFeeForBillpay("0");
        depositAccount.setAccountNumber("*********");
        billPaymentOCPOnlineAndOfflineVerifyService.billPayOnlinePaymentVerify(
                topUpVerifyRequest, headers, transId, billerTopUpDetailResponse, activityBillPayVerifyEvent, depositAccount, "caseValue");

        ArgumentCaptor<OCPBillPayment> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OCPBillPayment.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(eq(transId), dataSaveToCacheCaptor.capture());

        OCPBillPayment actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals(topUpVerifyRequest.getReference1(), actualDataSaveToCache.getPmtRefIdent());
        Assertions.assertEquals("*********", actualDataSaveToCache.getInvoiceNum());
    }

    @Test
    void billPayOnlinePaymentVerifyWhenDeepLinkCallFromProtectionShouldSetDataCorrectlyTest() throws SQLException, TMBCommonException, TMBCustomCommonExceptionWithResponse, JsonProcessingException {
        BillerTopUpDetailResponse billerTopUpDetailResponse = Factory.createBillerDetail("2151");

        mockFetchTransactionLimit();

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        DeepLinkRequest deepLinkRequest = new DeepLinkRequest();
        deepLinkRequest.setRef1("12345");
        deepLinkRequest.setRef2("67890");
        deepLinkRequest.setAmount("2000.00");
        deepLinkRequest.setCompCode("1234");
        deepLinkRequest.setCallFrom("Protection");
        Mockito.when(commonPaymentService.getDeepLinkRequestFromRedis("deeplink")).thenReturn(deepLinkRequest);

        OCPAccountPayment toAccount = new OCPAccountPayment();
        toAccount.setAccountId("**********");
        toAccount.setAccountType("DDA");

        OCPFee topUpFee = new OCPFee();
        topUpFee.setBillPmtFee("10.00");

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setToAccount(toAccount);
        ocpBillPayment.setFee(topUpFee);
        ocpBillPayment.setBankRefId("*********");
        ocpBillPayment.setAdditionalParams(Collections.emptyList());
        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenReturn(ocpBillPayment);

        Mockito.when(commonPaymentService.getTransactionId(Mockito.anyString(), Mockito.anyInt())).thenReturn(transId);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setAccountNumber("*********0");
        topUpVerifyRequest.setBillerCompCode("2151");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("");
        topUpVerifyRequest.setNote("");
        topUpVerifyRequest.setDeepLinkTransData("deeplink");

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setProductNickname("From Nick Name");
        depositAccount.setAccountName("From Name");
        depositAccount.setWaiveFeeForBillpay("0");
        depositAccount.setAccountNumber("*********0");
        billPaymentOCPOnlineAndOfflineVerifyService.billPayOnlinePaymentVerify(
                topUpVerifyRequest, headers, transId, billerTopUpDetailResponse, activityBillPayVerifyEvent, depositAccount, "caseValue");

        ArgumentCaptor<OCPBillPayment> argumentCaptor = ArgumentCaptor.forClass(OCPBillPayment.class);
        Mockito.verify(topUpETEService, Mockito.times(1))
                .verifyOCPPayment(argumentCaptor.capture());

        OCPBillPayment ocpBillPaymentCapture = argumentCaptor.getValue();
        Assertions.assertEquals("2000.00", ocpBillPaymentCapture.getAmount());
        Assertions.assertEquals("1234", ocpBillPaymentCapture.getCompCode());
    }

    @Test
    void getBillPayOnlinePaymentDetailWhenCompcodeIsAisOnTopSuccessTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        AdditionalParam additionalParam = generateAdditionalParam("paymentMode", "PREPAID");

        OCPBillPayment billPayOnlineETETransaction = new OCPBillPayment();
        billPayOnlineETETransaction.setAdditionalParams(Collections.singletonList(additionalParam));
        billPayOnlineETETransaction.setRef1("ref1");
        billPayOnlineETETransaction.setRef2("ref2");
        billPayOnlineETETransaction.setAmount("123.456");

        ArgumentCaptor<OCPBillPayment> eteRequestCaptor = ArgumentCaptor.forClass(OCPBillPayment.class);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail("MP01", correlationId)).thenReturn(Factory.createBillerDetail());
        Mockito.when(topUpETEService.verifyOCPPayment(eteRequestCaptor.capture())).thenReturn(billPayOnlineETETransaction);

        Mockito.doReturn("0.00").when(billPaymentOCPOnlineAndOfflineVerifyService).getDebtByAdditional(Mockito.any());

        Mockito.doReturn(true).when(billPaymentOCPOnlineAndOfflineVerifyService).getOptionFullAndSpecifiedOfAmount(Mockito.any(), Mockito.anyString());

        PaymentDetailResponse paymentDetailResponse = new PaymentDetailResponse();
        Mockito.doReturn(paymentDetailResponse).when(billPaymentOCPOnlineAndOfflineVerifyService).initResponse(Mockito.any(), Mockito.anyString(), Mockito.anyBoolean());

        TopUpVerifyRequest paymentDetailRequest = new TopUpVerifyRequest();
        paymentDetailRequest.setBillerCompCode("MP01");
        PaymentDetailResponse actual = billPaymentOCPOnlineAndOfflineVerifyService.getBillPayOnlinePaymentDetail(correlationId, paymentDetailRequest);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals("3070", actual.getCompCode());
        Assertions.assertNotNull(actual.getReference1());
        Assertions.assertEquals("ref1", actual.getReference1().getValue());
        Assertions.assertNotNull(actual.getReference2());
        Assertions.assertEquals("ref2", actual.getReference2().getValue());
        Assertions.assertFalse(actual.getReference2().isEditable());
        Assertions.assertNotNull(actual.getAmount());
        Assertions.assertEquals("123.456", actual.getAmount().getAmount());
        Assertions.assertFalse(actual.getAmount().isEditable());

        Assertions.assertEquals(BILL_REF_1_ETE_REQUEST_AIS_ON_TOP, eteRequestCaptor.getValue().getRef1());
    }

    @Test
    void getBillPayOnlinePaymentDetailWhenCompcodeIsAisOnTopPostpaidSuccessTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        OCPBillPayment billPayOnlineETETransaction = new OCPBillPayment();
        billPayOnlineETETransaction.setAdditionalParams(Collections.singletonList(generateAdditionalParam("paymentMode", "POSTPAID")));
        billPayOnlineETETransaction.setRef1("ref1");
        billPayOnlineETETransaction.setRef2("ref2");
        billPayOnlineETETransaction.setAmount("123.456");

        ArgumentCaptor<OCPBillPayment> eteRequestCaptor = ArgumentCaptor.forClass(OCPBillPayment.class);

        Mockito.when(commonPaymentService.fetchBillerBillPayDetail("MP01", correlationId)).thenReturn(Factory.createBillerDetail());
        Mockito.when(topUpETEService.verifyOCPPayment(eteRequestCaptor.capture())).thenReturn(billPayOnlineETETransaction);

        Mockito.doReturn("0.00").when(billPaymentOCPOnlineAndOfflineVerifyService).getDebtByAdditional(Mockito.any());

        Mockito.doReturn(true).when(billPaymentOCPOnlineAndOfflineVerifyService).getOptionFullAndSpecifiedOfAmount(Mockito.any(), Mockito.anyString());

        PaymentDetailResponse paymentDetailResponse = new PaymentDetailResponse();
        Mockito.doReturn(paymentDetailResponse).when(billPaymentOCPOnlineAndOfflineVerifyService).initResponse(Mockito.any(), Mockito.anyString(), Mockito.anyBoolean());

        TopUpVerifyRequest paymentDetailRequest = new TopUpVerifyRequest();
        paymentDetailRequest.setBillerCompCode("MP01");
        PaymentDetailResponse actual = billPaymentOCPOnlineAndOfflineVerifyService.getBillPayOnlinePaymentDetail(correlationId, paymentDetailRequest);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals("3071", actual.getCompCode());
        Assertions.assertNotNull(actual.getReference1());
        Assertions.assertEquals("ref1", actual.getReference1().getValue());
        Assertions.assertNotNull(actual.getReference2());
        Assertions.assertEquals("ref2", actual.getReference2().getValue());
        Assertions.assertFalse(actual.getReference2().isEditable());
        Assertions.assertNotNull(actual.getAmount());
        Assertions.assertEquals("123.456", actual.getAmount().getAmount());
        Assertions.assertFalse(actual.getAmount().isEditable());

        Assertions.assertEquals(BILL_REF_1_ETE_REQUEST_AIS_ON_TOP, eteRequestCaptor.getValue().getRef1());
    }

    @Test
    void getBillPayOnlinePaymentDetailWhenCompcodeIsAisOnTopWithoutPaymentModeThrowTMBCommonException() throws TMBCommonException {
        OCPBillPayment billPayOnlineETETransaction = new OCPBillPayment();
        billPayOnlineETETransaction.setRef2("ref2");
        billPayOnlineETETransaction.setAmount("123.456");

        Mockito.when(commonPaymentService.fetchBillerBillPayDetail("MP01", correlationId)).thenReturn(Factory.createBillerDetail());
        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenReturn(billPayOnlineETETransaction);

        Mockito.doReturn("0.00").when(billPaymentOCPOnlineAndOfflineVerifyService).getDebtByAdditional(Mockito.any());

        Mockito.doReturn(true).when(billPaymentOCPOnlineAndOfflineVerifyService).getOptionFullAndSpecifiedOfAmount(Mockito.any(), Mockito.anyString());

        PaymentDetailResponse paymentDetailResponse = new PaymentDetailResponse();
        Mockito.doReturn(paymentDetailResponse).when(billPaymentOCPOnlineAndOfflineVerifyService).initResponse(Mockito.any(), Mockito.anyString(), Mockito.anyBoolean());

        TopUpVerifyRequest paymentDetailRequest = new TopUpVerifyRequest();
        paymentDetailRequest.setBillerCompCode("MP01");

        Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentOCPOnlineAndOfflineVerifyService.getBillPayOnlinePaymentDetail(correlationId, paymentDetailRequest));
    }

    @Test
    void billPayOnlinePaymentVerifyWhenCompcodeIsAisOnTopSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        AdditionalParam additionalParam = generateAdditionalParam("Msg", "นายทดสอบ| ");
        AdditionalParam additionalParam2 = generateAdditionalParam("paymentMode", "PREPAID");
        AdditionalParam additionalParam3 = generateAdditionalParam("packageName", "Package Name");
        AdditionalParam additionalParam4 = generateAdditionalParam("packageNameTh", "Package Name TH");
        AdditionalParam additionalParam5 = generateAdditionalParam("packageDetail", "Package Detail");
        AdditionalParam additionalParam6 = generateAdditionalParam("packageDetailTh", "Package Detail TH");
        List<AdditionalParam> additionalParams =
                List.of(additionalParam, additionalParam2, additionalParam3, additionalParam4, additionalParam5, additionalParam6);

        TopUpVerifyResponse actual = mockGetTopUpVerifyResponseSuccess(additionalParams, BILL_COMP_CODE_AIS_ON_TOP_PREPAID);

        Assertions.assertNotNull(actual.getFee());
        Assertions.assertNotNull(actual.getTransId());
        Assertions.assertNotNull(actual.getAmount());
        Assertions.assertEquals("123.456", actual.getAmount());
        var telcoPackage = actual.getTelcoPackage();
        Assertions.assertEquals("Package Name", telcoPackage.getNameEN());
        Assertions.assertEquals("Package Name TH", telcoPackage.getNameTH());
        Assertions.assertEquals("Package Detail", telcoPackage.getDetailEN());
        Assertions.assertEquals("Package Detail TH", telcoPackage.getDetailTH());
    }

    @Test
    void billPayOnlinePaymentVerifyWhenCompcodeIsAisOnTopPostpaidSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        AdditionalParam additionalParam = generateAdditionalParam("Msg", "นายทดสอบ| ");
        AdditionalParam additionalParam2 = generateAdditionalParam("paymentMode", "POSTPAID");
        AdditionalParam additionalParam3 = generateAdditionalParam("packageName", "Package Name");
        AdditionalParam additionalParam4 = generateAdditionalParam("packageNameTh", "Package Name TH");
        List<AdditionalParam> additionalParams = List.of(additionalParam, additionalParam2, additionalParam3, additionalParam4);

        TopUpVerifyResponse actual = mockGetTopUpVerifyResponseSuccess(additionalParams, BILL_COMP_CODE_AIS_ON_TOP_POSTPAID);

        Assertions.assertNotNull(actual.getFee());
        Assertions.assertNotNull(actual.getTransId());
        Assertions.assertNotNull(actual.getAmount());
        Assertions.assertEquals("123.456", actual.getAmount());
        var telcoPackage = actual.getTelcoPackage();
        Assertions.assertEquals("Package Name", telcoPackage.getNameEN());
        Assertions.assertEquals("Package Name TH", telcoPackage.getNameTH());
    }

    @Test
    void getBillPayOnlinePaymentDetailWhenCompCodeIsTrueMoveHDataPackageSuccessTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        OCPBillPayment billPayOnlineETETransaction = new OCPBillPayment();
        billPayOnlineETETransaction.setRef1("ref1");
        billPayOnlineETETransaction.setRef2("ref2");
        billPayOnlineETETransaction.setAmount("123.456");

        BillerTopUpDetailResponse billerDetail = Factory.createBillerDetail();
        billerDetail.getBillerInfo().setIsFullPayment(true);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(TOP_UP_COMP_CODE_TRUE_MOVE_H_DATA_PACKAGE, correlationId)).thenReturn(billerDetail);

        ArgumentCaptor<OCPBillPayment> eteRequestCaptor = ArgumentCaptor.forClass(OCPBillPayment.class);
        Mockito.when(topUpETEService.verifyOCPPayment(eteRequestCaptor.capture())).thenReturn(billPayOnlineETETransaction);

        Mockito.doReturn(false).when(billPaymentOCPOnlineAndOfflineVerifyService).getOptionFullAndSpecifiedOfAmount(Mockito.any(), Mockito.anyString());

        TopUpVerifyRequest paymentDetailRequest = new TopUpVerifyRequest();
        paymentDetailRequest.setBillerCompCode(TOP_UP_COMP_CODE_TRUE_MOVE_H_DATA_PACKAGE);
        paymentDetailRequest.setReference1("mobile number ref1");
        PaymentDetailResponse actual = billPaymentOCPOnlineAndOfflineVerifyService.getBillPayOnlinePaymentDetail(correlationId, paymentDetailRequest);

        Assertions.assertEquals("123.456", actual.getAmount().getAmount());
        Assertions.assertFalse(actual.getAmount().isEditable());

        Assertions.assertEquals("mobile number ref1", eteRequestCaptor.getValue().getRef1());
    }

    @Test
    void billPayOnlinePaymentVerifyWhenCompCodeIsTrueMoveHDataPackageSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        List<AdditionalParam> additionalParams = List.of(
                generateAdditionalParam("packageName", "Package Name"),
                generateAdditionalParam("packageDetail", "Package Detail")
        );

        TopUpVerifyResponse actual = mockGetTopUpVerifyResponseSuccess(additionalParams, TOP_UP_COMP_CODE_TRUE_MOVE_H_DATA_PACKAGE);

        Assertions.assertNotNull(actual.getTransId());
        Assertions.assertNotNull(actual.getAmount());
        Assertions.assertEquals("123.456", actual.getAmount());
        var telcoPackage = actual.getTelcoPackage();
        Assertions.assertEquals("Package Name", telcoPackage.getNameEN());
        Assertions.assertEquals("Package Name", telcoPackage.getNameTH());
        Assertions.assertEquals("Package Detail", telcoPackage.getDetailEN());
        Assertions.assertEquals("Package Detail", telcoPackage.getDetailTH());
    }


    @Test
    void billPayOnlinePaymentVerifyWhenCaseTopUpSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        mockFetchTransactionLimit();

        mockValidateIsRequireVerifyTransactionTopUp();

        mockVerifyOCPPayment();

        mockGetTransactionId();

        TopUpVerifyRequest topUpVerifyRequest = mockDataTopUpVerifyRequest();

        BillerTopUpDetailResponse billerTopUpDetailResponse = mockDataBillerTopUpDetailResponse("True Move", "2", "5");

        DepositAccount depositAccount = mockDataDepositAccount();

        TopUpVerifyResponse actual = billPaymentOCPOnlineAndOfflineVerifyService.billPayOnlinePaymentVerify(
                topUpVerifyRequest, headers, transId, billerTopUpDetailResponse, activityBillPayVerifyEvent, depositAccount,
                BILLER_PAYMENT_TOPUP);

        Assertions.assertNotNull(actual.getFee());
        Assertions.assertNotNull(actual.getTransId());
        Assertions.assertTrue(actual.isRequireFr());

        ArgumentCaptor<OCPBillPayment> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OCPBillPayment.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(eq(transId), dataSaveToCacheCaptor.capture());

        OCPBillPayment actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals(BigDecimal.ZERO, actualDataSaveToCache.getPaymentCacheData().getPaymentAccuUsgAmt());
        Assertions.assertTrue(actualDataSaveToCache.getPaymentCacheData().isRequireFr());
    }

    private static TopUpVerifyRequest mockDataTopUpVerifyRequest() {
        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setAccountNumber("*********");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("");
        topUpVerifyRequest.setNote("");
        topUpVerifyRequest.setToFavoriteName("ReqToFavoriteName");
        topUpVerifyRequest.setIsBillPay(false);
        topUpVerifyRequest.setBillerCompCode("2704");
        return topUpVerifyRequest;
    }

    private static DepositAccount mockDataDepositAccount() {
        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setProductNickname("Nick Name");
        depositAccount.setWaiveFeeForBillpay("0");
        depositAccount.setAccountNumber("*********");
        return depositAccount;
    }

    private static BillerTopUpDetailResponse mockDataBillerTopUpDetailResponse(String nameEn, String billerMethod, String paymentMethod) {
        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        billerInfoResponse.setNameEn(nameEn);
        billerInfoResponse.setBillerMethod(billerMethod);
        billerInfoResponse.setPaymentMethod(paymentMethod);
        billerInfoResponse.setBillerGroupType(PaymentServiceConstant.BILLER_GROUP_TOP_UP);
        BillerTopUpDetailResponse billerTopUpDetailResponse = new BillerTopUpDetailResponse();
        billerTopUpDetailResponse.setBillerInfo(billerInfoResponse);
        return billerTopUpDetailResponse;
    }

    private void mockGetTransactionId() {
        Mockito.when(commonPaymentService.getTransactionId(Mockito.anyString(), Mockito.anyInt())).thenReturn("REF00001");
    }

    private void mockVerifyOCPPayment() throws TMBCommonException {
        OCPAccountPayment toAccount = new OCPAccountPayment();
        toAccount.setAccountId("**********");
        toAccount.setAccountType("DDA");

        OCPFee topUpFee = new OCPFee();
        topUpFee.setBillPmtFee("10.00");

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setToAccount(toAccount);
        ocpBillPayment.setFee(topUpFee);
        ocpBillPayment.setBankRefId("*********");
        ocpBillPayment.setRef2("ref2");
        ocpBillPayment.setAmount("123.456");

        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenReturn(ocpBillPayment);
    }

    private void mockFetchTransactionLimit() throws SQLException {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setEbMaxLimitAmtCurrent(1000.00);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(1);
        customerCrmProfile.setPinFreeSeetingFlag("Y");
        Mockito.when(billPaymentValidateTransaction.fetchTransactionLimit(correlationId, crmId)).thenReturn(customerCrmProfile);
    }

    private AdditionalParam generateAdditionalParam(String name, String value) {
        var ap = new AdditionalParam();
        ap.setName(name);
        ap.setValue(value);
        return ap;
    }

    private TopUpVerifyResponse mockGetTopUpVerifyResponseSuccess(List<AdditionalParam> additionalParams, String compCode) throws TMBCommonException, SQLException, TMBCustomCommonExceptionWithResponse, JsonProcessingException {
        BillerTopUpDetailResponse billerTopUpDetailResponse = mockDataBillerTopUpDetailResponse("Ignore EN", "2", "5");

        mockFetchTransactionLimit();

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        OCPAccountPayment toAccount = new OCPAccountPayment();
        toAccount.setAccountId("**********");
        toAccount.setAccountType("DDA");

        OCPFee topUpFee = new OCPFee();
        topUpFee.setBillPmtFee("10.00");

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setToAccount(toAccount);
        ocpBillPayment.setFee(topUpFee);
        ocpBillPayment.setBankRefId("*********");
        ocpBillPayment.setRef2("ref2");
        ocpBillPayment.setAmount("123.456");

        ocpBillPayment.setAdditionalParams(additionalParams);
        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenReturn(ocpBillPayment);

        mockGetTransactionId();

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setAccountNumber("*********");
        topUpVerifyRequest.setBillerCompCode("2151");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("");
        topUpVerifyRequest.setNote("");
        topUpVerifyRequest.setToFavoriteName("ReqToFavoriteName");
        topUpVerifyRequest.setIsBillPay(false);
        topUpVerifyRequest.setBillerCompCode(compCode);

        DepositAccount depositAccount = mockDataDepositAccount();

        return billPaymentOCPOnlineAndOfflineVerifyService.billPayOnlinePaymentVerify(
                topUpVerifyRequest, headers, transId, billerTopUpDetailResponse, activityBillPayVerifyEvent, depositAccount, "caseValue");
    }

    @ParameterizedTest
    @CsvSource({"3069", "3070", "3071"})
    void validateDuplicateRefWhenFoundRefsInCacheShouldThrow(String compCode) throws JsonProcessingException {
        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setCompCode(compCode);
        ocpBillPayment.setRef1("111");
        ocpBillPayment.setRef2("222");

        Map<String, String> mapRef = new HashMap<>();
        mapRef.put("ref1", "111");
        mapRef.put("ref2", "222");
        Mockito.when(cacheService.get(any())).thenReturn(TMBUtils.convertJavaObjectToString(mapRef));

        Assertions.assertThrows(TMBCustomCommonExceptionWithResponse.class,
                () -> billPaymentOCPOnlineAndOfflineVerifyService.validateDuplicateRef(crmId, ocpBillPayment));
    }

    @ParameterizedTest
    @CsvSource({"1234, 000", "000, 4321", "000, 000"})
    void validateDuplicateRefWhenPartiallyMatchedShouldNotThrow(String ref1, String ref2) throws JsonProcessingException {
        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setCompCode("3069");
        ocpBillPayment.setRef1(ref1);
        ocpBillPayment.setRef2(ref2);

        Map<String, String> mapRef = new HashMap<>();
        mapRef.put("ref1", "1234");
        mapRef.put("ref2", "4321");
        Mockito.when(cacheService.get(any())).thenReturn(TMBUtils.convertJavaObjectToString(mapRef));

        Assertions.assertDoesNotThrow(() -> billPaymentOCPOnlineAndOfflineVerifyService.validateDuplicateRef(crmId, ocpBillPayment));
    }

    @Test
    void validateDuplicateRefWhenFailedToGetCache() {
        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setCompCode("3070");
        ocpBillPayment.setRef1("987");
        ocpBillPayment.setRef2("789");

        Mockito.when(cacheService.get(any())).thenThrow(RuntimeException.class);

        Assertions.assertDoesNotThrow(() -> billPaymentOCPOnlineAndOfflineVerifyService.validateDuplicateRef(crmId, ocpBillPayment));
    }
}