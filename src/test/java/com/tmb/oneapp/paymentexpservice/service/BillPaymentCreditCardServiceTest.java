package com.tmb.oneapp.paymentexpservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.oneapp.paymentexpservice.client.CreditCardConfirmFeignClient;
import com.tmb.oneapp.paymentexpservice.client.CreditCardFeignClient;
import com.tmb.oneapp.paymentexpservice.factory.Factory;
import com.tmb.oneapp.paymentexpservice.model.AccountBalance;
import com.tmb.oneapp.paymentexpservice.model.ActivityTopUpEvent;
import com.tmb.oneapp.paymentexpservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.DepositAccount;
import com.tmb.oneapp.paymentexpservice.model.ETEError;
import com.tmb.oneapp.paymentexpservice.model.PaymentCacheData;
import com.tmb.oneapp.paymentexpservice.model.PaymentDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyResponse;
import com.tmb.oneapp.paymentexpservice.model.TransferActivities;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityBillPayVerifyEvent;
import com.tmb.oneapp.paymentexpservice.model.commonauth.VerifyTransactionResult;
import com.tmb.oneapp.paymentexpservice.model.creditcard.BillPaymentCreditCard;
import com.tmb.oneapp.paymentexpservice.model.creditcard.BilledStatementResponse;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CardInfo;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CardStatement;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CardStatus;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCardConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCardConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCardDetail;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCardFee;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CustomerDetail;
import com.tmb.oneapp.paymentexpservice.model.creditcard.GetCardResponse;
import com.tmb.oneapp.paymentexpservice.model.creditcard.PayeeCard;
import com.tmb.oneapp.paymentexpservice.model.creditcard.PayerAccount;
import com.tmb.oneapp.paymentexpservice.model.deeplink.DeepLinkRequest;
import com.tmb.oneapp.paymentexpservice.model.financiallog.FinancialCreditCard;
import com.tmb.oneapp.paymentexpservice.model.notification.NotificationCreditCard;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import feign.FeignException;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.Collections;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACTIVITY_LOG_BILL_PAY_VERIFY_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.APP_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_PAYMENT_TYPE_FULL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_PAYMENT_TYPE_FULL_MIN_SPECIFIED;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.CHANNEL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DAILY_LIMIT_TYPE_BILL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_MODEL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.OS_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PRE_LOGIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TXN_TYPE_BILL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_CREDIT_CARD_DETAIL_ENTER_PAGE;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_CREDIT_CARD_REVIEW_PAGE;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.SCHEDULE_CONFIG_TAB_FULL_AND_MINIMUM;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.SCHEDULE_CONFIG_TAB_ONLY_AMOUNT;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;


@ExtendWith(MockitoExtension.class)
class BillPaymentCreditCardServiceTest {

    @Mock
    CreditCardFeignClient creditCardFeignClient;

    @Mock
    CreditCardConfirmFeignClient creditCardConfirmFeignClient;

    @Mock
    CommonPaymentService commonPaymentService;

    @Mock
    LogService logService;

    @Mock
    BillPaymentValidateTransaction billPaymentValidateTransaction;

    @Mock
    TransactionLimitService transactionLimitService;
    @Mock
    OauthService oauthService;

    @Spy
    @InjectMocks
    BillPaymentCreditCardService billPaymentCreditCardService;

    String crmId;
    String correlationId;
    String reference1;
    String transId;
    ActivityTopUpEvent activityEvent;
    HttpHeaders headers;
    TopUpVerifyRequest topUpVerifyRequest;
    TopUpConfirmRequest confirmRequest;
    BillerTopUpDetailResponse billerDetail;
    ActivityBillPayVerifyEvent activityBillPayVerifyEvent;
    DepositAccount depositAccount;
    PaymentCacheData paymentCacheData;
    String transactionDateTime;

    @BeforeEach
    void setUp() {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        transId = "TOPUP_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        reference1 = "**********";
        activityEvent = new ActivityTopUpEvent("", "", "");
        headers = new HttpHeaders();
        headers.add(X_CRMID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);
        headers.add(OS_VERSION, "");
        headers.add(CHANNEL, "");
        headers.add(APP_VERSION, "");
        headers.add(DEVICE_ID, "");
        headers.add(DEVICE_MODEL, "");

        topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("110.00");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("12345");
        topUpVerifyRequest.setBillerCompCode("5003");
        topUpVerifyRequest.setAccountNumber("**********");

        billerDetail = Factory.createBillerDetail();
        depositAccount = Factory.createDepositAccount();
        paymentCacheData = Factory.createPaymentCacheDataWithNickName();
        paymentCacheData.setToFavoriteNickname("ReqToFavoriteName");
        paymentCacheData.setBillerResp(billerDetail);

        activityBillPayVerifyEvent = new ActivityBillPayVerifyEvent(
                correlationId,
                ACTIVITY_LOG_BILL_PAY_VERIFY_ID,
                headers,
                topUpVerifyRequest,
                billerDetail
        );

        transactionDateTime = "*************";

        confirmRequest = new TopUpConfirmRequest();
        confirmRequest.setTransId(transId);
    }

    @Test
    void getCreditCardDetailWhenOwnerTest() throws JsonProcessingException, TMBCommonException {
        String accountId = "**********";
        GetCardResponse cardResponse = new GetCardResponse();
        cardResponse.setCreditCard(new CreditCardDetail());
        cardResponse.getCreditCard().setCustomer(new CustomerDetail());
        cardResponse.getCreditCard().getCustomer().setRmId(crmId);
        cardResponse.getCreditCard().setAccountId(accountId);
        Mockito.when(creditCardFeignClient.getCreditCardDetailsByCardId(any(), any()))
                .thenReturn(ResponseEntity.ok(cardResponse));

        BilledStatementResponse billedStatementResponse = new BilledStatementResponse();
        billedStatementResponse.setCardStatement(new CardStatement());
        billedStatementResponse.getCardStatement().setMinimumDue(new BigDecimal("100.00"));
        billedStatementResponse.getCardStatement().setTotalAmountDue(new BigDecimal("1000.00"));
        Mockito.when(creditCardFeignClient.getUnBilledStatement(any(), any()))
                .thenReturn(ResponseEntity.ok(billedStatementResponse));

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setReference1(reference1);
        PaymentDetailResponse actual = billPaymentCreditCardService.getCreditCardPaymentDetail(crmId, correlationId, topUpVerifyRequest);

        Assertions.assertTrue(actual.isOwner());
        Assertions.assertNull(actual.getAmount());
        Assertions.assertFalse(actual.isDirectDebit());
        Assertions.assertEquals(BILL_PAYMENT_TYPE_FULL_MIN_SPECIFIED, actual.getPaymentType());
        Assertions.assertEquals("1000.00", actual.getFull().getAmount());
        Assertions.assertEquals("100.00", actual.getMin().getAmount());
        Assertions.assertEquals("0.00", actual.getSpecified().getAmount());
        Assertions.assertFalse(actual.getFull().isEditable());
        Assertions.assertFalse(actual.getMin().isEditable());
        Assertions.assertTrue(actual.getSpecified().isEditable());

        Assertions.assertEquals(SCHEDULE_CONFIG_TAB_FULL_AND_MINIMUM, actual.getScheduleConfig().getTabsOfSchedule());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isOnce());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isMonthly());
        Assertions.assertFalse(actual.getScheduleConfig().getFrequency().isWeekly());
        Assertions.assertNull(actual.getScheduleConfig().getPhrase().getTitleEnterPage());
        Assertions.assertEquals(PHRASE_BILLER_CREDIT_CARD_DETAIL_ENTER_PAGE, actual.getScheduleConfig().getPhrase().getDetailEnterPage());
        Assertions.assertEquals(PHRASE_BILLER_CREDIT_CARD_REVIEW_PAGE, actual.getScheduleConfig().getPhrase().getReviewPage());
    }

    @Test
    void getCreditCardDetailWhenNotOwnerTest() throws JsonProcessingException, TMBCommonException {
        GetCardResponse cardResponse = new GetCardResponse();
        cardResponse.setCreditCard(new CreditCardDetail());
        cardResponse.getCreditCard().setCustomer(new CustomerDetail());
        cardResponse.getCreditCard().getCustomer().setRmId("************");
        Mockito.when(creditCardFeignClient.getCreditCardDetailsByCardId(any(), any()))
                .thenReturn(ResponseEntity.ok(cardResponse));

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setReference1(reference1);
        PaymentDetailResponse actual = billPaymentCreditCardService.getCreditCardPaymentDetail(crmId, correlationId, topUpVerifyRequest);

        Assertions.assertFalse(actual.isOwner());
        Assertions.assertNull(actual.getFull());
        Assertions.assertNull(actual.getMin());
        Assertions.assertNull(actual.getSpecified());
        Assertions.assertFalse(actual.isDirectDebit());
        Assertions.assertEquals(BILL_PAYMENT_TYPE_FULL, actual.getPaymentType());
        Assertions.assertTrue(actual.getAmount().isEditable());
        Assertions.assertEquals("0.00", actual.getAmount().getAmount());
        Assertions.assertNull(actual.getScheduleConfig().getPhrase());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isOnce());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isWeekly());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isMonthly());
        Assertions.assertEquals(SCHEDULE_CONFIG_TAB_ONLY_AMOUNT, actual.getScheduleConfig().getTabsOfSchedule());
    }

    @Test
    void getCreditCardDetailWhenRef2NotBlankTest() throws TMBCommonException, JsonProcessingException {
        reference1 = null;
        GetCardResponse cardResponse = new GetCardResponse();
        cardResponse.setCreditCard(new CreditCardDetail());
        cardResponse.getCreditCard().setCustomer(new CustomerDetail());
        cardResponse.getCreditCard().getCustomer().setRmId("************");
        Mockito.when(creditCardFeignClient.getCreditCardDetailsByAccountId(any(), any()))
                .thenReturn(ResponseEntity.ok(cardResponse));

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setReference1(reference1);
        topUpVerifyRequest.setReference2("0000000050083520184010630");
        PaymentDetailResponse actual = billPaymentCreditCardService.getCreditCardPaymentDetail(crmId, correlationId, topUpVerifyRequest);

        Assertions.assertFalse(actual.isOwner());
        Assertions.assertNull(actual.getFull());
        Assertions.assertNull(actual.getMin());
        Assertions.assertNull(actual.getSpecified());
        Assertions.assertEquals(BILL_PAYMENT_TYPE_FULL, actual.getPaymentType());
        Assertions.assertTrue(actual.getAmount().isEditable());
        Assertions.assertEquals("0.00", actual.getAmount().getAmount());
    }

    @Test
    void creditCardPaymentVerifySuccessShouldCallSaveActivityLogTest() throws TMBCommonException, SQLException, JsonProcessingException {
        CreditCardDetail cardDetail = new CreditCardDetail();
        cardDetail.setCustomer(new CustomerDetail());
        cardDetail.getCustomer().setRmId("************");
        cardDetail.setAccountId("**********");
        cardDetail.setCardId("0000xxxxxxxx1111");
        cardDetail.setCardInfo(new CardInfo());
        cardDetail.getCardInfo().setCardEmbossingName1("TMB  ทดสอบ");
        cardDetail.getCardInfo().setExpiredBy("2701");
        cardDetail.setCardStatus(new CardStatus());
        cardDetail.getCardStatus().setCardPloanFlag("1");
        cardDetail.setProductId("VSOSMT");
        GetCardResponse cardResponse = new GetCardResponse();
        cardResponse.setCreditCard(cardDetail);
        Mockito.when(creditCardFeignClient.getCreditCardDetailsByCardId(any(), any()))
                .thenReturn(ResponseEntity.ok(cardResponse));

        CustomerCrmProfile dailyLimit = new CustomerCrmProfile();
        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(dailyLimit);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnNotRequireConfirmPin();

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setReference1(reference1);
        topUpVerifyRequest.setAccountNumber("********");
        topUpVerifyRequest.setBillerCompCode("0699");
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setToFavoriteName("ReqToFavoriteName");

        TopUpVerifyResponse actual = billPaymentCreditCardService
                .creditCardPaymentVerify(topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertNull(actual.getTopUpAccountName());
        Assertions.assertNull(actual.getTopUpRef());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
        Assertions.assertEquals(transId, actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());

        ArgumentCaptor<CreditCardConfirmRequest> dataSaveToCacheCaptor = ArgumentCaptor.forClass(CreditCardConfirmRequest.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.eq(transId), dataSaveToCacheCaptor.capture());

        CreditCardConfirmRequest actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals("ReqToFavoriteName", actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        Assertions.assertEquals(depositAccount.getProductNickname(), actualDataSaveToCache.getPaymentCacheData().getFromAccountNickname());
        Assertions.assertEquals(depositAccount.getAccountName(), actualDataSaveToCache.getPaymentCacheData().getFromAccountName());
        Assertions.assertEquals("**********", actualDataSaveToCache.getPaymentCacheData().getOriginRef2());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(any());
    }

    @Test
    void creditCardPaymentVerifyWhenExecuteCommonAuthenticationShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException {
        CreditCardDetail cardDetail = new CreditCardDetail();
        cardDetail.setCustomer(new CustomerDetail());
        cardDetail.getCustomer().setRmId("************");
        cardDetail.setAccountId("**********");
        cardDetail.setCardId("0000xxxxxxxx1111");
        cardDetail.setCardInfo(new CardInfo());
        cardDetail.getCardInfo().setCardEmbossingName1("TMB  ทดสอบ");
        cardDetail.getCardInfo().setExpiredBy("2701");
        cardDetail.setCardStatus(new CardStatus());
        cardDetail.getCardStatus().setCardPloanFlag("1");
        cardDetail.setProductId("VSOSMT");
        GetCardResponse cardResponse = new GetCardResponse();
        cardResponse.setCreditCard(cardDetail);
        Mockito.when(creditCardFeignClient.getCreditCardDetailsByCardId(any(), any()))
                .thenReturn(ResponseEntity.ok(cardResponse));

        CustomerCrmProfile dailyLimit = new CustomerCrmProfile();
        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(dailyLimit);

        mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen();

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setReference1(reference1);
        topUpVerifyRequest.setAccountNumber("********");
        topUpVerifyRequest.setBillerCompCode("0699");
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setToFavoriteName("ReqToFavoriteName");

        TopUpVerifyResponse actual = billPaymentCreditCardService
                .creditCardPaymentVerify(topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
    }

    @Test
    void creditCardPaymentVerifyShouldCallSaveLogWhenThrowException() {
        Mockito.when(creditCardFeignClient.getCreditCardDetailsByCardId(any(), any())).thenThrow(FeignException.class);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setReference1(reference1);
        topUpVerifyRequest.setAccountNumber("********");
        topUpVerifyRequest.setBillerCompCode("0699");
        topUpVerifyRequest.setAmount("100.00");
        Assertions.assertThrows(Exception.class, () ->
                billPaymentCreditCardService.creditCardPaymentVerify(topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount)
        );

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(any());
    }

    @ParameterizedTest
    @CsvSource({"1, 0.00", "0, 20.00"})
    void initVerifyResponseCorrectlyTest(String waiveFee, String expected) {
        BigDecimal fee = new BigDecimal("20.00");
        TopUpVerifyResponse actual = billPaymentCreditCardService.initVerifyResponse(waiveFee, transId, true, fee);

        Assertions.assertEquals(expected, actual.getFee().toString());
    }

    @Test
    void creditCardPaymentConfirmSuccessWhenPayByOwnerShouldNotCallUpdateDailyUsageTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        BillPaymentCreditCard billPaymentCreditCard = new BillPaymentCreditCard();
        billPaymentCreditCard.setAmount("100.00");
        billPaymentCreditCard.setRef1("**********123456");
        billPaymentCreditCard.setPayerAccount(new PayerAccount());
        billPaymentCreditCard.getPayerAccount().setId("**********0000");
        billPaymentCreditCard.setPayeeCard(new PayeeCard());
        billPaymentCreditCard.getPayeeCard().setAccountId("***************");
        billPaymentCreditCard.setFee(new CreditCardFee());
        billPaymentCreditCard.getFee().setBillPayment("10.00");
        billPaymentCreditCard.setEpayCode("REF00001");
        billPaymentCreditCard.setCompCode("1234");
        CreditCardConfirmRequest confirmRequestRequest = new CreditCardConfirmRequest();
        confirmRequestRequest.setBillPayment(billPaymentCreditCard);
        paymentCacheData.setToFavoriteNickname("Credit Card");
        confirmRequestRequest.setPaymentCacheData(paymentCacheData);
        confirmRequestRequest.getPaymentCacheData().setPayByOwner(true);
        confirmRequestRequest.getPaymentCacheData().setRequirePin(false);
        confirmRequestRequest.getPaymentCacheData().getBillerResp().getBillerInfo().setBillerCategoryCode("06");
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, CreditCardConfirmRequest.class, crmId))
                .thenReturn(confirmRequestRequest);

        Mockito.doNothing().when(transactionLimitService).validateDailyLimitExceeded(any(), anyDouble(), eq(confirmRequestRequest.getPaymentCacheData().isPayByOwner()), eq(DAILY_LIMIT_TYPE_BILL));

        BillPaymentCreditCard billPayment = new BillPaymentCreditCard();
        billPayment.setPayerAccount(new PayerAccount());
        billPayment.getPayerAccount().setBalances(new AccountBalance());
        billPayment.getPayerAccount().getBalances().setAvailable("1000.00");
        CreditCardConfirmResponse confirmResponse = new CreditCardConfirmResponse();
        confirmResponse.setBillPayment(billPayment);
        Mockito.when(creditCardConfirmFeignClient.billPaymentConfirm(any(), any()))
                .thenReturn(ResponseEntity.ok(confirmResponse));

        Mockito.when(commonPaymentService.getCurrentDateTime()).thenReturn(transactionDateTime);

        TopUpConfirmResponse actual = billPaymentCreditCardService.creditCardPaymentConfirm(crmId, correlationId, confirmRequest, headers);

        Assertions.assertNotNull(actual.getReference1());
        Assertions.assertNotNull(actual.getTopUpCreatedDatetime());
        Assertions.assertEquals("1000.00", actual.getRemainingBalance());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(any());

        ArgumentCaptor<FinancialCreditCard> finLogCaptor = ArgumentCaptor.forClass(FinancialCreditCard.class);
        ArgumentCaptor<TransferActivities> transferActivitiesCapture = ArgumentCaptor.forClass(TransferActivities.class);

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), finLogCaptor.capture(), transferActivitiesCapture.capture());

        FinancialCreditCard actualFinLog = finLogCaptor.getValue();
        Assertions.assertEquals("Credit Card", actualFinLog.getToAccNickName());
        Assertions.assertEquals(confirmRequestRequest.getPaymentCacheData().getBillerResp().getBillerInfo().getNameEn(), actualFinLog.getToAccName());
        Assertions.assertEquals(correlationId, actualFinLog.getActivityRefId());
        Assertions.assertEquals(confirmRequestRequest.getPaymentCacheData().getFromAccountNickname(), actualFinLog.getFromAccNickName());
        Assertions.assertEquals(confirmRequestRequest.getPaymentCacheData().getFromAccountName(), actualFinLog.getFromAccName());
        Assertions.assertEquals(TXN_TYPE_BILL, actualFinLog.getTxnType());

        Assertions.assertEquals("1234-56XX-XXXX-3456", transferActivitiesCapture.getValue().getBillerRef1());
        Assertions.assertEquals(actualFinLog.getTxnDt(), transferActivitiesCapture.getValue().getTransactionDate());

        NotificationCreditCard notificationCreditCardExpected = new NotificationCreditCard();
        notificationCreditCardExpected.setAddDateTimeEN("18 Aug 22 - 1:37 PM");
        notificationCreditCardExpected.setAddDateTimeTH("18 ส.ค. 65 - 13:37 น.");

        ArgumentCaptor<NotificationCreditCard> notificationCreditCardArgumentCaptor = ArgumentCaptor.forClass(NotificationCreditCard.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .sendENotificationPayment(notificationCreditCardArgumentCaptor.capture());

        Assertions.assertEquals(notificationCreditCardExpected.getAddDateTimeTH(), notificationCreditCardArgumentCaptor.getValue().getAddDateTimeTH());
        Assertions.assertEquals(notificationCreditCardExpected.getAddDateTimeEN(), notificationCreditCardArgumentCaptor.getValue().getAddDateTimeEN());
    }

    @Test
    void creditCardPaymentConfirmSuccessWhenNotPayByOwnerShouldVerifyPinAndCallUpdateDailyUsageTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        BillPaymentCreditCard billPaymentCreditCard = new BillPaymentCreditCard();
        billPaymentCreditCard.setAmount("100.10");
        billPaymentCreditCard.setRef1("*********");
        billPaymentCreditCard.setPayerAccount(new PayerAccount());
        billPaymentCreditCard.getPayerAccount().setId("**********0000");
        billPaymentCreditCard.setPayeeCard(new PayeeCard());
        billPaymentCreditCard.getPayeeCard().setAccountId("***************");
        billPaymentCreditCard.setFee(new CreditCardFee());
        billPaymentCreditCard.getFee().setBillPayment("10.00");
        billPaymentCreditCard.setEpayCode("REF00001");
        CreditCardConfirmRequest confirmRequestRequest = new CreditCardConfirmRequest();
        confirmRequestRequest.setBillPayment(billPaymentCreditCard);
        paymentCacheData.setRequirePin(true);
        confirmRequestRequest.setPaymentCacheData(paymentCacheData);
        confirmRequestRequest.getPaymentCacheData().setPayByOwner(false);
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, CreditCardConfirmRequest.class, crmId))
                .thenReturn(confirmRequestRequest);

        Mockito.doNothing().when(oauthService).validateAuthentication(any(), any(), any(), anyString());

        Mockito.doNothing().when(transactionLimitService).validateDailyLimitExceeded(any(), anyDouble(), eq(confirmRequestRequest.getPaymentCacheData().isPayByOwner()), eq(DAILY_LIMIT_TYPE_BILL));

        BillPaymentCreditCard billPayment = new BillPaymentCreditCard();
        billPayment.setPayerAccount(new PayerAccount());
        billPayment.getPayerAccount().setBalances(new AccountBalance());
        billPayment.getPayerAccount().getBalances().setAvailable("1000.00");
        CreditCardConfirmResponse confirmResponse = new CreditCardConfirmResponse();
        confirmResponse.setBillPayment(billPayment);
        Mockito.when(creditCardConfirmFeignClient.billPaymentConfirm(any(), any()))
                .thenReturn(ResponseEntity.ok(confirmResponse));

        Mockito.when(commonPaymentService.getCurrentDateTime()).thenReturn(transactionDateTime);

        TopUpConfirmResponse actual = billPaymentCreditCardService.creditCardPaymentConfirm(crmId, correlationId, confirmRequest, headers);

        Assertions.assertNotNull(actual.getReference1());
        Assertions.assertNotNull(actual.getTopUpCreatedDatetime());
        Assertions.assertEquals("1000.00", actual.getRemainingBalance());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(any());

        Mockito.verify(billPaymentValidateTransaction, Mockito.times(1))
                .updateBillDailyUsage(eq(crmId), eq(100.10), any(), anyBoolean(), eq(correlationId));
        NotificationCreditCard notificationCreditCardExpected = new NotificationCreditCard();
        notificationCreditCardExpected.setAddDateTimeEN("18 Aug 22 - 1:37 PM");
        notificationCreditCardExpected.setAddDateTimeTH("18 ส.ค. 65 - 13:37 น.");

        ArgumentCaptor<NotificationCreditCard> notificationCreditCardArgumentCaptor = ArgumentCaptor.forClass(NotificationCreditCard.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .sendENotificationPayment(notificationCreditCardArgumentCaptor.capture());

        Assertions.assertEquals(notificationCreditCardExpected.getAddDateTimeTH(), notificationCreditCardArgumentCaptor.getValue().getAddDateTimeTH());
        Assertions.assertEquals(notificationCreditCardExpected.getAddDateTimeEN(), notificationCreditCardArgumentCaptor.getValue().getAddDateTimeEN());
    }

    @Test
    void creditCardPaymentConfirmFailedThrowTMBCommonExceptionShouldCallWriteActivityLogWriteFinLogTest() throws TMBCommonException, JsonProcessingException {
        BillPaymentCreditCard billPaymentCreditCard = new BillPaymentCreditCard();
        billPaymentCreditCard.setAmount("100.00");
        billPaymentCreditCard.setRef1("*********");
        billPaymentCreditCard.setPayerAccount(new PayerAccount());
        billPaymentCreditCard.getPayerAccount().setId("payer-id");
        billPaymentCreditCard.setPayeeCard(new PayeeCard());
        billPaymentCreditCard.getPayeeCard().setAccountId("***************");
        billPaymentCreditCard.setFee(new CreditCardFee());
        billPaymentCreditCard.getFee().setBillPayment("10.00");
        CreditCardConfirmRequest confirmRequestRequest = new CreditCardConfirmRequest();
        confirmRequestRequest.setBillPayment(billPaymentCreditCard);
        paymentCacheData.setRequirePin(true);
        confirmRequestRequest.setPaymentCacheData(paymentCacheData);
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, CreditCardConfirmRequest.class, crmId))
                .thenReturn(confirmRequestRequest);

        CreditCardConfirmResponse confirmResponse = new CreditCardConfirmResponse();
        ETEError eteError = new ETEError();
        eteError.setNamespace("CRE");
        eteError.setCode("1234");
        eteError.setMessage("Failed");
        confirmResponse.setErrors(Collections.singletonList(eteError));
        Mockito.when(creditCardConfirmFeignClient.billPaymentConfirm(any(), any()))
                .thenReturn(ResponseEntity.ok(confirmResponse));

        headers.set(PRE_LOGIN, "true");
        Assertions.assertThrows(Exception.class, () ->
                billPaymentCreditCardService.creditCardPaymentConfirm(crmId, correlationId, confirmRequest, headers)
        );

        Mockito.verify(oauthService, Mockito.times(1))
                .validateAuthentication(any(), any(), any(), anyString());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(any());

        Mockito.verify(logService, Mockito.times((1)))
                .saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), any(), any());
    }

    @Test
    void creditCardPaymentConfirmFailedThrowFeignExceptionShouldCallWriteLogTest() throws TMBCommonException, JsonProcessingException {
        BillPaymentCreditCard billPaymentCreditCard = new BillPaymentCreditCard();
        billPaymentCreditCard.setAmount("100.00");
        billPaymentCreditCard.setRef1("*********");
        billPaymentCreditCard.setPayerAccount(new PayerAccount());
        billPaymentCreditCard.getPayerAccount().setId("payer-id");
        billPaymentCreditCard.setPayeeCard(new PayeeCard());
        billPaymentCreditCard.getPayeeCard().setAccountId("***************");
        billPaymentCreditCard.setFee(new CreditCardFee());
        billPaymentCreditCard.getFee().setBillPayment("10.00");

        CreditCardConfirmRequest confirmRequestRequest = new CreditCardConfirmRequest();
        confirmRequestRequest.setBillPayment(billPaymentCreditCard);
        paymentCacheData.setRequirePin(false);
        confirmRequestRequest.setPaymentCacheData(paymentCacheData);

        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, CreditCardConfirmRequest.class, crmId))
                .thenReturn(confirmRequestRequest);

        Mockito.when(creditCardConfirmFeignClient.billPaymentConfirm(any(), any()))
                .thenThrow(FeignException.class);

        Assertions.assertThrows(FeignException.class, () ->
                billPaymentCreditCardService.creditCardPaymentConfirm(crmId, correlationId, confirmRequest, headers)
        );

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(any());

        Mockito.verify(logService, Mockito.times((1)))
                .saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), any(), any());
    }

    @Test
    void creditCardPaymentConfirmFailedCircuitBreakerTrippedShouldCallWriteLogTest() throws TMBCommonException, JsonProcessingException {
        BillPaymentCreditCard billPaymentCreditCard = new BillPaymentCreditCard();
        billPaymentCreditCard.setAmount("100.00");
        billPaymentCreditCard.setRef1("*********");
        billPaymentCreditCard.setPayerAccount(new PayerAccount());
        billPaymentCreditCard.getPayerAccount().setId("payer-id");
        billPaymentCreditCard.setPayeeCard(new PayeeCard());
        billPaymentCreditCard.getPayeeCard().setAccountId("***************");
        billPaymentCreditCard.setFee(new CreditCardFee());
        billPaymentCreditCard.getFee().setBillPayment("10.00");

        CreditCardConfirmRequest confirmRequestRequest = new CreditCardConfirmRequest();
        confirmRequestRequest.setBillPayment(billPaymentCreditCard);
        paymentCacheData.setRequirePin(false);
        confirmRequestRequest.setPaymentCacheData(paymentCacheData);

        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, CreditCardConfirmRequest.class, crmId))
                .thenReturn(confirmRequestRequest);

        Mockito.when(creditCardConfirmFeignClient.billPaymentConfirm(Mockito.any(), Mockito.any()))
                .thenThrow(CallNotPermittedException.class);

        Assertions.assertThrows(CallNotPermittedException.class, () ->
                billPaymentCreditCardService.creditCardPaymentConfirm(crmId, correlationId, confirmRequest, headers)
        );

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());

        Mockito.verify(logService, Mockito.times((1)))
                .saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), Mockito.any(), Mockito.any());
    }

    @Test
    void getCreditCardPaymentDetailWhenHaveDeepLinkShouldSuccessTest() throws JsonProcessingException, TMBCommonException {
        String accountId = "**********";

        DeepLinkRequest deepLinkRequest = new DeepLinkRequest();
        deepLinkRequest.setTransId(accountId);
        Mockito.when(commonPaymentService.getDeepLinkRequestFromRedis("deeplink")).thenReturn(deepLinkRequest);

        BilledStatementResponse billedStatementResponse = new BilledStatementResponse();
        billedStatementResponse.setCardStatement(new CardStatement());
        billedStatementResponse.getCardStatement().setMinimumDue(new BigDecimal("100.00"));
        billedStatementResponse.getCardStatement().setTotalAmountDue(new BigDecimal("1000.00"));
        Mockito.when(creditCardFeignClient.getUnBilledStatement(any(), any()))
                .thenReturn(ResponseEntity.ok(billedStatementResponse));

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setReference1(reference1);
        topUpVerifyRequest.setDeepLinkTransData("deeplink");
        PaymentDetailResponse actual = billPaymentCreditCardService.getCreditCardPaymentDetail(crmId, correlationId, topUpVerifyRequest);

        Assertions.assertTrue(actual.isOwner());
        Assertions.assertNull(actual.getAmount());
        Assertions.assertFalse(actual.isDirectDebit());
        Assertions.assertEquals(BILL_PAYMENT_TYPE_FULL_MIN_SPECIFIED, actual.getPaymentType());
        Assertions.assertEquals("1000.00", actual.getFull().getAmount());
        Assertions.assertEquals("100.00", actual.getMin().getAmount());
        Assertions.assertEquals("0.00", actual.getSpecified().getAmount());
        Assertions.assertFalse(actual.getFull().isEditable());
        Assertions.assertFalse(actual.getMin().isEditable());
        Assertions.assertTrue(actual.getSpecified().isEditable());
    }

    @Test
    void creditCardPaymentVerifyWhenCallFromDeeplinkTest() throws TMBCommonException, SQLException, JsonProcessingException {
        String accountId = "************";
        DeepLinkRequest deepLinkRequest = new DeepLinkRequest();
        deepLinkRequest.setTransId(accountId);
        Mockito.when(commonPaymentService.getDeepLinkRequestFromRedis("deeplink")).thenReturn(deepLinkRequest);
        CreditCardDetail cardDetail = new CreditCardDetail();
        cardDetail.setCustomer(new CustomerDetail());
        cardDetail.getCustomer().setRmId("************");
        cardDetail.setAccountId("**********");
        cardDetail.setCardId("0000xxxxxxxx1111");
        cardDetail.setCardInfo(new CardInfo());
        cardDetail.getCardInfo().setCardEmbossingName1("TMB  ทดสอบ");
        cardDetail.getCardInfo().setExpiredBy("2701");
        cardDetail.setCardStatus(new CardStatus());
        cardDetail.getCardStatus().setCardPloanFlag("1");
        cardDetail.setProductId("VSOSMT");
        GetCardResponse cardResponse = new GetCardResponse();
        cardResponse.setCreditCard(cardDetail);
        Mockito.when(creditCardFeignClient.getCreditCardDetailsByAccountId(any(), any()))
                .thenReturn(ResponseEntity.ok(cardResponse));

        CustomerCrmProfile dailyLimit = new CustomerCrmProfile();
        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(dailyLimit);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnNotRequireConfirmPin();

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setReference1(reference1);
        topUpVerifyRequest.setAccountNumber("********");
        topUpVerifyRequest.setBillerCompCode("0699");
        topUpVerifyRequest.setAmount("100.00");
        topUpVerifyRequest.setDeepLinkTransData("deeplink");

        TopUpVerifyResponse actual = billPaymentCreditCardService
                .creditCardPaymentVerify(topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertNull(actual.getTopUpAccountName());
        Assertions.assertNull(actual.getTopUpRef());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
        Assertions.assertEquals(transId, actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());
    }

    @Test
    void creditCardPaymentVerifyWhenRef2IsNotBlankAndRef1isMaskingTest() throws TMBCommonException, SQLException, JsonProcessingException {
        String reference1 = "470680XXXXXX1017";
        String reference2 = "0000000050083520184010630";

        CreditCardDetail cardDetail = new CreditCardDetail();
        cardDetail.setCustomer(new CustomerDetail());
        cardDetail.getCustomer().setRmId("************");
        cardDetail.setAccountId("**********");
        cardDetail.setCardId("0000xxxxxxxx1111");
        cardDetail.setCardInfo(new CardInfo());
        cardDetail.getCardInfo().setCardEmbossingName1("TMB  ทดสอบ");
        cardDetail.getCardInfo().setExpiredBy("2701");
        cardDetail.setCardStatus(new CardStatus());
        cardDetail.getCardStatus().setCardPloanFlag("1");
        cardDetail.setProductId("VSOSMT");
        GetCardResponse cardResponse = new GetCardResponse();
        cardResponse.setCreditCard(cardDetail);
        Mockito.when(creditCardFeignClient.getCreditCardDetailsByAccountId(any(), any()))
                .thenReturn(ResponseEntity.ok(cardResponse));

        CustomerCrmProfile dailyLimit = new CustomerCrmProfile();
        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(dailyLimit);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnNotRequireConfirmPin();

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setReference1(reference1);
        topUpVerifyRequest.setReference2(reference2);
        topUpVerifyRequest.setAccountNumber("********");
        topUpVerifyRequest.setBillerCompCode("0699");
        topUpVerifyRequest.setAmount("100.00");

        TopUpVerifyResponse actual = billPaymentCreditCardService
                .creditCardPaymentVerify(topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertNull(actual.getTopUpAccountName());
        Assertions.assertNull(actual.getTopUpRef());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
        Assertions.assertEquals(transId, actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());
    }

    @Test
    void creditCardPaymentVerifyWhenRef1IsNotBlankAndIsValidNumberTest() throws TMBCommonException, SQLException, JsonProcessingException {
        String reference1 = "***************";
        String reference2 = "0000000050083520184010630";

        CreditCardDetail cardDetail = new CreditCardDetail();
        cardDetail.setCustomer(new CustomerDetail());
        cardDetail.getCustomer().setRmId("************");
        cardDetail.setAccountId("**********");
        cardDetail.setCardId("0000xxxxxxxx1111");
        cardDetail.setCardInfo(new CardInfo());
        cardDetail.getCardInfo().setCardEmbossingName1("TMB  ทดสอบ");
        cardDetail.getCardInfo().setExpiredBy("2701");
        cardDetail.setCardStatus(new CardStatus());
        cardDetail.getCardStatus().setCardPloanFlag("1");
        cardDetail.setProductId("VSOSMT");
        GetCardResponse cardResponse = new GetCardResponse();
        cardResponse.setCreditCard(cardDetail);
        Mockito.when(creditCardFeignClient.getCreditCardDetailsByCardId(any(), any()))
                .thenReturn(ResponseEntity.ok(cardResponse));

        CustomerCrmProfile dailyLimit = new CustomerCrmProfile();
        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(dailyLimit);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnNotRequireConfirmPin();

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setReference1(reference1);
        topUpVerifyRequest.setReference2(reference2);
        topUpVerifyRequest.setAccountNumber("********");
        topUpVerifyRequest.setBillerCompCode("0699");
        topUpVerifyRequest.setAmount("100.00");

        TopUpVerifyResponse actual = billPaymentCreditCardService
                .creditCardPaymentVerify(topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertNull(actual.getTopUpAccountName());
        Assertions.assertNull(actual.getTopUpRef());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
        Assertions.assertEquals(transId, actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());
    }

    @Test
    void creditCardPaymentConfirmSuccessWhenNotPayByOwnerButIsCreditCardShouldNotCallUpdateDailyUsageTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        BillPaymentCreditCard billPaymentCreditCard = new BillPaymentCreditCard();
        billPaymentCreditCard.setAmount("100.10");
        billPaymentCreditCard.setRef1("*********");
        billPaymentCreditCard.setPayerAccount(new PayerAccount());
        billPaymentCreditCard.getPayerAccount().setId("**********0000");
        billPaymentCreditCard.setPayeeCard(new PayeeCard());
        billPaymentCreditCard.getPayeeCard().setAccountId("***************");
        billPaymentCreditCard.setFee(new CreditCardFee());
        billPaymentCreditCard.getFee().setBillPayment("10.00");
        billPaymentCreditCard.setEpayCode("REF00001");
        CreditCardConfirmRequest confirmRequestRequest = new CreditCardConfirmRequest();
        confirmRequestRequest.setBillPayment(billPaymentCreditCard);
        paymentCacheData.setRequirePin(false);
        confirmRequestRequest.setPaymentCacheData(paymentCacheData);
        confirmRequestRequest.getPaymentCacheData().setPayByOwner(false);
        confirmRequestRequest.getPaymentCacheData().setIsCreditCard(true);
        confirmRequestRequest.getPaymentCacheData().setCardNumber("**********123456");

        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, CreditCardConfirmRequest.class, crmId))
                .thenReturn(confirmRequestRequest);

        BillPaymentCreditCard billPayment = new BillPaymentCreditCard();
        billPayment.setPayerAccount(new PayerAccount());
        billPayment.getPayerAccount().setBalances(new AccountBalance());
        billPayment.getPayerAccount().getBalances().setAvailable("1000.00");
        CreditCardConfirmResponse confirmResponse = new CreditCardConfirmResponse();
        confirmResponse.setBillPayment(billPayment);
        Mockito.when(creditCardConfirmFeignClient.billPaymentConfirm(any(), any()))
                .thenReturn(ResponseEntity.ok(confirmResponse));

        Mockito.when(commonPaymentService.getCurrentDateTime()).thenReturn(transactionDateTime);

        TopUpConfirmResponse actual = billPaymentCreditCardService.creditCardPaymentConfirm(crmId, correlationId, confirmRequest, headers);

        Assertions.assertNotNull(actual.getReference1());
        Assertions.assertNotNull(actual.getTopUpCreatedDatetime());
        Assertions.assertEquals("1000.00", actual.getRemainingBalance());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(any());

        Mockito.verify(billPaymentValidateTransaction, Mockito.never())
                .updateBillDailyUsage(anyString(), any(), any(), anyBoolean(), anyString());
        NotificationCreditCard notificationCreditCardExpected = new NotificationCreditCard();
        notificationCreditCardExpected.setAddDateTimeEN("18 Aug 22 - 1:37 PM");
        notificationCreditCardExpected.setAddDateTimeTH("18 ส.ค. 65 - 13:37 น.");

        ArgumentCaptor<NotificationCreditCard> notificationCreditCardArgumentCaptor = ArgumentCaptor.forClass(NotificationCreditCard.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .sendENotificationPayment(notificationCreditCardArgumentCaptor.capture());

        Assertions.assertEquals(notificationCreditCardExpected.getAddDateTimeTH(), notificationCreditCardArgumentCaptor.getValue().getAddDateTimeTH());
        Assertions.assertEquals(notificationCreditCardExpected.getAddDateTimeEN(), notificationCreditCardArgumentCaptor.getValue().getAddDateTimeEN());
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnNotRequireConfirmPin() throws TMBCommonException {
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), eq(false), any(CustomerCrmProfile.class))).thenReturn(new VerifyTransactionResult(false, null, new CommonAuthenResult()));
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen() throws TMBCommonException {
        CommonAuthenResult commonAuthenResult = new CommonAuthenResult()
                .setRequireCommonAuthen(true)
                .setIsForceFR(true)
                .setPinFree(true);
        VerifyTransactionResult resultWhenExecuteCommonAuthen = new VerifyTransactionResult(false, null, commonAuthenResult);
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), eq(false), any(CustomerCrmProfile.class))).thenReturn(resultWhenExecuteCommonAuthen);
    }
}