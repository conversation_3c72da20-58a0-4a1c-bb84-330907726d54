package com.tmb.oneapp.paymentexpservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.zxing.WriterException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.factory.Factory;
import com.tmb.oneapp.paymentexpservice.model.Balance;
import com.tmb.oneapp.paymentexpservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenticationInformation;
import com.tmb.oneapp.paymentexpservice.model.DepositAccount;
import com.tmb.oneapp.paymentexpservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.paymentexpservice.model.LegacyAuthenticationRequest;
import com.tmb.oneapp.paymentexpservice.model.PaymentCacheData;
import com.tmb.oneapp.paymentexpservice.model.PaymentDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.Receiver;
import com.tmb.oneapp.paymentexpservice.model.Sender;
import com.tmb.oneapp.paymentexpservice.model.TPromptPayETEResponse;
import com.tmb.oneapp.paymentexpservice.model.TPromptPayVerifyETEResponse;
import com.tmb.oneapp.paymentexpservice.model.Terminal;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyResponse;
import com.tmb.oneapp.paymentexpservice.model.TransferActivities;
import com.tmb.oneapp.paymentexpservice.model.commonauth.CommonAuthenWithPayloadRequest;
import com.tmb.oneapp.paymentexpservice.model.commonauth.VerifyTransactionResult;
import com.tmb.oneapp.paymentexpservice.model.financiallog.FinancialEWallet;
import com.tmb.oneapp.paymentexpservice.model.notification.NotificationEWallet;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import com.tmb.oneapp.paymentexpservice.service.v1.V1AccountTopUpService;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

import static com.tmb.oneapp.paymentexpservice.constant.CommonAuthenticationConstant.COMMON_AUTH_TOP_UP_FEATURE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.CommonAuthenticationConstant.COMMON_AUTH_TOP_UP_FLOW_NAME;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.APP_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DAILY_LIMIT_TYPE_TRANSFER;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.IP_ADDRESS;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PAYMENT_QR_PROMPT_PAY;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.QR_TRANSFER_MODULE_PIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TOP_UP_MODULE_PIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class EWalletPaymentServiceTest {
    @Mock
    LogService logService;

    @Mock
    PromptPayPaymentETEService promptPayPaymentETEService;

    @Mock
    AccountService accountService;

    @Mock
    CommonPaymentService commonPaymentService;

    @Mock
    BillPaymentValidateTransaction billPaymentValidateTransaction;

    @Mock
    ValidatorFaceRecognizeTopUpAndBill validatorFaceRecognizeTopUpAndBill;

    @Mock
    TransactionLimitService transactionLimitService;
    @Mock
    OauthService oauthService;

    @Spy
    @InjectMocks
    EWalletPaymentService eWalletPaymentService;

    @Mock
    CommonFRService commonFRService;

    @Mock
    V1AccountTopUpService v1AccountTopUpService;

    String crmId;
    String correlationId;
    String appVersion;
    BillerTopUpDetailResponse billerDetail;
    String transactionDateTime;
    String ipAddress = "";
    HttpHeaders headers;
    TopUpConfirmRequest confirmRequest;

    @BeforeEach
    void setUp() {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        appVersion = "4.3.0";
        ipAddress = "127.0.0.1";
        billerDetail = Factory.createBillerDetail("EW01");
        billerDetail.getBillerInfo().setBillerCompCode("EW01");
        billerDetail.getBillerInfo().setNameEn("PromptPay e-Wallet");
        billerDetail.getBillerInfo().setBillerGroupType("1");
        transactionDateTime = "*************";
        headers = new HttpHeaders();
        headers.add(X_CRMID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);
        headers.add(IP_ADDRESS, ipAddress);
        headers.add(APP_VERSION, appVersion);
        confirmRequest = new TopUpConfirmRequest();
    }

    private void mockMethodFetchTransactionLimit() throws SQLException {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setEbMaxLimitAmtCurrent(1000.00);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(1);
        customerCrmProfile.setPinFreeSeetingFlag("Y");

        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(customerCrmProfile);
    }

    private void mockMethodGetAccountBelongToCrmIdReturnAccount() throws TMBCommonException {
        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountNumber("**********");
        depositAccount.setAccountType("SDA");
        depositAccount.setProductNickname("From Nick Name");
        depositAccount.setWaiveFeeForPromptPay("1");
        depositAccount.setProductNameEn("ProductNameEn");

        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(Mockito.anyString(), Mockito.anyString(), any()))
                .thenReturn(depositAccount);
    }

    private void mockMethodFetchBillerTopUpDetail() throws TMBCommonException {
        Mockito.when(commonPaymentService.fetchBillerTopUpDetail(Mockito.anyString(), Mockito.anyString())).thenReturn(billerDetail);
    }


    @Test
    void validateSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, ExecutionException, InterruptedException, TMBCustomCommonExceptionWithResponse {
        String expectedReceiverName = "สมบูรณ์ รักดี";
        String expectedTransId = "TOPUP_" + crmId + "_" + UUID.randomUUID();
        BigDecimal expectedFeeWhenWaiveFee = new BigDecimal("0.00");

        mockMethodFetchBillerTopUpDetail();

        mockMethodGetAccountBelongToCrmIdReturnAccount();

        Mockito.when(commonPaymentService.getTaxIdFromCustomerKyc(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("*************");

        mockMethodFetchTransactionLimit();

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("FromAccountName");
        sender.setAccountType("DDA");

        Terminal terminal = new Terminal();

        Receiver receiver = new Receiver();
        receiver.setAccountName(expectedReceiverName);
        receiver.setAccountDisplayName(expectedReceiverName);
        receiver.setAccountId("**********");
        receiver.setBankCode("14");

        TPromptPayVerifyETEResponse promptPayETETransaction = new TPromptPayVerifyETEResponse();
        promptPayETETransaction.setFee(new BigDecimal("2.00"));
        promptPayETETransaction.setSender(sender);
        promptPayETETransaction.setTerminal(terminal);
        promptPayETETransaction.setReceiver(receiver);

        Mockito.when(promptPayPaymentETEService.validateEWallet(any())).thenReturn(promptPayETETransaction);

        Mockito.when(commonPaymentService.fetchBankShortNameBasedOnBankCode(Mockito.anyString(), Mockito.eq(promptPayETETransaction.getReceiver().getBankCode())))
                .thenReturn("SCB");

        Mockito.when(commonPaymentService.generateTransactionRef(Mockito.anyString(), Mockito.anyString())).thenReturn(expectedTransId);

        TopUpVerifyRequest promptPayRequest = new TopUpVerifyRequest();
        promptPayRequest.setAmount("100.00");
        promptPayRequest.setAccountNumber("**********");
        promptPayRequest.setBillerCompCode("EW01");
        promptPayRequest.setReference1("***************");
        promptPayRequest.setReference2("");
        promptPayRequest.setNote("");
        promptPayRequest.setToFavoriteName("ReqToFavoriteName");
        promptPayRequest.setQr(PaymentServiceConstant.PAYMENT_QR_PROMPT_PAY);

        Mockito.when(promptPayPaymentETEService.getPromptPayFee(any(), Mockito.anyString(), Mockito.anyString())).thenReturn("0.00");

        TopUpVerifyResponse actual = eWalletPaymentService.validate(crmId, correlationId, promptPayRequest, headers);

        assertEquals(expectedReceiverName, actual.getTopUpAccountName());
        assertEquals(true, actual.getIsRequireConfirmPin());
        assertEquals(expectedTransId, actual.getTransId());
        assertEquals(expectedFeeWhenWaiveFee, actual.getFee());
        Assertions.assertNull(actual.getTopUpRef());
        Assertions.assertTrue(actual.isRequireFr());
        assertEquals(promptPayRequest.getAmount(), actual.getAmount());
        Assertions.assertFalse(actual.getIsRequireCommonAuthen());
        Assertions.assertNull(actual.getCommonAuthenticationInformation());

        ArgumentCaptor<TPromptPayVerifyETEResponse> dataSaveToCacheCaptor = ArgumentCaptor.forClass(TPromptPayVerifyETEResponse.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.anyString(), dataSaveToCacheCaptor.capture());

        TPromptPayVerifyETEResponse actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        assertEquals("ReqToFavoriteName", actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        assertEquals("From Nick Name", actualDataSaveToCache.getPaymentCacheData().getFromAccountNickname());
        assertEquals(PaymentServiceConstant.PAYMENT_QR_PROMPT_PAY, actualDataSaveToCache.getPaymentCacheData().getQr());
        assertEquals(promptPayETETransaction.getReceiver().getBankCode(), actualDataSaveToCache.getPaymentCacheData().getBankCode());
        assertEquals(promptPayETETransaction.getSender().getAccountName(), actualDataSaveToCache.getSender().getAccountName());
        assertEquals(promptPayETETransaction.getTerminal().getId(), actualDataSaveToCache.getTerminal().getId());
        assertEquals(expectedFeeWhenWaiveFee, actualDataSaveToCache.getFee());
        assertEquals(BigDecimal.ZERO, actualDataSaveToCache.getPaymentCacheData().getPaymentAccuUsgAmt());

    }

    @Test
    void validateWhenUseCommonAuthForAuthenticationShouldReturnRequireCommonAuthSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, ExecutionException, InterruptedException, TMBCustomCommonExceptionWithResponse {
        mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen();

        mockMethodFetchBillerTopUpDetail();

        mockMethodGetAccountBelongToCrmIdReturnAccount();

        Mockito.when(commonPaymentService.getTaxIdFromCustomerKyc(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("*************");

        mockMethodFetchTransactionLimit();


        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("FromAccountName");
        sender.setAccountType("DDA");

        Terminal terminal = new Terminal();

        Receiver receiver = new Receiver();
        receiver.setAccountName("สมบูรณ์ รักดี");
        receiver.setAccountDisplayName("สมบูรณ์ รักดี");
        receiver.setAccountId("**********");
        receiver.setBankCode("14");

        TPromptPayVerifyETEResponse promptPayETETransaction = new TPromptPayVerifyETEResponse();
        promptPayETETransaction.setFee(new BigDecimal("2.00"));
        promptPayETETransaction.setSender(sender);
        promptPayETETransaction.setTerminal(terminal);
        promptPayETETransaction.setReceiver(receiver);

        Mockito.when(promptPayPaymentETEService.validateEWallet(any())).thenReturn(promptPayETETransaction);

        Mockito.when(commonPaymentService.fetchBankShortNameBasedOnBankCode(Mockito.anyString(), Mockito.eq(promptPayETETransaction.getReceiver().getBankCode())))
                .thenReturn("SCB");

        Mockito.when(commonPaymentService.generateTransactionRef(Mockito.anyString(), Mockito.anyString())).thenReturn("trans-is");

        TopUpVerifyRequest promptPayRequest = new TopUpVerifyRequest();
        promptPayRequest.setAmount("100.00");
        promptPayRequest.setAccountNumber("**********");
        promptPayRequest.setBillerCompCode("EW01");
        promptPayRequest.setReference1("***************");
        promptPayRequest.setReference2("");
        promptPayRequest.setNote("");
        promptPayRequest.setToFavoriteName("ReqToFavoriteName");
        promptPayRequest.setQr(PaymentServiceConstant.PAYMENT_QR_PROMPT_PAY);

        Mockito.when(promptPayPaymentETEService.getPromptPayFee(any(), Mockito.anyString(), Mockito.anyString())).thenReturn("0.00");

        TopUpVerifyResponse actual = eWalletPaymentService.validate(crmId, correlationId, promptPayRequest, headers);

        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
        Assertions.assertFalse(actual.isRequireFr());
    }

    @Test
    void validateWhenFlowFromQRShouldReturnIsRequirePinTrueTest() throws TMBCommonException, SQLException, TMBCustomCommonExceptionWithResponse, ExecutionException, JsonProcessingException, InterruptedException {
        mockMethodFetchBillerTopUpDetail();

        mockMethodGetAccountBelongToCrmIdReturnAccount();

        Mockito.when(commonPaymentService.getTaxIdFromCustomerKyc(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("*************");

        mockMethodFetchTransactionLimit();

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("FromAccountName");
        sender.setAccountType("DDA");

        Terminal terminal = new Terminal();

        Receiver receiver = new Receiver();
        receiver.setAccountName("AccountName");
        receiver.setAccountDisplayName("AccountDisplayName");
        receiver.setAccountId("**********");
        receiver.setBankCode("14");

        TPromptPayVerifyETEResponse promptPayETETransaction = new TPromptPayVerifyETEResponse();
        promptPayETETransaction.setFee(new BigDecimal("2.00"));
        promptPayETETransaction.setSender(sender);
        promptPayETETransaction.setTerminal(terminal);
        promptPayETETransaction.setReceiver(receiver);

        Mockito.when(promptPayPaymentETEService.validateEWallet(any())).thenReturn(promptPayETETransaction);

        Mockito.when(commonPaymentService.fetchBankShortNameBasedOnBankCode(Mockito.anyString(), Mockito.eq(promptPayETETransaction.getReceiver().getBankCode())))
                .thenReturn("SCB");

        Mockito.when(commonPaymentService.generateTransactionRef(Mockito.anyString(), Mockito.anyString())).thenReturn("TransId");

        TopUpVerifyRequest promptPayRequest = new TopUpVerifyRequest();
        promptPayRequest.setAmount("100.00");
        promptPayRequest.setAccountNumber("**********");
        promptPayRequest.setBillerCompCode("EW01");
        promptPayRequest.setReference1("***************");
        promptPayRequest.setReference2("");
        promptPayRequest.setNote("");
        promptPayRequest.setToFavoriteName("ReqToFavoriteName");
        promptPayRequest.setQr(PaymentServiceConstant.PAYMENT_QR_PROMPT_PAY);

        Mockito.when(promptPayPaymentETEService.getPromptPayFee(any(), Mockito.anyString(), Mockito.anyString())).thenReturn("0.00");

        HttpHeaders headers = new HttpHeaders();
        headers.add(PaymentServiceConstant.PRE_LOGIN, "true");
        headers.add(X_CRMID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);
        headers.add(IP_ADDRESS, ipAddress);
        headers.add(APP_VERSION, appVersion);

        TopUpVerifyResponse actual = eWalletPaymentService.validate(crmId, correlationId, promptPayRequest, headers);

        Assertions.assertTrue(actual.getIsRequireConfirmPin());
        Assertions.assertTrue(actual.isRequireFr());
        assertEquals(promptPayRequest.getAmount(), actual.getAmount());
    }


    @Test
    void validateWhenCallETEFailedShouldThrowsTMBCommonExceptionTest() throws TMBCommonException, ExecutionException, InterruptedException {
        mockMethodFetchBillerTopUpDetail();

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountNumber("**********");
        depositAccount.setAccountType("SDA");

        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(Mockito.anyString(), Mockito.anyString(), any()))
                .thenReturn(depositAccount);

        Mockito.when(commonPaymentService.getTaxIdFromCustomerKyc(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("*************");

        Mockito.when(promptPayPaymentETEService.validateEWallet(any())).thenThrow(TMBCommonException.class);

        TopUpVerifyRequest promptPayRequest = new TopUpVerifyRequest();
        promptPayRequest.setAccountNumber("**********");
        promptPayRequest.setBillerCompCode("EW01");
        promptPayRequest.setAmount("100.50");
        promptPayRequest.setReference1("***************");

        Assertions.assertThrows(TMBCommonException.class, () ->
                eWalletPaymentService.validate(crmId, correlationId, promptPayRequest, headers));
    }

    @Test
    void validate_WhenCannotGetAccountBelongToCrmId_ShouldThrowAccountNoEligibleTest() throws TMBCommonException {
        String accountNumberNotMatch = "accountNumberNotMatch";

        mockMethodFetchBillerTopUpDetail();
        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(crmId, correlationId, accountNumberNotMatch)).thenThrow(TMBCommonException.class);

        TopUpVerifyRequest promptPayRequest = new TopUpVerifyRequest()
                .setAmount("100.00")
                .setAccountNumber(accountNumberNotMatch)
                .setBillerCompCode("EW01")
                .setReference1("***************")
                .setToFavoriteName("ReqToFavoriteName")
                .setQr(PaymentServiceConstant.PAYMENT_QR_PROMPT_PAY);


        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> eWalletPaymentService.validate(crmId, correlationId, promptPayRequest, headers));

        assertEquals(ResponseCode.NO_ELIGIBLE_ERROR.getCode(), exception.getErrorCode());
        Mockito.verify(logService, Mockito.times(1)).saveLogActivityBillPayEvent(any());
    }

    @Test
    void validate_WhenCannotGetAccountBelongToCrmId_ShouldThrowExceptionTest() throws TMBCommonException {
        String accountNumberNotMatch = "accountNumberNotMatch";

        mockMethodFetchBillerTopUpDetail();
        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(crmId, correlationId, accountNumberNotMatch)).thenThrow(FeignException.class);

        TopUpVerifyRequest promptPayRequest = new TopUpVerifyRequest()
                .setAmount("100.00")
                .setAccountNumber(accountNumberNotMatch)
                .setBillerCompCode("EW01")
                .setReference1("***************")
                .setToFavoriteName("ReqToFavoriteName")
                .setQr(PaymentServiceConstant.PAYMENT_QR_PROMPT_PAY);


        Assertions.assertThrows(FeignException.class, () -> eWalletPaymentService.validate(crmId, correlationId, promptPayRequest, headers));

        Mockito.verify(logService, Mockito.times(0)).saveLogActivityBillPayEvent(any());
    }

    @Test
    void confirmShouldSuccessTest() throws SQLException, IOException, TMBCommonException, WriterException {
        String transId = "TOPUP_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        String frUuid = UUID.randomUUID().toString();
        confirmRequest.setTransId(transId);
        confirmRequest.setFrUuid(frUuid);

        TPromptPayVerifyETEResponse dataCache = new TPromptPayVerifyETEResponse();
        dataCache.setAmount(new BigDecimal("10.50"));
        dataCache.setTransactionReference("********");
        dataCache.setPaymentCacheData(new PaymentCacheData());
        dataCache.getPaymentCacheData().setBankShortName("TTB");
        dataCache.getPaymentCacheData().setBillerCompCode("EW01");
        dataCache.getPaymentCacheData().setFromAccountNickname("FromNickName");
        dataCache.getPaymentCacheData().setToFavoriteNickname("ReqToFavoriteName");
        dataCache.getPaymentCacheData().setBillerResp(billerDetail);
        dataCache.getPaymentCacheData().setFlow("TOPUP");
        dataCache.getPaymentCacheData().setRequireFr(true);
        dataCache.getPaymentCacheData().setRequirePin(false);
        dataCache.getPaymentCacheData().setPaymentAccuUsgAmt(new BigDecimal("10000.00"));
        dataCache.setFee(new BigDecimal("10.00"));
        dataCache.setSender(new Sender());
        dataCache.getSender().setAccountId("**********");
        dataCache.setReceiver(new Receiver());
        dataCache.getReceiver().setAccountId("**********");
        dataCache.setTerminal(new Terminal());
        dataCache.getTerminal().setId("TerminalID");
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TPromptPayVerifyETEResponse.class, crmId))
                .thenReturn(dataCache);

        transactionLimitService.validateDailyLimitExceeded(any(CustomerCrmProfile.class), anyDouble(), eq(false), eq(DAILY_LIMIT_TYPE_TRANSFER));


        TPromptPayVerifyETEResponse ppConfirmResponse = new TPromptPayVerifyETEResponse();
        ppConfirmResponse.setTransactionCreatedDatetime("");
        ppConfirmResponse.setBalance(new Balance());
        ppConfirmResponse.getBalance().setAvailable(new BigDecimal("10000.00"));
        ppConfirmResponse.setReceiver(new Receiver());
        ppConfirmResponse.getReceiver().setAccountId("**********");
        ppConfirmResponse.getReceiver().setProxyValue("");
        ppConfirmResponse.setSender(new Sender());
        ppConfirmResponse.getSender().setAccountId("**********");
        ppConfirmResponse.setAmount(new BigDecimal("1000.00"));
        ppConfirmResponse.setTransactionReference("REF1111111");
        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        tPromptPayETEResponse.setData(ppConfirmResponse);
        Mockito.when(promptPayPaymentETEService.confirmEWallet(any())).thenReturn(tPromptPayETEResponse);

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPaymentAccuUsgAmt(new BigDecimal("10000.00"));

        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(customerCrmProfile);

        Mockito.when(commonPaymentService.getCurrentDateTime()).thenReturn(transactionDateTime);
        mockCommonFRIsExisted();

        TopUpConfirmResponse actual = eWalletPaymentService.confirm(confirmRequest, crmId, correlationId, headers);

        Assertions.assertNotNull(actual);

        Mockito.verify(commonPaymentService, Mockito.times(1))
                .deleteCacheAccountAfterBillPay(headers, correlationId, crmId);

        ArgumentCaptor<FinancialEWallet> finLogCaptor = ArgumentCaptor.forClass(FinancialEWallet.class);
        ArgumentCaptor<TransferActivities> transferActivityArgument = ArgumentCaptor.forClass(TransferActivities.class);

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), finLogCaptor.capture(), transferActivityArgument.capture());

        Mockito.verify(commonPaymentService, Mockito.times(1))
                .deleteCacheAccountAfterBillPay(headers, correlationId, crmId);

        Mockito.verify(validatorFaceRecognizeTopUpAndBill, Mockito.times(1))
                .updatePaymentAccumulateUsageAmount(Mockito.anyBoolean(), eq(dataCache.getPaymentCacheData().getPaymentAccuUsgAmt()), eq(dataCache.getAmount()), eq(correlationId), eq(crmId));

        FinancialEWallet actualFinLog = finLogCaptor.getValue();
        assertEquals("ReqToFavoriteName", actualFinLog.getToAccNickName());
        assertEquals(ppConfirmResponse.getReceiver().getAccountName(), actualFinLog.getToAccName());
        assertEquals(correlationId, actualFinLog.getActivityRefId());
        assertEquals(dataCache.getPaymentCacheData().getFromAccountNickname(), actualFinLog.getFromAccNickName());
        assertEquals(dataCache.getSender().getAccountName(), actualFinLog.getFromAccName());
        assertEquals(PaymentServiceConstant.TXN_TYPE_TOP_UP, actualFinLog.getTxnType());

        NotificationEWallet notificationEWalletExpected = new NotificationEWallet();
        notificationEWalletExpected.setAddDateTimeEN("18 Aug 22 - 1:37 PM");
        notificationEWalletExpected.setAddDateTimeTH("18 ส.ค. 65 - 13:37 น.");

        ArgumentCaptor<NotificationEWallet> notificationEWalletArgumentCaptor = ArgumentCaptor.forClass(NotificationEWallet.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .sendENotificationPayment(notificationEWalletArgumentCaptor.capture());

        assertEquals(notificationEWalletExpected.getAddDateTimeTH(), notificationEWalletArgumentCaptor.getValue().getAddDateTimeTH());
        assertEquals(notificationEWalletExpected.getAddDateTimeEN(), notificationEWalletArgumentCaptor.getValue().getAddDateTimeEN());
    }

    @Test
    void confirmWhenIsQrPromptPayShouldSuccessTest() throws SQLException, IOException, TMBCommonException, WriterException {
        String transId = "TOPUP_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        String frUuid = UUID.randomUUID().toString();
        confirmRequest.setTransId(transId);
        confirmRequest.setFrUuid(frUuid);

        TPromptPayVerifyETEResponse dataCache = new TPromptPayVerifyETEResponse();
        dataCache.setAmount(new BigDecimal("10.50"));
        dataCache.setTransactionReference("********");
        dataCache.setPaymentCacheData(new PaymentCacheData());
        dataCache.getPaymentCacheData().setBankShortName("TTB");
        dataCache.getPaymentCacheData().setBillerCompCode("EW01");
        dataCache.getPaymentCacheData().setFromAccountNickname("FromNickName");
        dataCache.getPaymentCacheData().setToFavoriteNickname("ReqToFavoriteName");
        dataCache.getPaymentCacheData().setBillerResp(billerDetail);
        dataCache.getPaymentCacheData().setRequireFr(true);
        dataCache.getPaymentCacheData().setRequirePin(true);
        dataCache.getPaymentCacheData().setFlow("TOPUP");
        dataCache.setFee(new BigDecimal("10.00"));
        dataCache.setSender(new Sender());
        dataCache.getSender().setAccountId("**********");
        dataCache.setReceiver(new Receiver());
        dataCache.getReceiver().setAccountId("**********");
        dataCache.getReceiver().setBankCode(PaymentServiceConstant.TTB_BANK_CODE);
        dataCache.setTerminal(new Terminal());
        dataCache.getTerminal().setId("TerminalID");
        dataCache.getPaymentCacheData().setQr(PaymentServiceConstant.PAYMENT_QR_PROMPT_PAY);
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TPromptPayVerifyETEResponse.class, crmId))
                .thenReturn(dataCache);


        Mockito.doNothing().when(oauthService).validateAuthentication(any(), any(), any(), anyString());
        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(new CustomerCrmProfile());
        Mockito.doNothing().when(transactionLimitService).validateDailyLimitExceeded(any(CustomerCrmProfile.class), anyDouble(), eq(false), eq(DAILY_LIMIT_TYPE_TRANSFER));

        TPromptPayVerifyETEResponse ppConfirmResponse = new TPromptPayVerifyETEResponse();
        ppConfirmResponse.setTransactionCreatedDatetime("");
        ppConfirmResponse.setBalance(new Balance());
        ppConfirmResponse.getBalance().setAvailable(new BigDecimal("10000.00"));
        ppConfirmResponse.setReceiver(new Receiver());
        ppConfirmResponse.getReceiver().setAccountId("**********");
        ppConfirmResponse.getReceiver().setProxyValue("");
        ppConfirmResponse.setSender(new Sender());
        ppConfirmResponse.getSender().setAccountId("**********");
        ppConfirmResponse.setAmount(new BigDecimal("1000.00"));
        ppConfirmResponse.setTransactionReference("REF1111111");
        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        tPromptPayETEResponse.setData(ppConfirmResponse);
        Mockito.when(promptPayPaymentETEService.confirmEWallet(any())).thenReturn(tPromptPayETEResponse);

        Mockito.when(commonPaymentService.getCurrentDateTime()).thenReturn(transactionDateTime);
        mockCommonFRIsExisted();

        TopUpConfirmResponse actual = eWalletPaymentService.confirm(confirmRequest, crmId, correlationId, headers);

        Assertions.assertNotNull(actual);

        Mockito.verify(commonPaymentService, Mockito.times(1))
                .deleteCacheAccountAfterBillPay(headers, correlationId, crmId);

        Mockito.verify(validatorFaceRecognizeTopUpAndBill, Mockito.times(1))
                .updatePaymentAccumulateUsageAmount(true, dataCache.getPaymentCacheData().getPaymentAccuUsgAmt(), dataCache.getAmount(), correlationId, crmId);

        ArgumentCaptor<FinancialEWallet> finLogCaptor = ArgumentCaptor.forClass(FinancialEWallet.class);
        ArgumentCaptor<TransferActivities> transferActivityArgument = ArgumentCaptor.forClass(TransferActivities.class);

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), finLogCaptor.capture(), transferActivityArgument.capture());

        FinancialEWallet actualFinLog = finLogCaptor.getValue();
        assertEquals("ReqToFavoriteName", actualFinLog.getToAccNickName());
        assertEquals(dataCache.getReceiver().getAccountName(), actualFinLog.getToAccName());
        assertEquals(correlationId, actualFinLog.getActivityRefId());
        assertEquals(dataCache.getPaymentCacheData().getFromAccountNickname(), actualFinLog.getFromAccNickName());
        assertEquals(dataCache.getSender().getAccountName(), actualFinLog.getFromAccName());
        assertEquals(PaymentServiceConstant.THAI_QR_FIN_FLEX_VALUES1, actualFinLog.getFinFlexValues1());
    }

    @Test
    void confirmWhenVerifyPinFailedShouldThrowsTMBCommonExceptionTest() throws TMBCommonException, JsonProcessingException {
        String transId = "TOPUP_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        String frUuid = UUID.randomUUID().toString();
        confirmRequest.setTransId(transId);
        confirmRequest.setFrUuid(frUuid);

        TPromptPayVerifyETEResponse dataCache = new TPromptPayVerifyETEResponse();
        dataCache.setTerminal(new Terminal());
        dataCache.getTerminal().setId("123457");
        dataCache.setAmount(new BigDecimal("10.50"));
        dataCache.setSender(new Sender());
        dataCache.setReceiver(new Receiver());
        dataCache.getReceiver().setAccountId("**********");
        dataCache.setFee(new BigDecimal("10"));
        dataCache.setPaymentCacheData(new PaymentCacheData());
        dataCache.getPaymentCacheData().setBankShortName("TTB");
        dataCache.getPaymentCacheData().setBillerCompCode("EW01");
        dataCache.getPaymentCacheData().setFromAccountNickname("FromNickName");
        dataCache.getPaymentCacheData().setToFavoriteNickname("ReqToFavoriteName");
        dataCache.getPaymentCacheData().setBillerResp(billerDetail);
        dataCache.getPaymentCacheData().setFlow("TOPUP");
        dataCache.getPaymentCacheData().setRequireFr(true);
        dataCache.getPaymentCacheData().setRequirePin(true);
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TPromptPayVerifyETEResponse.class, crmId))
                .thenReturn(dataCache);

        mockCommonFRIsExisted();
        TMBCommonException tmbCommonException = new TMBCommonException("");
        tmbCommonException.setErrorCode(ResponseCode.PIN_REQUIRED.getCode());
        Mockito.doThrow(tmbCommonException).when(oauthService).validateAuthentication(any(), any(), any(), anyString());

        Assertions.assertThrows(TMBCommonException.class, () ->
                eWalletPaymentService.confirm(confirmRequest, crmId, correlationId, headers));
    }

    @Test
    void confirmWhenCallETEErrorShouldThrowsTMBCommonExceptionTest() throws TMBCommonException, JsonProcessingException {
        String transId = "TOPUP_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        String frUuid = UUID.randomUUID().toString();
        confirmRequest.setTransId(transId);
        confirmRequest.setFrUuid(frUuid);

        TPromptPayVerifyETEResponse dataCache = new TPromptPayVerifyETEResponse();
        dataCache.setAmount(new BigDecimal("10.50"));
        dataCache.setSender(new Sender());
        dataCache.setReceiver(new Receiver());
        dataCache.setFee(new BigDecimal("10.00"));
        dataCache.setPaymentCacheData(new PaymentCacheData());
        dataCache.getPaymentCacheData().setBillerCompCode("EW01");
        dataCache.getPaymentCacheData().setBillerResp(billerDetail);
        dataCache.getPaymentCacheData().setFlow("TOPUP");
        dataCache.getPaymentCacheData().setRequireFr(true);
        dataCache.getPaymentCacheData().setRequirePin(true);
        dataCache.setSender(new Sender());
        dataCache.getReceiver().setAccountId("**********");
        dataCache.setReceiver(new Receiver());
        dataCache.getReceiver().setAccountId("**********");
        dataCache.setTerminal(new Terminal());
        dataCache.getTerminal().setId("TerminalID");
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TPromptPayVerifyETEResponse.class, crmId))
                .thenReturn(dataCache);

        Mockito.doNothing().when(oauthService).validateAuthentication(any(), any(), any(), anyString());
        transactionLimitService.validateDailyLimitExceeded(any(CustomerCrmProfile.class), anyDouble(), eq(true), eq(DAILY_LIMIT_TYPE_TRANSFER));

        String errorCodeFromCallETE = "400";
        TMBCommonException tmbCommonException = new TMBCommonException(null);
        tmbCommonException.setErrorCode(errorCodeFromCallETE);
        Mockito.doThrow(tmbCommonException).when(eWalletPaymentService).callConfirmETEFeignClient(any());
        mockCommonFRIsExisted();

        Assertions.assertThrows(TMBCommonException.class, () ->
                eWalletPaymentService.confirm(confirmRequest, crmId, correlationId, headers));

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(any());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), any(), any());

    }

    @Test
    void confirmWhenCallETEErrorShouldThrowsExceptionTest() throws JsonProcessingException, TMBCommonException {
        String transId = "TOPUP_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        String frUuid = UUID.randomUUID().toString();
        confirmRequest.setTransId(transId);
        confirmRequest.setFrUuid(frUuid);

        TPromptPayVerifyETEResponse dataCache = new TPromptPayVerifyETEResponse();
        dataCache.setAmount(new BigDecimal("10.50"));
        dataCache.setSender(new Sender());
        dataCache.setReceiver(new Receiver());
        dataCache.setFee(new BigDecimal("10.00"));
        dataCache.setPaymentCacheData(new PaymentCacheData());
        dataCache.getPaymentCacheData().setBillerCompCode("EW01");
        dataCache.getPaymentCacheData().setBillerResp(billerDetail);
        dataCache.getPaymentCacheData().setFlow("TOPUP");
        dataCache.getPaymentCacheData().setRequireFr(true);
        dataCache.setSender(new Sender());
        dataCache.getReceiver().setAccountId("**********");
        dataCache.setReceiver(new Receiver());
        dataCache.getReceiver().setAccountId("**********");
        dataCache.setTerminal(new Terminal());
        dataCache.getTerminal().setId("TerminalID");
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TPromptPayVerifyETEResponse.class, crmId))
                .thenReturn(dataCache);

        Mockito.doNothing().when(oauthService).verifyPinCacheV2(anyString(), anyString(), anyString(), eq(TOP_UP_MODULE_PIN));
        Mockito.doNothing().when(transactionLimitService).validateDailyLimitExceeded(any(CustomerCrmProfile.class), anyDouble(), anyBoolean(), eq(DAILY_LIMIT_TYPE_TRANSFER));

        Mockito.doThrow(FeignException.class).when(eWalletPaymentService).callConfirmETEFeignClient(any());
        mockCommonFRIsExisted();
        Assertions.assertThrows(Exception.class, () ->
                eWalletPaymentService.confirm(confirmRequest, crmId, correlationId, headers));

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(any());
    }

    @ParameterizedTest
    @CsvSource({
            ", , *********",
            "PROMPTPAY, 11, *********",
            "PROMPTPAY, 12, *********",
    })
    void getActivityVerifyIdTest(String qr, String bankCode, String expected) {
        assertEquals(
                expected,
                eWalletPaymentService.getActivityVerifyId(qr, bankCode)
        );
    }

    @Test
    void verifyEWalletNumberSuccessTest() throws TMBCommonException, ExecutionException, InterruptedException, TMBCustomCommonExceptionWithResponse {
        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountNumber("*********");
        Mockito.when(accountService.getAccountTopUp(crmId, correlationId)).thenReturn(List.of(depositAccount));

        Mockito.when(commonPaymentService.generateTerminalId()).thenReturn("000001");
        Receiver receiver = new Receiver();
        receiver.setAccountName("account name");
        receiver.setAccountDisplayName("display name");
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setReceiver(receiver);
        Mockito.when(promptPayPaymentETEService.validateEWallet(any())).thenReturn(tPromptPayVerifyETEResponse);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setBillerCompCode("EW01");
        topUpVerifyRequest.setReference1("**********12345");
        PaymentDetailResponse actual = eWalletPaymentService.verifyEWalletNumber(crmId, correlationId, topUpVerifyRequest);

        assertEquals("account name", actual.getAccountName());
        assertEquals("display name", actual.getDisplayName());
    }

    @Test
    void confirmWhenCommonFRIsNotExistedShouldThrowsExceptionTest() throws JsonProcessingException, TMBCommonException {
        String transId = "TOPUP_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        String frUuid = UUID.randomUUID().toString();
        confirmRequest.setTransId(transId);
        confirmRequest.setFrUuid(frUuid);

        TPromptPayVerifyETEResponse dataCache = new TPromptPayVerifyETEResponse();
        dataCache.setAmount(new BigDecimal("10.50"));
        dataCache.setSender(new Sender());
        dataCache.setReceiver(new Receiver());
        dataCache.setFee(new BigDecimal("10.00"));
        dataCache.setPaymentCacheData(new PaymentCacheData());
        dataCache.getPaymentCacheData().setBillerCompCode("EW01");
        dataCache.getPaymentCacheData().setBillerResp(billerDetail);
        dataCache.getPaymentCacheData().setFlow("TOPUP");
        dataCache.getPaymentCacheData().setRequireFr(true);
        dataCache.setSender(new Sender());
        dataCache.getReceiver().setAccountId("**********");
        dataCache.setReceiver(new Receiver());
        dataCache.getReceiver().setAccountId("**********");
        dataCache.setTerminal(new Terminal());
        dataCache.getTerminal().setId("TerminalID");
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TPromptPayVerifyETEResponse.class, crmId))
                .thenReturn(dataCache);
        mockCommonFRIsNotExisted();

        Assertions.assertThrows(Exception.class, () ->
                eWalletPaymentService.confirm(confirmRequest, crmId, correlationId, headers));

        Mockito.verify(logService, Mockito.never())
                .saveLogActivityBillPayEvent(any());
    }

    @Test
    void validateAuthenticationWhenUseCommonAuthenticationTest() throws TMBCommonException {
        String transId = "transId";

        var paymentCacheData = new PaymentCacheData()
                .setRequireCommonAuthentication(false)
                .setRequirePin(true)
                .setQr(PAYMENT_QR_PROMPT_PAY);

        Assertions.assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(eWalletPaymentService, "validateAuthentication", transId, headers, paymentCacheData, "10.50"));

        ArgumentCaptor<LegacyAuthenticationRequest> captor = ArgumentCaptor.forClass(LegacyAuthenticationRequest.class);
        Mockito.verify(oauthService, Mockito.times(1)).validateAuthentication(any(), eq(null), captor.capture(), anyString());
        assertEquals(QR_TRANSFER_MODULE_PIN, captor.getValue().getModule());
    }

    @Test
    void validateAuthenticationWhenUseLegacyAuthenticationTest() throws TMBCommonException {
        String transId = "transId";
        String amount = "10.50";

        var paymentCacheData = new PaymentCacheData()
                .setRequireCommonAuthentication(true)
                .setRequirePin(false)
                .setCommonAuthenticationInformation(new CommonAuthenticationInformation()
                        .setFeatureId(COMMON_AUTH_TOP_UP_FEATURE_ID)
                        .setFlowName(COMMON_AUTH_TOP_UP_FLOW_NAME)
                        .setBillerCompCode("comp-code-form-cache")
                        .setTotalPaymentAccumulateUsage(new BigDecimal("1000.00"))
                );

        Assertions.assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(eWalletPaymentService, "validateAuthentication", transId, headers, paymentCacheData, amount));

        ArgumentCaptor<CommonAuthenWithPayloadRequest> captor = ArgumentCaptor.forClass(CommonAuthenWithPayloadRequest.class);
        Mockito.verify(oauthService, Mockito.times(1)).validateAuthentication(any(), captor.capture(), eq(null), anyString());
        assertEquals(COMMON_AUTH_TOP_UP_FEATURE_ID, captor.getValue().getFeatureId());
        assertEquals(COMMON_AUTH_TOP_UP_FLOW_NAME, captor.getValue().getFlowName());
        assertEquals("comp-code-form-cache", captor.getValue().getBillerCompCode());
        assertEquals("1000.00", captor.getValue().getDailyAmount());
        assertEquals(transId, captor.getValue().getRefId());
        assertEquals(amount, captor.getValue().getAmount());
    }

    private void mockCommonFRIsExisted() throws TMBCommonException {
        Mockito.when(commonFRService.isCommonFRExistedByUUID(anyString(), anyString(), any(), anyString())).thenReturn(true);
    }

    private void mockCommonFRIsNotExisted() throws TMBCommonException {
        Mockito.when(commonFRService.isCommonFRExistedByUUID(anyString(), anyString(), any(), anyString())).thenReturn(false);
    }

    @ParameterizedTest()
    @CsvSource({" , , Home-TopUp-Landing"
            , " , to_fav, Home-Favorite"
            , "TEST_FLOW, , TEST_FLOW"
            , "TEST_FLOW, to_fav, TEST_FLOW"})
    void initialFlowNameTests(String flowName, String toFavorite, String expect) {
        TopUpVerifyRequest request = new TopUpVerifyRequest()
                .setFlow(flowName)
                .setToFavoriteName(toFavorite);

        eWalletPaymentService.initialFlowName(request);

        String actualFlow = request.getFlow();
        assertEquals(expect, actualFlow);
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen() throws TMBCommonException {
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransactionForTopUp(any(), any(), eq(false), any(CustomerCrmProfile.class))).thenReturn(new VerifyTransactionResult(true, new FaceRecognizeResponse().setIsRequireFr(true).setPaymentAccuUsgAmt(BigDecimal.ZERO), new CommonAuthenResult()));
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen() throws TMBCommonException {
        CommonAuthenResult commonAuthenResult = new CommonAuthenResult()
                .setRequireCommonAuthen(true)
                .setIsForceFR(true)
                .setPinFree(true);
        VerifyTransactionResult resultWhenExecuteCommonAuthen = new VerifyTransactionResult(false, null, commonAuthenResult);
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransactionForTopUp(any(), any(), eq(false), any(CustomerCrmProfile.class))).thenReturn(resultWhenExecuteCommonAuthen);
    }

    @Test
    void testUpdatePaymentAccumulateUsageWhenCommonAuthenticationIsRequired() {
        TPromptPayVerifyETEResponse dataCache = new TPromptPayVerifyETEResponse();
        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setRequireFr(false);
        paymentCacheData.setRequireCommonAuthentication(true);
        paymentCacheData.setPaymentAccuUsgAmt(null);

        dataCache.setPaymentCacheData(paymentCacheData);
        dataCache.setAmount(new BigDecimal("100"));

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPaymentAccuUsgAmt(new BigDecimal("1000"));

        ReflectionTestUtils.invokeMethod(eWalletPaymentService, "updatePaymentAccumulateUsage", crmId, correlationId, dataCache, customerCrmProfile);

        verify(validatorFaceRecognizeTopUpAndBill, times(1)).updatePaymentAccumulateUsageAmount(eq(true), eq(new BigDecimal("1000")), eq(new BigDecimal("100")), eq(correlationId), eq(crmId));
    }

    @Test
    void testUpdatePaymentAccumulateUsageWhenCommonAuthenticationNotIsRequiredAndFRIsNotRequired() {
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setRequireFr(false);
        paymentCacheData.setRequireCommonAuthentication(false);
        paymentCacheData.setPaymentAccuUsgAmt(new BigDecimal("1000"));
        tPromptPayVerifyETEResponse.setPaymentCacheData(paymentCacheData);
        tPromptPayVerifyETEResponse.setAmount(new BigDecimal("100"));

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPaymentAccuUsgAmt(new BigDecimal("1000"));

        ReflectionTestUtils.invokeMethod(eWalletPaymentService, "updatePaymentAccumulateUsage", crmId, correlationId, tPromptPayVerifyETEResponse, customerCrmProfile);

        verify(validatorFaceRecognizeTopUpAndBill, times(1)).updatePaymentAccumulateUsageAmount(eq(false), eq(new BigDecimal("1000")), eq(new BigDecimal("100")), eq(correlationId), eq(crmId));
    }
}