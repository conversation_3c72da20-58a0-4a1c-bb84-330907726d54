package com.tmb.oneapp.paymentexpservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.paymentexpservice.client.AccountInfoFeignClient;
import com.tmb.oneapp.paymentexpservice.factory.Factory;
import com.tmb.oneapp.paymentexpservice.model.ActivityTopUpEvent;
import com.tmb.oneapp.paymentexpservice.model.BillerInfoResponse;
import com.tmb.oneapp.paymentexpservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.DepositAccount;
import com.tmb.oneapp.paymentexpservice.model.LoanAccountETE;
import com.tmb.oneapp.paymentexpservice.model.LoanBalance;
import com.tmb.oneapp.paymentexpservice.model.LoanBalanceInfo;
import com.tmb.oneapp.paymentexpservice.model.LoanBalanceList;
import com.tmb.oneapp.paymentexpservice.model.OCPBillPayment;
import com.tmb.oneapp.paymentexpservice.model.OCPFee;
import com.tmb.oneapp.paymentexpservice.model.PaymentDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyResponse;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityBillPayVerifyEvent;
import com.tmb.oneapp.paymentexpservice.model.commonauth.VerifyTransactionResult;
import com.tmb.oneapp.paymentexpservice.model.loan.Balances;
import com.tmb.oneapp.paymentexpservice.model.loan.DirectDebit;
import com.tmb.oneapp.paymentexpservice.model.loan.HomeLoanFullInfoResponse;
import com.tmb.oneapp.paymentexpservice.model.loan.LoanAccount;
import com.tmb.oneapp.paymentexpservice.model.loan.Payment;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.sql.SQLException;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACTIVITY_LOG_BILL_PAY_VERIFY_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.APP_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILLER_GROUP_TOP_UP;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_PAYMENT_TYPE_FULL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_PAYMENT_TYPE_FULL_SPECIFIED;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.CHANNEL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_MODEL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.LOAN_ACCOUNT_PREFIX;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ONLINE_TRANS_REF_SEQUENCE;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.OS_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TOP_UP_PAYMENT_SEQUENCE_DIGIT;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TOP_UP_REF_SEQUENCE_DIGIT;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TRANSFER_REFERENCE_NUMBER_PREFIX;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_TTB_LOAN_DETAIL_ENTER_PAGE;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_TTB_LOAN_REVIEW_PAGE;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.SCHEDULE_CONFIG_TAB_FULL_AND_SPECIFIED;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.SCHEDULE_CONFIG_TAB_ONLY_AMOUNT;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;

@ExtendWith(MockitoExtension.class)
class BillPaymentOCPLoanVerifyServiceTest {

    @Mock
    AccountService accountService;

    @Mock
    AccountInfoFeignClient accountInfoFeignClient;

    @Mock
    LogService logService;

    @Mock
    BillPaymentValidateTransaction billPaymentValidateTransaction;

    @Mock
    CommonPaymentService commonPaymentService;

    @Mock
    TopUpETEService topUpETEService;

    @Spy
    @InjectMocks
    BillPaymentOCPLoanVerifyService billPaymentOCPLoanVerifyService;

    String crmId;
    String correlationId;
    String transId;
    String paymentRequestRef1;
    String paymentRequestRef2;
    String loanAccount;
    HttpHeaders headers;
    ActivityTopUpEvent activityEvent;
    ActivityBillPayVerifyEvent activityBillPayVerifyEvent;
    TopUpVerifyRequest topUpVerifyRequest;
    BillerTopUpDetailResponse billerDetail;

    @BeforeEach
    void setUp() {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        transId = "TOPUP_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        paymentRequestRef1 = "**********";
        paymentRequestRef2 = "001";
        loanAccount = LOAN_ACCOUNT_PREFIX + paymentRequestRef1 + paymentRequestRef2;
        activityEvent = new ActivityTopUpEvent("", "", "");
        headers = new HttpHeaders();
        headers.add(X_CRMID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);

        headers = new HttpHeaders();
        headers.add(X_CRMID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);
        headers.add(OS_VERSION, "");
        headers.add(CHANNEL, "");
        headers.add(APP_VERSION, "");
        headers.add(DEVICE_ID, "");
        headers.add(DEVICE_MODEL, "");

        topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("110.00");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("12345");
        topUpVerifyRequest.setBillerCompCode("5003");
        topUpVerifyRequest.setAccountNumber("**********");

        billerDetail = Factory.createBillerDetail();

        activityBillPayVerifyEvent = new ActivityBillPayVerifyEvent(
                correlationId,
                ACTIVITY_LOG_BILL_PAY_VERIFY_ID,
                headers,
                topUpVerifyRequest,
                billerDetail
        );
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnNotRequireConfirmPin() throws TMBCommonException {
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any())).thenReturn(new VerifyTransactionResult(false, null, new CommonAuthenResult()));
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnRequireConfirmPin() throws TMBCommonException {
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any())).thenReturn(new VerifyTransactionResult(true, null, new CommonAuthenResult()));
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen() throws TMBCommonException {
        CommonAuthenResult commonAuthenResult = new CommonAuthenResult()
                .setRequireCommonAuthen(true)
                .setIsForceFR(true)
                .setPinFree(true);
        VerifyTransactionResult resultWhenExecuteCommonAuthen = new VerifyTransactionResult(false, null, commonAuthenResult);
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(resultWhenExecuteCommonAuthen);
    }

    @Test
    void getTTBLoanPaymentDetailWhenRef1Ref2InvalidShouldThrowINCORRECT_REF1_REF2() {
        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setReference1(paymentRequestRef1);
        topUpVerifyRequest.setReference2(paymentRequestRef2);

        TmbServiceResponse<HomeLoanFullInfoResponse> response = new TmbServiceResponse<>();
        Mockito.when(accountInfoFeignClient.getAccountLoanDetail(Mockito.anyString(), Mockito.any()))
                .thenReturn(ResponseEntity.ok(response));

        Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentOCPLoanVerifyService.getTTBLoanPaymentDetail(crmId, correlationId, topUpVerifyRequest)
        );
    }

    @Test
    void getTTBLoanPaymentDetailWhenNoTOwnerShouldReturnPaymentTypeBILL_PAYMENT_TYPE_FULLTest() throws TMBCommonException {
        LoanAccount loanAccount = new LoanAccount();
        loanAccount.setTitle("test test");
        loanAccount.setPayment(new Payment());
        loanAccount.getPayment().setNextPaymentAmount("1000.00");
        loanAccount.setDirectDebit(new DirectDebit("*********0", "", ""));
        HomeLoanFullInfoResponse homeLoanFullInfoResponse = new HomeLoanFullInfoResponse();
        homeLoanFullInfoResponse.setAccount(loanAccount);
        TmbServiceResponse<HomeLoanFullInfoResponse> response = new TmbServiceResponse<>();
        response.setData(homeLoanFullInfoResponse);
        Mockito.when(accountInfoFeignClient.getAccountLoanDetail(Mockito.anyString(), Mockito.any()))
                .thenReturn(ResponseEntity.ok(response));

        Mockito.when(accountService.isPayBillBySelfLoan(correlationId, crmId, "0**********001"))
                .thenReturn(false);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setReference1(paymentRequestRef1);
        topUpVerifyRequest.setReference2(paymentRequestRef2);
        PaymentDetailResponse actual = billPaymentOCPLoanVerifyService.getTTBLoanPaymentDetail(crmId, correlationId, topUpVerifyRequest);

        Assertions.assertEquals(BILL_PAYMENT_TYPE_FULL, actual.getPaymentType());
        Assertions.assertFalse(actual.isOwner());
        Assertions.assertNull(actual.getFull());
        Assertions.assertNull(actual.getMin());
        Assertions.assertNull(actual.getSpecified());
        Assertions.assertEquals("0.00", actual.getAmount().getAmount());
        Assertions.assertTrue(actual.getAmount().isEditable());
        Assertions.assertTrue(actual.isDirectDebit());
        Assertions.assertNull(actual.getScheduleConfig().getPhrase());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isOnce());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isWeekly());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isMonthly());
        Assertions.assertEquals(SCHEDULE_CONFIG_TAB_ONLY_AMOUNT, actual.getScheduleConfig().getTabsOfSchedule());
    }

    @Test
    void getTTBLoanPaymentDetailWhenIsOwnerShouldReturnPaymentTypeBILL_PAYMENT_TYPE_FULL_SPECIFIEDTest() throws TMBCommonException {
        Mockito.when(accountService.isPayBillBySelfLoan(correlationId, crmId, loanAccount))
                .thenReturn(true);

        LoanAccount loanAccount = new LoanAccount();
        loanAccount.setTitle("test test");
        loanAccount.setPayment(new Payment());
        loanAccount.getPayment().setNextPaymentAmount("1000.00");
        loanAccount.getPayment().setTotalPastDueAmount("1000.00");
        loanAccount.setDirectDebit(new DirectDebit("", "", ""));
        HomeLoanFullInfoResponse homeLoanFullInfoResponse = new HomeLoanFullInfoResponse(new LoanAccount());
        homeLoanFullInfoResponse.setAccount(loanAccount);
        TmbServiceResponse<HomeLoanFullInfoResponse> response = new TmbServiceResponse<>();
        response.setData(homeLoanFullInfoResponse);
        Mockito.when(accountInfoFeignClient.getAccountLoanDetail(Mockito.anyString(), Mockito.any()))
                .thenReturn(ResponseEntity.ok(response));

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setReference1(paymentRequestRef1);
        topUpVerifyRequest.setReference2(paymentRequestRef2);
        PaymentDetailResponse actual = billPaymentOCPLoanVerifyService.getTTBLoanPaymentDetail(crmId, correlationId, topUpVerifyRequest);

        Assertions.assertEquals(BILL_PAYMENT_TYPE_FULL_SPECIFIED, actual.getPaymentType());
        Assertions.assertNull(actual.getPaymentName());
        Assertions.assertTrue(actual.isOwner());
        Assertions.assertNull(actual.getAmount());
        Assertions.assertNull(actual.getMin());
        Assertions.assertFalse(actual.isDirectDebit());
        Assertions.assertEquals("2000.00", actual.getFull().getAmount());
        Assertions.assertFalse(actual.getFull().isEditable());
        Assertions.assertEquals("0.00", actual.getSpecified().getAmount());
        Assertions.assertTrue(actual.getSpecified().isEditable());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isOnce());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isWeekly());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isMonthly());
        Assertions.assertEquals(PHRASE_BILLER_TTB_LOAN_DETAIL_ENTER_PAGE, actual.getScheduleConfig().getPhrase().getDetailEnterPage());
        Assertions.assertEquals(PHRASE_BILLER_TTB_LOAN_REVIEW_PAGE, actual.getScheduleConfig().getPhrase().getReviewPage());
        Assertions.assertEquals(SCHEDULE_CONFIG_TAB_FULL_AND_SPECIFIED, actual.getScheduleConfig().getTabsOfSchedule());
    }

    @Test
    void ttbLoanPaymentVerifyWhenNotOwnerShouldCheckDailyLimitTest() throws TMBCommonException, SQLException, JsonProcessingException {
        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnRequireConfirmPin();

        Mockito.doNothing().when(billPaymentOCPLoanVerifyService).checkAccruedDebt(loanAccount, "100.00", correlationId);

        Mockito.when(accountService.isPayBillBySelfLoan(correlationId, crmId, loanAccount))
                .thenReturn(false);

        Mockito.when(billPaymentValidateTransaction.fetchTransactionLimit(correlationId, crmId)).thenReturn(new CustomerCrmProfile());

        Mockito.when(commonPaymentService.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT))
                .thenReturn("REF0000000001");

        OCPBillPayment response = new OCPBillPayment();
        response.setFee(new OCPFee());
        response.getFee().setPaymentFee("5.00");
        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any()))
                .thenReturn(response);

        TopUpVerifyRequest verifyRequest = new TopUpVerifyRequest();
        verifyRequest.setReference1(paymentRequestRef1);
        verifyRequest.setReference2(paymentRequestRef2);
        verifyRequest.setAccountNumber("********");
        verifyRequest.setBillerCompCode("AL01");
        verifyRequest.setAmount("100.00");
        verifyRequest.setToFavoriteName("ReqToFavoriteName");

        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setNameEn("True Move");
        billerInfo.setBillerMethod("2");
        billerInfo.setBillerGroupType(BILLER_GROUP_TOP_UP);
        billerInfo.setBillerCompCode("1234");
        billerInfo.setPaymentMethod("1");
        billerInfo.setBillerMethod("2");
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfo);

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setAccountNumber("**********");
        depositAccount.setAccountName("FromAccountName");
        depositAccount.setProductNickname("FromNickname");
        depositAccount.setWaiveFeeForBillpay("0");
        TopUpVerifyResponse actual = billPaymentOCPLoanVerifyService.ttbLoanPaymentVerify(
                verifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertNull(actual.getTopUpAccountName());
        Assertions.assertNull(actual.getTopUpRef());
        Assertions.assertTrue(actual.getIsRequireConfirmPin());
        Assertions.assertEquals(transId, actual.getTransId());
        Assertions.assertEquals(new BigDecimal("5.00"), actual.getFee());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());

        ArgumentCaptor<OCPBillPayment> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OCPBillPayment.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(eq(transId), dataSaveToCacheCaptor.capture());

        OCPBillPayment actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals("ReqToFavoriteName", actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        Assertions.assertEquals(depositAccount.getProductNickname(), actualDataSaveToCache.getPaymentCacheData().getFromAccountNickname());
        Assertions.assertEquals(depositAccount.getAccountName(), actualDataSaveToCache.getPaymentCacheData().getFromAccountName());
    }

    @Test
    void ttbLoanPaymentVerifyWhenIsOwnerShouldNotCheckDailyLimitAndConFirmPinIsFalseTest() throws TMBCommonException, SQLException, JsonProcessingException {
        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnNotRequireConfirmPin();

        Mockito.doNothing().when(billPaymentOCPLoanVerifyService).checkAccruedDebt(loanAccount, "100.00", correlationId);

        Mockito.when(accountService.isPayBillBySelfLoan(correlationId, crmId, loanAccount))
                .thenReturn(true);

        Mockito.when(commonPaymentService.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT))
                .thenReturn("REF0000000001");

        OCPBillPayment response = new OCPBillPayment();
        response.setFee(new OCPFee());
        response.getFee().setPaymentFee("5.00");
        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any()))
                .thenReturn(response);

        TopUpVerifyRequest verifyRequest = new TopUpVerifyRequest();
        verifyRequest.setReference1(paymentRequestRef1);
        verifyRequest.setReference2(paymentRequestRef2);
        verifyRequest.setAccountNumber("**********");
        verifyRequest.setBillerCompCode("AL01");
        verifyRequest.setAmount("100.00");

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setAccountNumber("**********");
        depositAccount.setWaiveFeeForBillpay("1");
        TopUpVerifyResponse actual = billPaymentOCPLoanVerifyService.ttbLoanPaymentVerify(
                verifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertNull(actual.getTopUpAccountName());
        Assertions.assertNull(actual.getTopUpRef());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
        Assertions.assertEquals(transId, actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());
    }

    @Test
    void ttbLoanPaymentVerifyWhenExecuteWithCommonAuthenticationShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException {
        mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen();

        Mockito.doNothing().when(billPaymentOCPLoanVerifyService).checkAccruedDebt(loanAccount, "100.00", correlationId);

        Mockito.when(accountService.isPayBillBySelfLoan(correlationId, crmId, loanAccount))
                .thenReturn(false);

        Mockito.when(billPaymentValidateTransaction.fetchTransactionLimit(correlationId, crmId)).thenReturn(new CustomerCrmProfile());

        Mockito.when(commonPaymentService.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT))
                .thenReturn("REF0000000001");

        OCPBillPayment response = new OCPBillPayment();
        response.setFee(new OCPFee());
        response.getFee().setPaymentFee("5.00");
        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any()))
                .thenReturn(response);

        TopUpVerifyRequest verifyRequest = new TopUpVerifyRequest();
        verifyRequest.setReference1(paymentRequestRef1);
        verifyRequest.setReference2(paymentRequestRef2);
        verifyRequest.setAccountNumber("********");
        verifyRequest.setBillerCompCode("AL01");
        verifyRequest.setAmount("100.00");
        verifyRequest.setToFavoriteName("ReqToFavoriteName");

        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setNameEn("True Move");
        billerInfo.setBillerMethod("2");
        billerInfo.setBillerGroupType(BILLER_GROUP_TOP_UP);
        billerInfo.setBillerCompCode("1234");
        billerInfo.setPaymentMethod("1");
        billerInfo.setBillerMethod("2");
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfo);

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setAccountNumber("**********");
        depositAccount.setAccountName("FromAccountName");
        depositAccount.setProductNickname("FromNickname");
        depositAccount.setWaiveFeeForBillpay("0");

        TopUpVerifyResponse actual = billPaymentOCPLoanVerifyService.ttbLoanPaymentVerify(
                verifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
    }

    @Test
    void ttbLoanPaymentVerifyWhenThrowShouldSaveActivityLogTest() throws TMBCommonException {
        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnNotRequireConfirmPin();

        Mockito.doNothing().when(billPaymentOCPLoanVerifyService).checkAccruedDebt(loanAccount, "100.00", correlationId);

        Mockito.when(accountService.isPayBillBySelfLoan(correlationId, crmId, loanAccount))
                .thenReturn(true);

        Mockito.when(commonPaymentService.getTransactionId(ONLINE_TRANS_REF_SEQUENCE, TOP_UP_PAYMENT_SEQUENCE_DIGIT))
                .thenReturn("REF0000000001");

        Mockito.when(topUpETEService.verifyOCPPayment(Mockito.any())).thenThrow(FeignException.class);

        TopUpVerifyRequest verifyRequest = new TopUpVerifyRequest();
        verifyRequest.setReference1(paymentRequestRef1);
        verifyRequest.setReference2(paymentRequestRef2);
        verifyRequest.setAccountNumber("**********");
        verifyRequest.setBillerCompCode("AL01");
        verifyRequest.setAmount("100.00");

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setAccountNumber("**********");
        depositAccount.setWaiveFeeForBillpay("1");
        Assertions.assertThrows(Exception.class, () ->
                billPaymentOCPLoanVerifyService.ttbLoanPaymentVerify(verifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount)
        );

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());
    }

    @ParameterizedTest
    @CsvSource({"100", "99.999"})
    void checkDebtWhenAmountLessThanDebtShouldNotThrowTest(String amount) {
        LoanAccountETE loanAccountETE = new LoanAccountETE();
        loanAccountETE.setBalanceInfo(new LoanBalanceInfo());
        loanAccountETE.getBalanceInfo().setBalances(new LoanBalanceList());
        loanAccountETE.getBalanceInfo().getBalances().setPayoff(new LoanBalance());
        loanAccountETE.getBalanceInfo().getBalances().getPayoff().setAmount(new BigDecimal("100.00"));
        LoanAccount loanAccount = new LoanAccount();
        loanAccount.setBalances(new Balances());
        loanAccount.getBalances().setPayoff("100.00");
        HomeLoanFullInfoResponse homeLoanFullInfoResponse = new HomeLoanFullInfoResponse();
        homeLoanFullInfoResponse.setAccount(loanAccount);
        TmbServiceResponse<HomeLoanFullInfoResponse> response = new TmbServiceResponse<>();
        response.setData(homeLoanFullInfoResponse);
        Mockito.when(accountInfoFeignClient.getAccountLoanDetail(Mockito.anyString(), Mockito.any()))
                .thenReturn(ResponseEntity.ok(response));

        Assertions.assertDoesNotThrow(() -> billPaymentOCPLoanVerifyService.checkAccruedDebt("************", amount, correlationId));
    }

    @Test
    void checkDebtWhenAmountMoreThanDebtShouldThrowTMBCommonExceptionTest() {
        LoanAccount loanAccount = new LoanAccount();
        loanAccount.setTitle("test test");
        loanAccount.setBalances(new Balances());
        loanAccount.getBalances().setPayoff("100.00");
        HomeLoanFullInfoResponse homeLoanFullInfoResponse = new HomeLoanFullInfoResponse(new LoanAccount());
        homeLoanFullInfoResponse.setAccount(loanAccount);
        TmbServiceResponse<HomeLoanFullInfoResponse> response = new TmbServiceResponse<>();
        response.setData(homeLoanFullInfoResponse);

        Mockito.when(accountInfoFeignClient.getAccountLoanDetail(Mockito.anyString(), Mockito.any()))
                .thenReturn(ResponseEntity.ok(response));

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentOCPLoanVerifyService.checkAccruedDebt("************", "100.01", correlationId)
        );

        Assertions.assertEquals("billpay_0020", exception.getErrorCode());
    }
}