package com.tmb.oneapp.paymentexpservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.zxing.WriterException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.customer.favorite.CustomerBillerResponse;
import com.tmb.common.model.customer.favorite.CustomerFavoriteListResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.paymentexpservice.factory.Factory;
import com.tmb.oneapp.paymentexpservice.model.ActivityTopUpEvent;
import com.tmb.oneapp.paymentexpservice.model.Balance;
import com.tmb.oneapp.paymentexpservice.model.BillerInfoResponse;
import com.tmb.oneapp.paymentexpservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenticationInformation;
import com.tmb.oneapp.paymentexpservice.model.CustomerKYCResponse;
import com.tmb.oneapp.paymentexpservice.model.DepositAccount;
import com.tmb.oneapp.paymentexpservice.model.FinRequest;
import com.tmb.oneapp.paymentexpservice.model.LegacyAuthenticationRequest;
import com.tmb.oneapp.paymentexpservice.model.PaymentCacheData;
import com.tmb.oneapp.paymentexpservice.model.Receiver;
import com.tmb.oneapp.paymentexpservice.model.Sender;
import com.tmb.oneapp.paymentexpservice.model.TPromptPayETERequest;
import com.tmb.oneapp.paymentexpservice.model.TPromptPayVerifyETEResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyResponse;
import com.tmb.oneapp.paymentexpservice.model.TransferActivities;
import com.tmb.oneapp.paymentexpservice.model.ValidChannel;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityBillPayPromptPayConfirmEvent;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityBillPayVerifyEvent;
import com.tmb.oneapp.paymentexpservice.model.commonauth.CommonAuthenWithPayloadRequest;
import com.tmb.oneapp.paymentexpservice.model.commonauth.VerifyTransactionResult;
import com.tmb.oneapp.paymentexpservice.model.notification.NotificationBillPaymentPromptPay;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static com.tmb.oneapp.paymentexpservice.constant.CommonAuthenticationConstant.COMMON_AUTH_TOP_UP_FEATURE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.CommonAuthenticationConstant.COMMON_AUTH_TOP_UP_FLOW_NAME;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACTIVITY_LOG_BILL_PAY_VERIFY_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.APP_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILLER_GROUP_TOP_UP;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_MODULE_PIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.CATEGORY_E_DONATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.CHANNEL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_MODEL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.E_DONATION_MODULE_PIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.LOAN_ACCOUNT_PREFIX;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.OS_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PAYMENT_QR_E_DONATION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PAYMENT_QR_PROMPT_PAY;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.QR_BILL_MODULE_PIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.SENDER_TYPE_QR;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.SUCCESS;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.THAI_QR_FIN_FLEX_VALUES1;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TOP_UP_REF_SEQUENCE_DIGIT;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TRANSFER_REFERENCE_NUMBER_PREFIX;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TXN_TYPE_BILL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BillPaymentPromptPayServiceTest {
    @Mock
    AccountService accountService;

    @Mock
    BillPaymentValidateTransaction billPaymentValidateTransaction;

    @Mock
    CommonPaymentService commonPaymentService;

    @Mock
    PromptPayPaymentETEService promptPayPaymentETEService;

    @Mock
    LogService logService;

    @Spy
    @InjectMocks
    BillPaymentPromptPayService billPaymentPromptPayService;

    @Mock
    TransactionLimitService transactionLimitService;

    @Mock
    OauthService oauthService;

    String crmId;
    String correlationId;
    String transId;
    String paymentRequestRef1;
    String paymentRequestRef2;
    String loanAccount;
    HttpHeaders headers;
    ActivityTopUpEvent activityEvent;
    ActivityBillPayVerifyEvent activityBillPayVerifyEvent;
    TopUpVerifyRequest topUpVerifyRequest;
    TopUpConfirmRequest confirmRequest;
    BillerTopUpDetailResponse billerDetail;
    String transactionDateTime;

    @BeforeEach
    void setUp() {

        ReflectionTestUtils.setField(billPaymentPromptPayService, "regExCitizenMasking", "(.{9})(.)(.{2})(.)$");

        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        transId = "TOPUP_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        paymentRequestRef1 = "**********";
        paymentRequestRef2 = "001";
        loanAccount = LOAN_ACCOUNT_PREFIX + paymentRequestRef1 + paymentRequestRef2;
        activityEvent = new ActivityTopUpEvent("", "", "");
        headers = new HttpHeaders();
        headers.add(X_CRMID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);

        headers = new HttpHeaders();
        headers.add(X_CRMID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);
        headers.add(OS_VERSION, "");
        headers.add(CHANNEL, "");
        headers.add(APP_VERSION, "");
        headers.add(DEVICE_ID, "");
        headers.add(DEVICE_MODEL, "");

        topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("110.00");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("12345");
        topUpVerifyRequest.setBillerCompCode("5003");
        topUpVerifyRequest.setAccountNumber("**********");

        billerDetail = Factory.createBillerDetail();

        activityBillPayVerifyEvent = new ActivityBillPayVerifyEvent(
                correlationId,
                ACTIVITY_LOG_BILL_PAY_VERIFY_ID,
                headers,
                topUpVerifyRequest,
                billerDetail
        );

        transactionDateTime = "*************";

        confirmRequest = new TopUpConfirmRequest();
        confirmRequest.setTransId(transId);
    }

    private void mockGetCurrentDateTime() {
        Mockito.when(commonPaymentService.getCurrentDateTime()).thenReturn(transactionDateTime);
    }

    private void mockETEConfirmBillPay() throws TMBCommonException {
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setBalance(new Balance());
        tPromptPayVerifyETEResponse.getBalance().setAvailable(new BigDecimal("20000.12"));

        when(promptPayPaymentETEService.confirmBillPay(any())).thenReturn(tPromptPayVerifyETEResponse);
    }

    private void mockGetFavoriteList() {
        CustomerBillerResponse customerBillerResponse = new CustomerBillerResponse();
        customerBillerResponse.setBillerCompcode("1234");
        customerBillerResponse.setRef1("*********");
        customerBillerResponse.setRef2(null);
        customerBillerResponse.setFavoriteNickname("Prompt Pay");
        List<CustomerBillerResponse> customerBillerResponseList = new ArrayList<>();
        customerBillerResponseList.add(customerBillerResponse);

        TmbOneServiceResponse<CustomerFavoriteListResponse> responseCustomerFavorite = new TmbOneServiceResponse<>();
        CustomerFavoriteListResponse customerFavoriteListResponse = new CustomerFavoriteListResponse();
        customerFavoriteListResponse.setBiller(customerBillerResponseList);
        responseCustomerFavorite.setData(customerFavoriteListResponse);
    }

    private void mockGetTransactionId() {
        when(commonPaymentService.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT))
                .thenReturn("REF********11");
    }

    private void mockGetCacheWhenEDonation(String someRef2WhenSetAllowShareToRD, boolean isRequirePin) throws JsonProcessingException, TMBCommonException {
        TPromptPayVerifyETEResponse cacheData = (TPromptPayVerifyETEResponse) TMBUtils.convertStringToJavaObj("{\"sender\":{\"accountId\":\"**********\",\"accountType\":\"SDA\",\"accountName\":\"น.ส. น้องนุ จะเทสวันแอป\",\"taxId\":\"*************\",\"customerId\":\"\"},\"receiver\":{\"accountId\":\"**********\",\"accountType\":\"DDA\",\"accountName\":\"Bhumirajanagarindra Kidney Institute Foundation\",\"accountDisplayName\":\"Bhumirajanagarindra Kidney Institut\",\"id\":\"***************\",\"itmxFlag\":\"Y\",\"creditFlag\":\"\",\"category\":\"26\",\"requiredReference2Flag\":\"Y\",\"scanOnly\":\"1\"},\"terminal\":{\"id\":\"I000055B011BTMBO\",\"type\":\"80\",\"pccTraceId\":\"\"},\"amount\":100.00,\"transactionReference\":\"202304171500000222\",\"transactionCreatedDatetime\":\"2023-04-17T15:14:26.619366+07:00\",\"fee\":0.00,\"reference1\":\"********\",\"reference2\":\"0\",\"reference3\":\"DWD0000002989\",\"reference4\":\"\",\"interRegionFee\":0.0,\"paymentType\":\"0\",\"paymentCacheData\":{\"note\":\"\",\"flow\":\"Home-ScanQR-Pre\",\"qr\":\"E-DONATION\",\"billerCompCode\":\"***************\",\"originRef1\":\"********\",\"originRef2\":\"0\",\"billerResp\":{\"biller_info\":{\"biller_id\":105766,\"name_th\":\"มูลนิธิสถาบันโรคไตภูมิราชนครินทร์\",\"name_en\":\"Bhumirajanagarindra Kidney Institute Foundation\",\"biller_comp_code\":\"***************\",\"image_url\":\"/biller/***************.png\",\"fee\":0.00,\"biller_method\":\"0\",\"biller_group_type\":\"0\",\"to_account_id\":\"**********\",\"is_full_payment\":false,\"payment_method\":\"5\",\"biller_category_code\":\"26\",\"barcode_only\":\"1\",\"to_bank_id\":\"011\",\"require_amount\":\"N\",\"allow_add_favorite\":\"N\",\"allow_set_schedule\":true,\"frequency_once_only\":false,\"credit_card_flag\":false,\"expired_date\":\"9999-12-31T00:00:00.000000+07:00\",\"effective_date\":\"2018-07-20T00:00:00.000000+07:00\",\"epayment\":{\"fee\":\"0.00\"},\"biller_short_name\":\"Bhumirajanagarindra Kidney Institute\"},\"ref1\":{\"label_th\":\"เลขที่โครงการ\",\"label_en\":\"Project ID\",\"max_length\":20,\"is_mobile\":false,\"reg_ex\":\"^[a-zA-Z0-9 ]+$\",\"key_board_layout\":\"ALPHNUM\"},\"ref2\":{\"label_th\":\"เลขที่บัตรประชาชน\",\"label_en\":\"Citizen ID\",\"max_length\":20,\"is_require_add_favorite\":true,\"is_require_pay\":true,\"reg_ex\":\"^[a-zA-Z_0-9. ]+$\",\"key_board_layout\":\"ALPHNUM\"},\"amount\":null},\"preLogin\":true,\"transactionUsed\":false,\"allowShareToRd\":false,\"requiredRef2\":false,\"payByOwner\":false,\"transferToOwnAccount\":false}}", TPromptPayVerifyETEResponse.class);
        cacheData.getPaymentCacheData().getBillerResp().getBillerInfo().setBillerCategoryCode(CATEGORY_E_DONATION_ID);
        cacheData.getPaymentCacheData().getBillerResp().getRef1().setIsMobile(true);
        cacheData.getPaymentCacheData().setOriginRef2(someRef2WhenSetAllowShareToRD);
        cacheData.getPaymentCacheData().setRequirePin(isRequirePin);
        when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TPromptPayVerifyETEResponse.class, crmId))
                .thenReturn(cacheData);
    }

    @Test
    void paymentVerifyShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, ExecutionException, InterruptedException {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setEbMaxLimitAmtCurrent(1000.00);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(1);
        customerCrmProfile.setPinFreeSeetingFlag("Y");
        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(customerCrmProfile);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnRequireConfirmPin();

        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setFee(new BigDecimal("5.0"));
        tPromptPayVerifyETEResponse.setSender(new Sender());
        tPromptPayVerifyETEResponse.setReceiver(new Receiver());
        tPromptPayVerifyETEResponse.getReceiver().setAccountDisplayName("display name");
        when(promptPayPaymentETEService.validateBillPay(any())).thenReturn(tPromptPayVerifyETEResponse);

        TopUpVerifyRequest verifyRequest = new TopUpVerifyRequest();
        verifyRequest.setReference1(paymentRequestRef1);
        verifyRequest.setReference2(paymentRequestRef2);
        verifyRequest.setAccountNumber("********");
        verifyRequest.setBillerCompCode("AL01");
        verifyRequest.setAmount("100.00");
        verifyRequest.setScanFlag(true);
        verifyRequest.setToFavoriteName("ReqToFavoriteName");

        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setNameEn("True Move");
        billerInfo.setBillerMethod("2");
        billerInfo.setBillerGroupType(BILLER_GROUP_TOP_UP);
        billerInfo.setBillerCompCode("1234");
        billerInfo.setPaymentMethod("1");
        billerInfo.setBillerMethod("2");
        billerInfo.setBillerCategoryCode("1");
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfo);

        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("************");
        customerKYCResponse.setIdType("CI");
        doReturn(customerKYCResponse).when(billPaymentPromptPayService).getCustomerKYCResponse(verifyRequest, billerDetail, crmId, correlationId);

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setAccountNumber("**********");
        depositAccount.setWaiveFeeForBillpay("0");
        TopUpVerifyResponse actual = billPaymentPromptPayService.paymentVerify(
                verifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertEquals(new BigDecimal("5.00"), actual.getFee());
        Assertions.assertEquals("display name", actual.getBillerName());
        Assertions.assertNull(actual.getCitizenId());
        Assertions.assertTrue(actual.getIsRequireConfirmPin());

        ArgumentCaptor<TPromptPayVerifyETEResponse> dataSaveToCacheCaptor = ArgumentCaptor.forClass(TPromptPayVerifyETEResponse.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.eq(transId), dataSaveToCacheCaptor.capture());

        TPromptPayVerifyETEResponse actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals("ReqToFavoriteName", actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        Assertions.assertEquals(depositAccount.getProductNickname(), actualDataSaveToCache.getPaymentCacheData().getFromAccountNickname());
        Assertions.assertFalse(actualDataSaveToCache.getPaymentCacheData().isTransferToOwnAccount());
    }

    @Test
    void paymentVerifyWhenExecuteCommonAuthenticationShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, ExecutionException, InterruptedException {
        mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen();

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setEbMaxLimitAmtCurrent(1000.00);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(1);
        customerCrmProfile.setPinFreeSeetingFlag("Y");
        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(customerCrmProfile);

        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setFee(new BigDecimal("5.0"));
        tPromptPayVerifyETEResponse.setSender(new Sender());
        tPromptPayVerifyETEResponse.setReceiver(new Receiver());
        tPromptPayVerifyETEResponse.getReceiver().setAccountDisplayName("display name");
        when(promptPayPaymentETEService.validateBillPay(any())).thenReturn(tPromptPayVerifyETEResponse);

        TopUpVerifyRequest verifyRequest = new TopUpVerifyRequest();
        verifyRequest.setReference1(paymentRequestRef1);
        verifyRequest.setReference2(paymentRequestRef2);
        verifyRequest.setAccountNumber("********");
        verifyRequest.setBillerCompCode("AL01");
        verifyRequest.setAmount("100.00");
        verifyRequest.setScanFlag(true);
        verifyRequest.setToFavoriteName("ReqToFavoriteName");

        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setNameEn("True Move");
        billerInfo.setBillerMethod("2");
        billerInfo.setBillerGroupType(BILLER_GROUP_TOP_UP);
        billerInfo.setBillerCompCode("1234");
        billerInfo.setPaymentMethod("1");
        billerInfo.setBillerMethod("2");
        billerInfo.setBillerCategoryCode("1");
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfo);

        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("************");
        customerKYCResponse.setIdType("CI");
        doReturn(customerKYCResponse).when(billPaymentPromptPayService).getCustomerKYCResponse(verifyRequest, billerDetail, crmId, correlationId);

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setAccountNumber("**********");
        depositAccount.setWaiveFeeForBillpay("1");

        TopUpVerifyResponse actual = billPaymentPromptPayService.paymentVerify(
                verifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);


        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
    }

    @Test
    void paymentVerifyWhenIsEDonationShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, ExecutionException, InterruptedException {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setEbMaxLimitAmtCurrent(1000.00);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(1);
        customerCrmProfile.setPinFreeSeetingFlag("Y");
        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(customerCrmProfile);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnRequireConfirmPin();

        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("*********0123");
        customerKYCResponse.setIdType("CI");
        when(accountService.getCustomerKyc(crmId, correlationId)).thenReturn(CompletableFuture.completedFuture(customerKYCResponse));

        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setFee(new BigDecimal("5.0"));
        tPromptPayVerifyETEResponse.setReceiver(new Receiver());
        tPromptPayVerifyETEResponse.getReceiver().setAccountDisplayName("display name");
        tPromptPayVerifyETEResponse.setSender(new Sender());
        tPromptPayVerifyETEResponse.getSender().setTaxId("123456" +
                "7890123");
        tPromptPayVerifyETEResponse.getSender().setAccountName("name");
        tPromptPayVerifyETEResponse.setReference2("*********0123");
        when(promptPayPaymentETEService.validateBillPay(any())).thenReturn(tPromptPayVerifyETEResponse);

        TopUpVerifyRequest verifyRequest = new TopUpVerifyRequest();
        verifyRequest.setReference1(paymentRequestRef1);
        verifyRequest.setReference2(paymentRequestRef2);
        verifyRequest.setAccountNumber("********");
        verifyRequest.setBillerCompCode("AL01");
        verifyRequest.setAmount("100.00");
        verifyRequest.setScanFlag(true);
        verifyRequest.setAllowShareToRd(true);
        verifyRequest.setQr(PAYMENT_QR_E_DONATION);

        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setNameEn("True Move");
        billerInfo.setBillerMethod("2");
        billerInfo.setBillerGroupType(BILLER_GROUP_TOP_UP);
        billerInfo.setBillerCompCode("1234");
        billerInfo.setPaymentMethod("1");
        billerInfo.setBillerMethod("2");
        billerInfo.setBillerCategoryCode("26");
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfo);

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setAccountNumber("**********");
        depositAccount.setWaiveFeeForBillpay("0");
        TopUpVerifyResponse actual = billPaymentPromptPayService.paymentVerify(
                verifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertEquals(new BigDecimal("5.00"), actual.getFee());
        Assertions.assertEquals("display name", actual.getBillerName());
        Assertions.assertEquals("X-XXXX-XXXX0-12-3", actual.getCitizenId());
        Assertions.assertTrue(actual.getIsRequireConfirmPin());
    }

    @Test
    void paymentVerifyWhenTransactionFromQRShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, ExecutionException, InterruptedException {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setEbMaxLimitAmtCurrent(1000.00);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(1);
        customerCrmProfile.setPinFreeSeetingFlag("Y");
        when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(customerCrmProfile);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnRequireConfirmPin();

        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setFee(new BigDecimal("5.0"));
        tPromptPayVerifyETEResponse.setSender(new Sender());
        tPromptPayVerifyETEResponse.setReceiver(new Receiver());
        tPromptPayVerifyETEResponse.getReceiver().setAccountDisplayName("display name");
        ArgumentCaptor<TPromptPayETERequest> requestForCallToETECaptor = ArgumentCaptor.forClass(TPromptPayETERequest.class);
        when(promptPayPaymentETEService.validateBillPay(requestForCallToETECaptor.capture())).thenReturn(tPromptPayVerifyETEResponse);

        TopUpVerifyRequest verifyRequest = new TopUpVerifyRequest();
        verifyRequest.setReference1(paymentRequestRef1);
        verifyRequest.setReference2(paymentRequestRef2);
        verifyRequest.setAccountNumber("********");
        verifyRequest.setBillerCompCode("AL01");
        verifyRequest.setAmount("100.00");
        verifyRequest.setScanFlag(true);
        verifyRequest.setQr(PAYMENT_QR_PROMPT_PAY);
        verifyRequest.setToFavoriteName("ReqToFavoriteName");

        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setNameEn("True Move");
        billerInfo.setBillerMethod("2");
        billerInfo.setBillerGroupType(BILLER_GROUP_TOP_UP);
        billerInfo.setBillerCompCode("1234");
        billerInfo.setPaymentMethod("1");
        billerInfo.setBillerMethod("2");
        billerInfo.setBillerCategoryCode("1");
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfo);

        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("************");
        customerKYCResponse.setIdType("CI");
        doReturn(customerKYCResponse).when(billPaymentPromptPayService).getCustomerKYCResponse(verifyRequest, billerDetail, crmId, correlationId);

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountType("SDA");
        depositAccount.setAccountNumber("**********");
        depositAccount.setWaiveFeeForBillpay("0");
        TopUpVerifyResponse actual = billPaymentPromptPayService.paymentVerify(
                verifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertEquals(new BigDecimal("5.00"), actual.getFee());
        Assertions.assertEquals("display name", actual.getBillerName());
        Assertions.assertNull(actual.getCitizenId());
        Assertions.assertTrue(actual.getIsRequireConfirmPin());

        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.eq(transId), Mockito.any());


        TPromptPayETERequest requestForCallToETE = requestForCallToETECaptor.getValue();
        Assertions.assertEquals(SENDER_TYPE_QR, requestForCallToETE.getSender().getCustomerTypeFlag());
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthenReturnRequireConfirmPin() throws TMBCommonException {
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(new VerifyTransactionResult(true, null, new CommonAuthenResult()));
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen() throws TMBCommonException {
        CommonAuthenResult commonAuthenResult = new CommonAuthenResult()
                .setRequireCommonAuthen(true)
                .setIsForceFR(true)
                .setPinFree(true);
        VerifyTransactionResult resultWhenExecuteCommonAuthen = new VerifyTransactionResult(false, null, commonAuthenResult);
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(resultWhenExecuteCommonAuthen);
    }

    @Test
    void paymentConfirmShouldSuccessTest() throws TMBCommonException, SQLException, IOException, WriterException, TMBCustomCommonExceptionWithResponse {
        billerDetail.getBillerInfo().setBillerCategoryCode("11");
        billerDetail.getRef1().setIsMobile(true);

        TPromptPayVerifyETEResponse cacheData = new TPromptPayVerifyETEResponse();
        cacheData.setAmount(new BigDecimal("100.00"));
        cacheData.setPaymentCacheData(new PaymentCacheData());
        cacheData.setReference1("*********");
        cacheData.getPaymentCacheData().setOriginRef1("**********");
        cacheData.getPaymentCacheData().setToFavoriteNickname("Prompt Pay");
        cacheData.setReference2(null);
        cacheData.getPaymentCacheData().setBillerCompCode("1234");
        cacheData.getPaymentCacheData().setBillerResp(billerDetail);
        cacheData.getPaymentCacheData().setRequirePin(true);
        cacheData.setSender(new Sender());
        cacheData.getSender().setAccountType("SDA");
        cacheData.getSender().setAccountId("*********0");
        cacheData.getSender().setCustomerTypeFlag("H");
        cacheData.setReceiver(new Receiver());
        cacheData.getReceiver().setAccountId("**********");
        cacheData.getReceiver().setAccountName("name");
        cacheData.setFee(new BigDecimal("5.00"));
        when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TPromptPayVerifyETEResponse.class, crmId))
                .thenReturn(cacheData);

        doNothing().when(oauthService).validateAuthentication(any(), any(), any(), anyString());

        mockGetTransactionId();

        mockGetFavoriteList();

        mockETEConfirmBillPay();

        mockGetCurrentDateTime();

        TopUpConfirmResponse actual = billPaymentPromptPayService.paymentConfirm(crmId, correlationId, confirmRequest, headers);

        Assertions.assertEquals("20000.12", actual.getRemainingBalance());

        ArgumentCaptor<FinRequest> finRequestArgument = ArgumentCaptor.forClass(FinRequest.class);
        ArgumentCaptor<TransferActivities> transferActivityArgument = ArgumentCaptor.forClass(TransferActivities.class);

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), finRequestArgument.capture(), transferActivityArgument.capture());

        ArgumentCaptor<ActivityBillPayPromptPayConfirmEvent> activityEventArument = ArgumentCaptor.forClass(ActivityBillPayPromptPayConfirmEvent.class);
        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(activityEventArument.capture(), any());

        FinRequest finRequest = finRequestArgument.getValue();
        ActivityBillPayPromptPayConfirmEvent activityEvent = activityEventArument.getValue();
        Assertions.assertEquals("Prompt Pay", finRequest.getToAccNickName());
        Assertions.assertEquals(cacheData.getReceiver().getAccountDisplayName(), finRequest.getToAccName());
        Assertions.assertEquals(correlationId, finRequest.getActivityRefId());
        Assertions.assertEquals(cacheData.getSender().getAccountName(), finRequest.getFromAccName());
        Assertions.assertEquals(cacheData.getPaymentCacheData().getFromAccountNickname(), finRequest.getFromAccNickName());
        Assertions.assertEquals(SUCCESS, finRequest.getTxnStatus());
        Assertions.assertEquals(TXN_TYPE_BILL, finRequest.getTxnType());
        Assertions.assertEquals("XX5633", activityEvent.getReference1());

        NotificationBillPaymentPromptPay notificationPromptPayExpected = new NotificationBillPaymentPromptPay();
        notificationPromptPayExpected.setAddDateTimeEN("18 Aug 22 - 1:37 PM");
        notificationPromptPayExpected.setAddDateTimeTH("18 ส.ค. 65 - 13:37 น.");

        ArgumentCaptor<NotificationBillPaymentPromptPay> notificationPromptPayArgumentCaptor = ArgumentCaptor.forClass(NotificationBillPaymentPromptPay.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .sendENotificationPayment(notificationPromptPayArgumentCaptor.capture());

        Assertions.assertEquals(notificationPromptPayExpected.getAddDateTimeTH(), notificationPromptPayArgumentCaptor.getValue().getAddDateTimeTH());
        Assertions.assertEquals(notificationPromptPayExpected.getAddDateTimeEN(), notificationPromptPayArgumentCaptor.getValue().getAddDateTimeEN());
    }

    @Test
    void paymentConfirmWhenEDonationAndChoseAllowShareToTDShouldDoesNotThrowsTest() throws TMBCommonException, IOException {
        String someRef2WhenSetAllowShareToRD = "0";

        mockGetCacheWhenEDonation(someRef2WhenSetAllowShareToRD, false);

        mockGetTransactionId();

        mockGetFavoriteList();

        mockETEConfirmBillPay();

        mockGetCurrentDateTime();

        Assertions.assertDoesNotThrow(() -> billPaymentPromptPayService.paymentConfirm(crmId, correlationId, confirmRequest, headers));

        Mockito.verify(logService, Mockito.times(1)).saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), any(), any());
        Mockito.verify(logService, Mockito.times(1)).saveLogActivityBillPayEvent(any());
        Mockito.verify(commonPaymentService, Mockito.times(1)).sendENotificationPayment(any());
    }

    @Test
    void paymentConfirmWhenIsQRPromptPayShouldSuccessTest() throws TMBCommonException, SQLException, IOException, WriterException, TMBCustomCommonExceptionWithResponse {
        TPromptPayVerifyETEResponse cacheData = new TPromptPayVerifyETEResponse();
        cacheData.setAmount(new BigDecimal("100.00"));
        cacheData.setPaymentCacheData(new PaymentCacheData());
        cacheData.getPaymentCacheData().setBillerCompCode("1234");
        cacheData.setReference1("*********");
        cacheData.setReference2(null);
        cacheData.getPaymentCacheData().setQr(PAYMENT_QR_PROMPT_PAY);
        cacheData.getPaymentCacheData().setBillerResp(billerDetail);
        cacheData.getPaymentCacheData().setRequirePin(true);
        cacheData.getPaymentCacheData().setToFavoriteNickname("Prompt Pay");
        cacheData.setSender(new Sender());
        cacheData.getSender().setAccountType("SDA");
        cacheData.getSender().setAccountId("*********0");
        cacheData.getSender().setCustomerTypeFlag("H");
        cacheData.setReceiver(new Receiver());
        cacheData.getReceiver().setAccountId("**********");
        cacheData.getReceiver().setAccountName("name");
        cacheData.getReceiver().setBankCode("04");
        cacheData.setFee(new BigDecimal("5.00"));

        when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TPromptPayVerifyETEResponse.class, crmId))
                .thenReturn(cacheData);

        mockGetTransactionId();

        mockGetFavoriteList();

        mockETEConfirmBillPay();

        mockGetCurrentDateTime();

        TopUpConfirmResponse actual = billPaymentPromptPayService.paymentConfirm(crmId, correlationId, confirmRequest, headers);

        Assertions.assertEquals("20000.12", actual.getRemainingBalance());

        Mockito.verify(oauthService, times(1))
                .validateAuthentication(any(), any(), any(), anyString());

        ArgumentCaptor<FinRequest> finRequestArgument = ArgumentCaptor.forClass(FinRequest.class);
        ArgumentCaptor<TransferActivities> transferActivityArgument = ArgumentCaptor.forClass(TransferActivities.class);

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), finRequestArgument.capture(), transferActivityArgument.capture());

        FinRequest finRequest = finRequestArgument.getValue();
        Assertions.assertEquals("Prompt Pay", finRequest.getToAccNickName());
        Assertions.assertEquals(cacheData.getReceiver().getAccountDisplayName(), finRequest.getToAccName());
        Assertions.assertEquals(correlationId, finRequest.getActivityRefId());
        Assertions.assertEquals(cacheData.getSender().getAccountName(), finRequest.getFromAccName());
        Assertions.assertEquals(cacheData.getPaymentCacheData().getFromAccountNickname(), finRequest.getFromAccNickName());
        Assertions.assertEquals(THAI_QR_FIN_FLEX_VALUES1, finRequest.getFinFlexValues1());
        Assertions.assertEquals("04", finRequest.getBankCode());

        NotificationBillPaymentPromptPay notificationPromptPayExpected = new NotificationBillPaymentPromptPay();
        notificationPromptPayExpected.setAddDateTimeEN("18 Aug 22 - 1:37 PM");
        notificationPromptPayExpected.setAddDateTimeTH("18 ส.ค. 65 - 13:37 น.");

        ArgumentCaptor<NotificationBillPaymentPromptPay> notificationPromptPayArgumentCaptor = ArgumentCaptor.forClass(NotificationBillPaymentPromptPay.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .sendENotificationPayment(notificationPromptPayArgumentCaptor.capture());

        Assertions.assertEquals(notificationPromptPayExpected.getAddDateTimeTH(), notificationPromptPayArgumentCaptor.getValue().getAddDateTimeTH());
        Assertions.assertEquals(notificationPromptPayExpected.getAddDateTimeEN(), notificationPromptPayArgumentCaptor.getValue().getAddDateTimeEN());
    }

    @Test
    void paymentConfirmWhenThrowsExceptionShouldSaveLogTest() throws TMBCommonException, IOException {
        billerDetail.getBillerInfo().setBillerCategoryCode("11");
        billerDetail.getRef1().setIsMobile(true);

        TPromptPayVerifyETEResponse cacheData = new TPromptPayVerifyETEResponse();
        cacheData.setAmount(new BigDecimal("100.00"));
        cacheData.setPaymentCacheData(new PaymentCacheData());
        cacheData.setReference1("*********");
        cacheData.getPaymentCacheData().setOriginRef1("**********");
        cacheData.setReference2(null);
        cacheData.getPaymentCacheData().setBillerCompCode("1234");
        cacheData.getPaymentCacheData().setBillerResp(billerDetail);
        cacheData.getPaymentCacheData().setRequirePin(false);
        cacheData.setSender(new Sender());
        cacheData.getSender().setAccountType("SDA");
        cacheData.getSender().setAccountId("*********0");
        cacheData.getSender().setCustomerTypeFlag("H");
        cacheData.setReceiver(new Receiver());
        cacheData.getReceiver().setAccountId("**********");
        cacheData.getReceiver().setAccountName("name");
        cacheData.setFee(new BigDecimal("5.00"));
        when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TPromptPayVerifyETEResponse.class, crmId))
                .thenReturn(cacheData);

        mockGetTransactionId();

        mockGetFavoriteList();

        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setBalance(new Balance());
        tPromptPayVerifyETEResponse.getBalance().setAvailable(new BigDecimal("20000.12"));

        when(promptPayPaymentETEService.confirmBillPay(any())).thenThrow(FeignException.class);


        Assertions.assertThrows(Exception.class, () -> billPaymentPromptPayService.paymentConfirm(crmId, correlationId, confirmRequest, headers));

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());

        ArgumentCaptor<FinRequest> finRequestArgument = ArgumentCaptor.forClass(FinRequest.class);
        ArgumentCaptor<TransferActivities> transferActivityArgument = ArgumentCaptor.forClass(TransferActivities.class);

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), finRequestArgument.capture(), transferActivityArgument.capture());

    }

    @Test
    void paymentConfirmWhenIsCreditCardShouldNotCallUpdateDailyUsageSuccessTest() throws TMBCommonException, SQLException, IOException, WriterException, TMBCustomCommonExceptionWithResponse {
        billerDetail.getBillerInfo().setBillerCategoryCode("11");
        billerDetail.getRef1().setIsMobile(true);

        TPromptPayVerifyETEResponse cacheData = new TPromptPayVerifyETEResponse();
        cacheData.setAmount(new BigDecimal("100.00"));
        cacheData.setPaymentCacheData(new PaymentCacheData());
        cacheData.setReference1("*********");
        cacheData.getPaymentCacheData().setOriginRef1("**********");
        cacheData.getPaymentCacheData().setToFavoriteNickname("Prompt Pay");
        cacheData.setReference2(null);
        cacheData.getPaymentCacheData().setBillerCompCode("1234");
        cacheData.getPaymentCacheData().setBillerResp(billerDetail);
        cacheData.getPaymentCacheData().setIsCreditCard(true);
        cacheData.getPaymentCacheData().setCardNumber("*********0123456");
        cacheData.getPaymentCacheData().setRequirePin(false);
        cacheData.setSender(new Sender());
        cacheData.getSender().setAccountType("SDA");
        cacheData.getSender().setAccountId("*********0");
        cacheData.getSender().setCustomerTypeFlag("H");
        cacheData.setReceiver(new Receiver());
        cacheData.getReceiver().setAccountId("**********");
        cacheData.getReceiver().setAccountName("name");
        cacheData.setFee(new BigDecimal("5.00"));
        when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TPromptPayVerifyETEResponse.class, crmId))
                .thenReturn(cacheData);

        mockGetTransactionId();

        mockGetFavoriteList();

        mockETEConfirmBillPay();

        mockGetCurrentDateTime();

        TopUpConfirmResponse actual = billPaymentPromptPayService.paymentConfirm(crmId, correlationId, confirmRequest, headers);

        Assertions.assertEquals("20000.12", actual.getRemainingBalance());

        ArgumentCaptor<FinRequest> finRequestArgument = ArgumentCaptor.forClass(FinRequest.class);
        ArgumentCaptor<TransferActivities> transferActivityArgument = ArgumentCaptor.forClass(TransferActivities.class);

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), finRequestArgument.capture(), transferActivityArgument.capture());

        ArgumentCaptor<ActivityBillPayPromptPayConfirmEvent> activityEventArument = ArgumentCaptor.forClass(ActivityBillPayPromptPayConfirmEvent.class);
        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(activityEventArument.capture(), any());
        Mockito.verify(billPaymentValidateTransaction, Mockito.never())
                .updateBillDailyUsage(anyString(), any(), any(), anyBoolean(), anyString());


        FinRequest finRequest = finRequestArgument.getValue();
        ActivityBillPayPromptPayConfirmEvent activityEvent = activityEventArument.getValue();
        Assertions.assertEquals("Prompt Pay", finRequest.getToAccNickName());
        Assertions.assertEquals(cacheData.getReceiver().getAccountDisplayName(), finRequest.getToAccName());
        Assertions.assertEquals(correlationId, finRequest.getActivityRefId());
        Assertions.assertEquals(cacheData.getSender().getAccountName(), finRequest.getFromAccName());
        Assertions.assertEquals(cacheData.getPaymentCacheData().getFromAccountNickname(), finRequest.getFromAccNickName());
        Assertions.assertEquals(SUCCESS, finRequest.getTxnStatus());
        Assertions.assertEquals(TXN_TYPE_BILL, finRequest.getTxnType());
        Assertions.assertEquals("XX5633", activityEvent.getReference1());

        NotificationBillPaymentPromptPay notificationPromptPayExpected = new NotificationBillPaymentPromptPay();
        notificationPromptPayExpected.setAddDateTimeEN("18 Aug 22 - 1:37 PM");
        notificationPromptPayExpected.setAddDateTimeTH("18 ส.ค. 65 - 13:37 น.");

        ArgumentCaptor<NotificationBillPaymentPromptPay> notificationPromptPayArgumentCaptor = ArgumentCaptor.forClass(NotificationBillPaymentPromptPay.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .sendENotificationPayment(notificationPromptPayArgumentCaptor.capture());

        Assertions.assertEquals(notificationPromptPayExpected.getAddDateTimeTH(), notificationPromptPayArgumentCaptor.getValue().getAddDateTimeTH());
        Assertions.assertEquals(notificationPromptPayExpected.getAddDateTimeEN(), notificationPromptPayArgumentCaptor.getValue().getAddDateTimeEN());
    }

    @Test
    void paymentConfirmEDonationShouldSuccessTest() throws TMBCommonException, SQLException, TMBCustomCommonExceptionWithResponse, IOException, WriterException {
        mockGetCacheWhenEDonation("0", true);
        mockGetTransactionId();
        mockGetFavoriteList();
        mockETEConfirmBillPay();
        mockGetCurrentDateTime();
        ArgumentCaptor<LegacyAuthenticationRequest> moduleArgument = ArgumentCaptor.forClass(LegacyAuthenticationRequest.class);
        doNothing().when(oauthService).validateAuthentication(any(), any(), moduleArgument.capture(), anyString());

        TopUpConfirmResponse actual = billPaymentPromptPayService.paymentConfirm(crmId, correlationId, confirmRequest, headers);
        Assertions.assertNotNull(actual);
        Assertions.assertEquals(E_DONATION_MODULE_PIN, moduleArgument.getValue().getModule());
    }

    @Test
    void validateQRISOWhenFlagIsOnAndHasChannel97SuccessTest() {
        ReflectionTestUtils.setField(billPaymentPromptPayService, "isQRISO20022FlagOn", true);
        ValidChannel validChannel = new ValidChannel("57", "x", "y", "z");
        Assertions.assertDoesNotThrow(() -> billPaymentPromptPayService.validateQRChannel(List.of(validChannel), true, new TPromptPayETERequest()));
    }

    @Test
    void validateQRISOWhenFlagIsOffAndNoChannel57ShouldThrow() {
        ValidChannel validChannel = new ValidChannel("99", "x", "y", "z");
        Assertions.assertThrows(TMBCommonException.class,
                () -> billPaymentPromptPayService.validateQRChannel(List.of(validChannel), true, new TPromptPayETERequest()));
    }

    @Test
    void validateAuthenticationWhenUseLegacyAuthenticationAndIsEDonationTest() throws TMBCommonException {
        boolean isEDonation = true;
        String transId = "transId";

        var paymentCacheData = new PaymentCacheData()
                .setRequireCommonAuthentication(false)
                .setRequirePin(true);

        Assertions.assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(billPaymentPromptPayService, "validateAuthentication", transId, headers, paymentCacheData, "10.50", isEDonation));

        ArgumentCaptor<LegacyAuthenticationRequest> captor = ArgumentCaptor.forClass(LegacyAuthenticationRequest.class);
        Mockito.verify(oauthService, Mockito.times(1)).validateAuthentication(any(), eq(null), captor.capture(), anyString());
        assertEquals(E_DONATION_MODULE_PIN, captor.getValue().getModule());
    }

    @Test
    void validateAuthenticationWhenUseLegacyAuthenticationAndTransactionFromQRTest() throws TMBCommonException {
        boolean isEDonation = false;
        String transId = "transId";
        String transactionFromQR = "QR";

        var paymentCacheData = new PaymentCacheData()
                .setRequireCommonAuthentication(false)
                .setRequirePin(true)
                .setQr(transactionFromQR);

        Assertions.assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(billPaymentPromptPayService, "validateAuthentication", transId, headers, paymentCacheData, "10.50", isEDonation));

        ArgumentCaptor<LegacyAuthenticationRequest> captor = ArgumentCaptor.forClass(LegacyAuthenticationRequest.class);
        Mockito.verify(oauthService, Mockito.times(1)).validateAuthentication(any(), eq(null), captor.capture(), anyString());
        assertEquals(QR_BILL_MODULE_PIN, captor.getValue().getModule());
    }

    @Test
    void validateAuthenticationWhenUseLegacyAuthenticationTest() throws TMBCommonException {
        boolean isEDonation = false;
        String transId = "transId";
        String transactionFromQR = null;

        var paymentCacheData = new PaymentCacheData()
                .setRequireCommonAuthentication(false)
                .setRequirePin(true)
                .setQr(transactionFromQR);

        Assertions.assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(billPaymentPromptPayService, "validateAuthentication", transId, headers, paymentCacheData, "10.50", isEDonation));

        ArgumentCaptor<LegacyAuthenticationRequest> captor = ArgumentCaptor.forClass(LegacyAuthenticationRequest.class);
        Mockito.verify(oauthService, Mockito.times(1)).validateAuthentication(any(), eq(null), captor.capture(), anyString());
        assertEquals(BILL_MODULE_PIN, captor.getValue().getModule());
    }

    @Test
    void validateAuthenticationWhenUseCommonAuthenticationTest() throws TMBCommonException {
        String transId = "transId";
        String amount = "10.50";

        var paymentCacheData = new PaymentCacheData()
                .setRequireCommonAuthentication(true)
                .setRequirePin(false)
                .setCommonAuthenticationInformation(new CommonAuthenticationInformation()
                        .setFeatureId(COMMON_AUTH_TOP_UP_FEATURE_ID)
                        .setFlowName(COMMON_AUTH_TOP_UP_FLOW_NAME)
                        .setBillerCompCode("comp-code-form-cache")
                        .setTotalPaymentAccumulateUsage(new BigDecimal("1000.00"))
                );

        Assertions.assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(billPaymentPromptPayService, "validateAuthentication", transId, headers, paymentCacheData, amount, true));

        ArgumentCaptor<CommonAuthenWithPayloadRequest> captor = ArgumentCaptor.forClass(CommonAuthenWithPayloadRequest.class);
        Mockito.verify(oauthService, Mockito.times(1)).validateAuthentication(any(), captor.capture(), eq(null), anyString());
        assertEquals(COMMON_AUTH_TOP_UP_FEATURE_ID, captor.getValue().getFeatureId());
        assertEquals(COMMON_AUTH_TOP_UP_FLOW_NAME, captor.getValue().getFlowName());
        assertEquals("comp-code-form-cache", captor.getValue().getBillerCompCode());
        assertEquals("1000.00", captor.getValue().getDailyAmount());
        assertEquals(transId, captor.getValue().getRefId());
        assertEquals(amount, captor.getValue().getAmount());
    }
}