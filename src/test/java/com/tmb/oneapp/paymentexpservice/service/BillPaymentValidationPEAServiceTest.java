package com.tmb.oneapp.paymentexpservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.paymentexpservice.client.CustomerExpFeignClient;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.factory.Factory;
import com.tmb.oneapp.paymentexpservice.model.ActivityTopUpEvent;
import com.tmb.oneapp.paymentexpservice.model.AdditionalParam;
import com.tmb.oneapp.paymentexpservice.model.BillerInfoResponse;
import com.tmb.oneapp.paymentexpservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.CreditCardAccount;
import com.tmb.oneapp.paymentexpservice.model.CreditCardResponse;
import com.tmb.oneapp.paymentexpservice.model.DepositAccount;
import com.tmb.oneapp.paymentexpservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.paymentexpservice.model.OCPAccountPayment;
import com.tmb.oneapp.paymentexpservice.model.OCPBillPayment;
import com.tmb.oneapp.paymentexpservice.model.OCPFee;
import com.tmb.oneapp.paymentexpservice.model.ReferenceTopUpResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityBillPayVerifyEvent;
import com.tmb.oneapp.paymentexpservice.model.commonauth.VerifyTransactionResult;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CardInfo;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCardDetail;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.OnlinePmt;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.OnlinePmtRec;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.PEABillPaymentDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.PEAValidationResponse;
import com.tmb.oneapp.paymentexpservice.service.v1.V1AccountTopUpService;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACCOUNT_TYPE_SAVING;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_COMP_CODE_PEA;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.PEA_TRANS_REF_SEQUENCE;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TOP_UP_PAYMENT_SEQUENCE_DIGIT;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_UTILITIES_LEGACY_ENTER_PAGE;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_UTILITIES_LEGACY_REVIEW_PAGE;
import static java.util.Objects.isNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;

@ExtendWith(MockitoExtension.class)
class BillPaymentValidationPEAServiceTest {

    @Mock
    BillPaymentCustomBillerOCPService billPaymentCustomBillerOCPService;

    @Mock
    CommonPaymentService commonPaymentService;

    @Mock
    LogService logService;

    @Mock
    BillPaymentValidateTransaction billPaymentValidateTransaction;

    @Mock
    CustomerExpFeignClient customerExpFeignClient;

    @Mock
    BillPaymentCreditCardService billPaymentCreditCardService;

    @Mock
    V1AccountTopUpService v1AccountTopUpService;

    @InjectMocks
    BillPaymentValidationPEAService billPaymentValidationPEAService;

    String crmId;
    String correlationId;
    String transId;
    String paymentRequestRef1;
    HttpHeaders headers;
    ActivityTopUpEvent activityEvent;

    @BeforeEach
    void setUp() {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        transId = "BILLPAY_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        paymentRequestRef1 = "**********";
        activityEvent = new ActivityTopUpEvent("", "", "");
        headers = new HttpHeaders();
        headers.add(X_CRMID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);
    }

    private void mockGetTransRefBillPay() {
        Mockito.when(commonPaymentService.genTransRefBillPayRef(crmId, "1", "1", BILL_COMP_CODE_PEA))
                .thenReturn("trnas-**********");
    }

    private void mockGetCreditCareByAccountId() throws TMBCommonException {
        CreditCardDetail creditCardDetail = new CreditCardDetail();
        creditCardDetail.setCardId("477025XXXXXX0167");
        creditCardDetail.setProductId("VSOCHI");
        CardInfo cardInfo = new CardInfo();
        cardInfo.setCardEmbossingName1("test test");
        cardInfo.setExpiredBy("2906");
        creditCardDetail.setCardInfo(cardInfo);
        Mockito.when(billPaymentCreditCardService.getCreditCardDetailByAccountId("0000000050082630661000344", correlationId)).thenReturn(creditCardDetail);
    }

    private void mockGetAccountCreditCard() {
        TmbServiceResponse<CreditCardResponse> creditCardResponse = new TmbServiceResponse<>();
        CreditCardAccount creditCardAccount = new CreditCardAccount();
        creditCardAccount.setAccountId("0000000050082630661000344");
        creditCardResponse.setData(new CreditCardResponse(
                List.of(creditCardAccount),
                Collections.emptyList()
        ));
        Mockito.when(customerExpFeignClient.getAccountsCreditCard(correlationId, crmId)).thenReturn(ResponseEntity.ok(creditCardResponse));
    }

    private void mockGetTransactionID() {
        Mockito.when(commonPaymentService.getTransactionId(Mockito.anyString(), eq(TOP_UP_PAYMENT_SEQUENCE_DIGIT)))
                .thenReturn("pea000000001");
    }

    private void mockFetchBillDetailsPEA() throws TMBCommonException {
        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        billerInfoResponse.setNameEn("PEA");
        billerInfoResponse.setFee(new BigDecimal("10.00"));
        billerInfoResponse.setToAccountId("**********");
        billerInfoResponse.setPaymentMethod("1");
        billerInfoResponse.setBillerMethod("1");
        billerInfoResponse.setBillerCompCode(BILL_COMP_CODE_PEA);
        billerInfoResponse.setBillerCategoryCode("11");
        billerInfoResponse.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        ReferenceTopUpResponse referenceTopUpResponse = new ReferenceTopUpResponse();
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfoResponse);
        billerDetail.setRef1(referenceTopUpResponse);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(BILL_COMP_CODE_PEA, correlationId)).thenReturn(billerDetail);
    }

    private void mockFetchBillPayDetails() throws TMBCommonException {
        BillerTopUpDetailResponse billerDetail = Factory.createBillerDetail(BILL_COMP_CODE_PEA);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(BILL_COMP_CODE_PEA, correlationId)).thenReturn(billerDetail);
    }

    private AdditionalParam generateAdditionalParam(String name, Object value) {
        AdditionalParam additionalParam = new AdditionalParam();
        additionalParam.setName(name);
        additionalParam.setValue(value);
        return additionalParam;
    }

    private void mockCallOCPValidationSuccess(OnlinePmt onlinePmt, boolean overDue) throws TMBCommonException {
        OnlinePmt onlinePmtKeys = new OnlinePmt();
        if (isNull(onlinePmt)) {
            onlinePmtKeys.setRef2("1.00");
            onlinePmtKeys.setAmt("100.00");
            onlinePmtKeys.setEffDt("2021-06-10T18:53:52+07:00");
            onlinePmtKeys.setAmt("300.00");
            onlinePmtKeys.setRef4("PGPEA1");
        } else {
            onlinePmtKeys = onlinePmt;
        }

        LinkedHashMap<String, String> itemMap = new LinkedHashMap<>();
        itemMap.put("InvoiceNo", "INVNO123");
        List<AdditionalParam> additionalParamList = List.of(
                generateAdditionalParam("Item", List.of(itemMap)),
                generateAdditionalParam("Msg", "3|1|202201|20.4100|291.5100|2|202112|28.9000|412.8900|3||3.5000|50.0000"),
                generateAdditionalParam("GrandAmt", "100.50"),
                generateAdditionalParam("OverDue", overDue ? "Y" : "N")
        );

        OnlinePmtRec onlinePmtRec = new OnlinePmtRec();
        onlinePmtRec.setOnlinePmtInfo(onlinePmtKeys);
        onlinePmtRec.setOnlinePmtKeys(onlinePmtKeys);
        OCPBillPayment ocpBillPaymentResponse = new OCPBillPayment()
                .setRbaNo("********")
                .setRequestId("b962003a-6fa7-48b1-81d2-5df3329f4ff8")
                .setRequestDateTime("2022-06-22T11:48:26+07:00")
                .setBankRefId("2022062010482618301")
                .setPaymentId("2022062010482618301")
                .setRef1("*********")
                .setRef2("************")
                .setRef3("0.00")
                .setRef4("")
                .setAmount("40.90")
                .setCurrency("THB")
                .setFromAccount(new OCPAccountPayment())
                .setToAccount(new OCPAccountPayment())
                .setFee(new OCPFee())
                .setAdditionalParams(additionalParamList);
        onlinePmtRec.setOcpBillPayment(ocpBillPaymentResponse);
        Mockito.when(billPaymentCustomBillerOCPService.callOCPValidation(Mockito.any())).thenReturn(onlinePmtRec);
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen() throws TMBCommonException {
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any())).thenReturn(new VerifyTransactionResult(true, new FaceRecognizeResponse().setIsRequireFr(true).setPaymentAccuUsgAmt(BigDecimal.ZERO), new CommonAuthenResult()));
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen() throws TMBCommonException {
        CommonAuthenResult commonAuthenResult = new CommonAuthenResult()
                .setRequireCommonAuthen(true)
                .setIsForceFR(true)
                .setPinFree(true);
        VerifyTransactionResult resultWhenExecuteCommonAuthen = new VerifyTransactionResult(false, null, commonAuthenResult);
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any())).thenReturn(resultWhenExecuteCommonAuthen);
    }


    @Test
    void getPaymentDetailPEAShouldSuccessTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, JsonProcessingException {
        Mockito.when(commonPaymentService.getTransactionId(PEA_TRANS_REF_SEQUENCE, TOP_UP_PAYMENT_SEQUENCE_DIGIT))
                .thenReturn("pea000000001");

        BillerTopUpDetailResponse billerTopUpDetailResponse = new BillerTopUpDetailResponse();
        billerTopUpDetailResponse.setBillerInfo(new BillerInfoResponse()
                .setExpiredDate("9999-12-31T00:00:00.000000+07:00"));
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(Mockito.anyString(), Mockito.anyString())).thenReturn(billerTopUpDetailResponse);

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setAdditionalParams(
                List.of(
                        generateAdditionalParam("TotalVat", "1.00"),
                        generateAdditionalParam("CaName", "name"),
                        generateAdditionalParam("GrandAmt", "100.00"),
                        generateAdditionalParam("CaAddress", "addr"))
        );
        OnlinePmtRec response = new OnlinePmtRec();
        response.setOcpBillPayment(ocpBillPayment);
        Mockito.when(billPaymentCustomBillerOCPService.callOCPValidation(Mockito.any())).thenReturn(response);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setBillerCompCode(BILL_COMP_CODE_PEA);
        PEABillPaymentDetailResponse actual = (PEABillPaymentDetailResponse) billPaymentValidationPEAService.getPaymentDetail(correlationId, topUpVerifyRequest);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals("99.00", actual.getAmount());
        Assertions.assertEquals("1.00", actual.getVat());
        Assertions.assertNull(actual.getScheduleConfig().getTabsOfSchedule());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isOnce());
        Assertions.assertFalse(actual.getScheduleConfig().getFrequency().isWeekly());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isMonthly());
        Assertions.assertNull(actual.getScheduleConfig().getPhrase().getTitleEnterPage());
        Assertions.assertEquals(PHRASE_BILLER_UTILITIES_LEGACY_ENTER_PAGE, actual.getScheduleConfig().getPhrase().getDetailEnterPage());
        Assertions.assertEquals(PHRASE_BILLER_UTILITIES_LEGACY_REVIEW_PAGE, actual.getScheduleConfig().getPhrase().getReviewPage());
    }

    @Test
    void validationPEAShouldSuccess() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        BillerInfoResponse billerInfoResponse = new BillerInfoResponse()
                .setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        billerInfoResponse.setNameEn("PEA");
        billerInfoResponse.setFee(new BigDecimal("10.00"));
        billerInfoResponse.setToAccountId("**********");
        billerInfoResponse.setPaymentMethod("1");
        billerInfoResponse.setBillerMethod("1");
        billerInfoResponse.setBillerCompCode(BILL_COMP_CODE_PEA);
        billerInfoResponse.setBillerCategoryCode("11");
        ReferenceTopUpResponse referenceTopUpResponse = new ReferenceTopUpResponse();
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfoResponse);
        billerDetail.setRef1(referenceTopUpResponse);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(BILL_COMP_CODE_PEA, correlationId)).thenReturn(billerDetail);

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setWaiveFeeForBillpay("0");
        depositAccount.setAccountType(ACCOUNT_TYPE_SAVING);
        depositAccount.setAccountNumber("********90");
        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(
                eq(crmId),
                eq(correlationId),
                eq("********90")
        )).thenReturn(depositAccount);

        Mockito.when(commonPaymentService.getTransactionId(PEA_TRANS_REF_SEQUENCE, TOP_UP_PAYMENT_SEQUENCE_DIGIT))
                .thenReturn("pea000000001");

        OnlinePmt onlinePmtKeys = new OnlinePmt();
        onlinePmtKeys.setRef2("1.00");
        onlinePmtKeys.setAmt("100.00");
        onlinePmtKeys.setEffDt("2021-06-10T18:53:52+07:00");
        onlinePmtKeys.setAmt("300.00");
        onlinePmtKeys.setMsg("3|1|256501|20.4100|291.5100|2|256412|28.9000|412.8900|3||3.5000|50.0000");
        mockCallOCPValidationSuccess(onlinePmtKeys, false);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        mockGetTransRefBillPay();

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode(BILL_COMP_CODE_PEA);
        request.setAccountNumber("********90");
        request.setToFavoriteName("ReqToFavoriteName");
        request.setAmount("100.50");
        PEAValidationResponse actual = (PEAValidationResponse) billPaymentValidationPEAService.validation(request, headers);

        Assertions.assertEquals("110.50", actual.getNetAmount());
        Assertions.assertEquals(3, actual.getBill().size());
        Assertions.assertEquals("01/2022", actual.getBill().get(0).getEletricityOf());
        Assertions.assertEquals("291.51", actual.getBill().get(0).getAmount());
        Assertions.assertEquals("20.41", actual.getBill().get(0).getVat());
        Assertions.assertEquals("12/2021", actual.getBill().get(1).getEletricityOf());
        Assertions.assertEquals("412.89", actual.getBill().get(1).getAmount());
        Assertions.assertEquals("28.90", actual.getBill().get(1).getVat());
        Assertions.assertEquals("", actual.getBill().get(2).getEletricityOf());
        Assertions.assertEquals("50.00", actual.getBill().get(2).getAmount());
        Assertions.assertEquals("3.50", actual.getBill().get(2).getVat());
        Assertions.assertTrue(actual.getIsRequireConfirmPin());
        Assertions.assertFalse(actual.isOverDue());

        ArgumentCaptor<OnlinePmt> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OnlinePmt.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.anyString(), dataSaveToCacheCaptor.capture());

        OnlinePmt actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals(request.getToFavoriteName(), actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        Assertions.assertEquals(depositAccount.getProductNickname(), actualDataSaveToCache.getPaymentCacheData().getFromAccountNickname());
        Assertions.assertEquals(depositAccount.getAccountName(), actualDataSaveToCache.getPaymentCacheData().getFromAccountName());
    }

    @Test
    void validationPEAWhenExecuteWithCommonAuthenticationShouldSuccess() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen();

        BillerInfoResponse billerInfoResponse = new BillerInfoResponse()
                .setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        billerInfoResponse.setNameEn("PEA");
        billerInfoResponse.setFee(new BigDecimal("10.00"));
        billerInfoResponse.setToAccountId("**********");
        billerInfoResponse.setPaymentMethod("1");
        billerInfoResponse.setBillerMethod("1");
        billerInfoResponse.setBillerCompCode(BILL_COMP_CODE_PEA);
        billerInfoResponse.setBillerCategoryCode("11");
        ReferenceTopUpResponse referenceTopUpResponse = new ReferenceTopUpResponse();
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfoResponse);
        billerDetail.setRef1(referenceTopUpResponse);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(BILL_COMP_CODE_PEA, correlationId)).thenReturn(billerDetail);

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setWaiveFeeForBillpay("0");
        depositAccount.setAccountType(ACCOUNT_TYPE_SAVING);
        depositAccount.setAccountNumber("********90");
        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(
                eq(crmId),
                eq(correlationId),
                eq("********90")
        )).thenReturn(depositAccount);

        Mockito.when(commonPaymentService.getTransactionId(PEA_TRANS_REF_SEQUENCE, TOP_UP_PAYMENT_SEQUENCE_DIGIT))
                .thenReturn("pea000000001");

        OnlinePmt onlinePmtKeys = new OnlinePmt();
        onlinePmtKeys.setRef2("1.00");
        onlinePmtKeys.setAmt("100.00");
        onlinePmtKeys.setEffDt("2021-06-10T18:53:52+07:00");
        onlinePmtKeys.setAmt("300.00");
        onlinePmtKeys.setMsg("3|1|256501|20.4100|291.5100|2|256412|28.9000|412.8900|3||3.5000|50.0000");
        mockCallOCPValidationSuccess(onlinePmtKeys, false);

        mockGetTransRefBillPay();

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode(BILL_COMP_CODE_PEA);
        request.setAccountNumber("********90");
        request.setToFavoriteName("ReqToFavoriteName");
        request.setAmount("100.50");
        PEAValidationResponse actual = (PEAValidationResponse) billPaymentValidationPEAService.validation(request, headers);

        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());

    }

    @Test
    void validationPEAWhenRef4PGPEA1ShouldReturnOverDueTrueTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        mockFetchBillPayDetails();

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setWaiveFeeForBillpay("0");
        depositAccount.setAccountType(ACCOUNT_TYPE_SAVING);
        depositAccount.setAccountNumber("********90");
        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(
                eq(crmId),
                eq(correlationId),
                eq("********90")
        )).thenReturn(depositAccount);

        Mockito.when(commonPaymentService.getTransactionId(PEA_TRANS_REF_SEQUENCE, TOP_UP_PAYMENT_SEQUENCE_DIGIT))
                .thenReturn("pea000000001");

        mockCallOCPValidationSuccess(null, true);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        mockGetTransRefBillPay();

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode(BILL_COMP_CODE_PEA);
        request.setAccountNumber("********90");
        request.setAmount("100.50");
        PEAValidationResponse actual = (PEAValidationResponse) billPaymentValidationPEAService.validation(request, headers);

        Assertions.assertEquals("100.50", actual.getNetAmount());
        Assertions.assertEquals(3, actual.getBill().size());
        Assertions.assertEquals("01/2022", actual.getBill().get(0).getEletricityOf());
        Assertions.assertEquals("291.51", actual.getBill().get(0).getAmount());
        Assertions.assertEquals("20.41", actual.getBill().get(0).getVat());
        Assertions.assertEquals("12/2021", actual.getBill().get(1).getEletricityOf());
        Assertions.assertEquals("412.89", actual.getBill().get(1).getAmount());
        Assertions.assertEquals("28.90", actual.getBill().get(1).getVat());
        Assertions.assertEquals("", actual.getBill().get(2).getEletricityOf());
        Assertions.assertEquals("50.00", actual.getBill().get(2).getAmount());
        Assertions.assertEquals("3.50", actual.getBill().get(2).getVat());
        Assertions.assertTrue(actual.isOverDue());
    }

    @Test
    void getPaymentDetailPEAWhenBillerExpireShouldThrowsExceptionTest() throws TMBCommonException {
        BillerTopUpDetailResponse billerTopUpDetailResponse = new BillerTopUpDetailResponse();
        billerTopUpDetailResponse.setBillerInfo(new BillerInfoResponse()
                .setExpiredDate("2000-12-31T00:00:00.000000+07:00")
        );
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(Mockito.anyString(), Mockito.anyString())).thenReturn(billerTopUpDetailResponse);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setBillerCompCode("2700");
        TMBCommonException actualException = Assertions.assertThrows(TMBCommonException.class,
                () -> billPaymentValidationPEAService.getPaymentDetail(correlationId, topUpVerifyRequest));

        Assertions.assertEquals(ResponseCode.BILLER_EXPIRED.getCode(), actualException.getErrorCode());
    }

    @Test
    void validationPEAWhenBillerExpireShouldThrowsExceptionTest() throws TMBCommonException {
        BillerTopUpDetailResponse billerTopUpDetailResponse = new BillerTopUpDetailResponse();
        billerTopUpDetailResponse.setBillerInfo(new BillerInfoResponse()
                .setExpiredDate("2000-12-31T00:00:00.000000+07:00")
        );
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(Mockito.anyString(), Mockito.anyString())).thenReturn(billerTopUpDetailResponse);

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2700");
        TMBCommonException actualException = Assertions.assertThrows(TMBCommonException.class,
                () -> billPaymentValidationPEAService.validation(request, headers));

        Assertions.assertEquals(ResponseCode.BILLER_EXPIRED.getCode(), actualException.getErrorCode());
    }

    @Test
    void validationPEAWhenFromAccountCreditCardShouldSuccessTest() throws TMBCommonException, JsonProcessingException, SQLException, TMBCustomCommonExceptionWithResponse {
        mockFetchBillDetailsPEA();

        mockGetTransactionID();

        mockGetAccountCreditCard();

        mockGetCreditCareByAccountId();

        mockCallOCPValidationSuccess(null, false);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        mockGetTransRefBillPay();

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode(BILL_COMP_CODE_PEA);
        request.setAccountNumber("0000000050082630661000344");
        request.setToFavoriteName("ReqToFavoriteName");
        request.setAmount("100.50");
        request.setIsCreditCard(true);
        PEAValidationResponse actual = (PEAValidationResponse) billPaymentValidationPEAService.validation(request, headers);

        Assertions.assertNotNull(actual.getIsRequireConfirmPin());
        Assertions.assertNotNull(actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());
        Assertions.assertEquals("100.50", actual.getNetAmount());

        ArgumentCaptor<OnlinePmt> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OnlinePmt.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.anyString(), dataSaveToCacheCaptor.capture());

        OnlinePmt actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertNotNull(actualDataSaveToCache.getOcpBillPaymentConfirmRequest());
    }

    @Test
    void validationPEA_WhenCannotGetAccountBelongToCrmId_ShouldThrowsAccountNotEligibleTest() throws TMBCommonException {
        String accountNumberNotMatch = "accountNumberNotMatch";
        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(crmId, correlationId, accountNumberNotMatch)).thenThrow(TMBCommonException.class);

        mockFetchBillDetailsPEA();

        TopUpVerifyRequest request = new TopUpVerifyRequest()
                .setBillerCompCode(BILL_COMP_CODE_PEA)
                .setAccountNumber(accountNumberNotMatch)
                .setToFavoriteName("ReqToFavoriteName")
                .setAmount("100.50")
                .setIsCreditCard(false);

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> billPaymentValidationPEAService.validation(request, headers));

        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ERROR.getCode(), exception.getErrorCode());
        Mockito.verify(logService, Mockito.times(1)).saveLogActivityBillPayEvent(any(ActivityBillPayVerifyEvent.class));
    }

    @Test
    void validationPEA_WhenCannotGetAccountBelongToCrmId_ShouldThrowsExceptionTest() throws TMBCommonException {
        String accountNumberNotMatch = "accountNumberNotMatch";
        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(crmId, correlationId, accountNumberNotMatch)).thenThrow(FeignException.class);

        mockFetchBillDetailsPEA();

        TopUpVerifyRequest request = new TopUpVerifyRequest()
                .setBillerCompCode(BILL_COMP_CODE_PEA)
                .setAccountNumber(accountNumberNotMatch)
                .setToFavoriteName("ReqToFavoriteName")
                .setAmount("100.50")
                .setIsCreditCard(false);

        Assertions.assertThrows(FeignException.class, () -> billPaymentValidationPEAService.validation(request, headers));

        Mockito.verify(logService, Mockito.times(0)).saveLogActivityBillPayEvent(any(ActivityBillPayVerifyEvent.class));
    }

    @Test
    void getAdditionalParamsItemWhenFailedToCastShouldReturnEmptyLinkedHashMap() {
        AdditionalParam additionalParam = generateAdditionalParam("X", new ArrayList<>());

        LinkedHashMap<String, Object> actual = billPaymentValidationPEAService.getAdditionalParamsItem(List.of(additionalParam));

        Assertions.assertNotNull(actual);
        Assertions.assertEquals(0, actual.size());
    }

    @Test
    void getAdditionalParamValueByNameWhenFailedToGetValueShouldReturnNull() {
        Assertions.assertNull(billPaymentValidationPEAService.getAdditionalParamValueByName(null, null));
    }

    @Test
    void validateAmountWhenRequestAmountAndGrandAmountNotMatchedShouldThrow() {
        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setAmount("777.888");
        request.setBillerCompCode(BILL_COMP_CODE_PEA);

        List<AdditionalParam> additionalParamList = List.of(generateAdditionalParam("GrandAmt", "999.876"));
        OCPBillPayment ocpBillPaymentResponse = new OCPBillPayment().setAdditionalParams(additionalParamList);

        OnlinePmtRec onlinePmtRec = new OnlinePmtRec();
        onlinePmtRec.setOcpBillPayment(ocpBillPaymentResponse);

        Assertions.assertThrows(TMBCommonException.class, () -> billPaymentValidationPEAService.validateAmount(request, onlinePmtRec));
    }

    @Test
    void validateAmountWhenFailedToValidateShouldThrow() {
        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode(BILL_COMP_CODE_PEA);
        request.setAmount("666.555");

        List<AdditionalParam> additionalParamList = List.of(generateAdditionalParam("GrandAmt", "ACB"));
        OCPBillPayment ocpBillPaymentResponse = new OCPBillPayment().setAdditionalParams(additionalParamList);

        OnlinePmtRec onlinePmtRec = new OnlinePmtRec();
        onlinePmtRec.setOcpBillPayment(ocpBillPaymentResponse);

        Assertions.assertThrows(Exception.class, () -> billPaymentValidationPEAService.validateAmount(request, onlinePmtRec));
    }

    @Test
    void validateAmountWhenBillerIsNotPEAShouldDoNothing() {
        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("XXXX");

        Assertions.assertDoesNotThrow(() -> billPaymentValidationPEAService.validateAmount(request, new OnlinePmtRec()));
    }
}
