package com.tmb.oneapp.paymentexpservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.paymentexpservice.client.CustomerExpFeignClient;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.model.ActivityTopUpEvent;
import com.tmb.oneapp.paymentexpservice.model.AdditionalParam;
import com.tmb.oneapp.paymentexpservice.model.BillerInfoResponse;
import com.tmb.oneapp.paymentexpservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.CreditCardAccount;
import com.tmb.oneapp.paymentexpservice.model.CreditCardResponse;
import com.tmb.oneapp.paymentexpservice.model.DepositAccount;
import com.tmb.oneapp.paymentexpservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.paymentexpservice.model.OCPAccountPayment;
import com.tmb.oneapp.paymentexpservice.model.OCPBillPayment;
import com.tmb.oneapp.paymentexpservice.model.OCPFee;
import com.tmb.oneapp.paymentexpservice.model.PaymentCacheData;
import com.tmb.oneapp.paymentexpservice.model.ReferenceTopUpResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityBillPayVerifyEvent;
import com.tmb.oneapp.paymentexpservice.model.commonauth.VerifyTransactionResult;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CardInfo;
import com.tmb.oneapp.paymentexpservice.model.creditcard.CreditCardDetail;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.EteOnlinePaymentOCPRequest;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.FromAccountRequestOCP;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.MEABillPaymentDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.MEAValidationResponse;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.OnlinePmt;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.OnlinePmtMiscData;
import com.tmb.oneapp.paymentexpservice.model.custombillpay.OnlinePmtRec;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import com.tmb.oneapp.paymentexpservice.service.v1.V1AccountTopUpService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACCOUNT_TYPE_CCA;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACCOUNT_TYPE_SAVING;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_COMP_CODE_MEA;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BLANK;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.OCP_REQUEST_CHANNEL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ONLINE_TRANS_REF_SEQUENCE;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.REQUEST_APP_ID_OCP_GATEWAY_VALUE;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.SHORT_SAVING_ACCOUNT_TYPE;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TOP_UP_BRANCH_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TOP_UP_CURRENCY;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TOP_UP_PAYMENT_SEQUENCE_DIGIT;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TOP_UP_REF_SEQUENCE_DIGIT;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TRANSFER_REFERENCE_NUMBER_PREFIX;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TTB_BANK_CODE_3DIGIT;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_UTILITIES_LEGACY_ENTER_PAGE;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_UTILITIES_LEGACY_REVIEW_PAGE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;

@ExtendWith(MockitoExtension.class)
class BillPaymentValidationMEAServiceTest {
    @Mock
    BillPaymentLegacyService billPaymentLegacyService;

    @Mock
    CommonPaymentService commonPaymentService;

    @Mock
    LogService logService;

    @Mock
    BillPaymentValidateTransaction billPaymentValidateTransaction;

    @Mock
    BillPaymentCreditCardService billPaymentCreditCardService;

    @Mock
    CustomerExpFeignClient customerExpFeignClient;

    @Mock
    BillPaymentCustomBillerOCPService billPaymentCustomBillerOCPService;

    @Mock
    V1AccountTopUpService v1AccountTopUpService;


    @InjectMocks
    BillPaymentValidationMEAService billPaymentValidationMEAService;

    String crmId;
    String correlationId;
    String transId;
    String paymentRequestRef1;
    HttpHeaders headers;
    ActivityTopUpEvent activityEvent;

    @BeforeEach
    void setUp() {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        transId = "BILLPAY_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        paymentRequestRef1 = "**********";
        activityEvent = new ActivityTopUpEvent("", "", "");
        headers = new HttpHeaders();
        headers.add(X_CRMID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);
    }

    @Test
    void getPaymentDetailMEAShouldSuccessTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, JsonProcessingException {
        Mockito.when(commonPaymentService.getTransactionId(ONLINE_TRANS_REF_SEQUENCE, TOP_UP_PAYMENT_SEQUENCE_DIGIT))
                .thenReturn("2022062010482618300");

        BillerInfoResponse biller = new BillerInfoResponse();
        biller.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        BillerTopUpDetailResponse billerTopUpDetailResponse = new BillerTopUpDetailResponse();
        billerTopUpDetailResponse.setBillerInfo(biller);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(Mockito.anyString(), Mockito.anyString())).thenReturn(billerTopUpDetailResponse);

        OnlinePmt onlinePmtInfo = new OnlinePmt();
        OnlinePmtMiscData onlinePmtMiscData = new OnlinePmtMiscData();
        onlinePmtMiscData.setMiscName("FullAmt");
        onlinePmtMiscData.setMiscText("100.00");
        onlinePmtInfo.setOnlinePmtMiscData(List.of(onlinePmtMiscData));
        onlinePmtInfo.setAmt("40.90");
        onlinePmtInfo.setRef1("********");
        onlinePmtInfo.setRef2("1.00");
        onlinePmtInfo.setRef3("0.00");
        onlinePmtInfo.setRef4("");
        onlinePmtInfo.setMobileNumber("*********");
        onlinePmtInfo.setBankRefId("นายสายัณห์ พนาศุริยสมบัติ");
        onlinePmtInfo.setCustRefId("13/253 ม.17 ม.สวนเทพมณฑล ถ.ศาลาธรรมสพน์ แขวงศาลาธรรมสพน 10170");

        OnlinePmt onlinePmtKeys = new OnlinePmt();
        onlinePmtKeys.setTrnId("2022062010482618300");
        onlinePmtKeys.setMobileNumber("*********");
        onlinePmtKeys.setAmt("40.90");
        onlinePmtKeys.setEffDt("2022-06-20T10:48:26+07:00");
        onlinePmtKeys.setBankId("011");
        onlinePmtKeys.setBranchId("0001");

        OCPAccountPayment toOCPAccountPayment = new OCPAccountPayment();
        toOCPAccountPayment
                .setAccountId("**********")
                .setAccountType("DDA")
                .setTitle("Metropolitan Electricity Authority");

        OCPFee ocpFee = new OCPFee();
        ocpFee
                .setBillPmtFee("10.00")
                .setFlagFeeReg("S");

        AdditionalParam additionalParamInvoiceNum = new AdditionalParam();
        additionalParamInvoiceNum
                .setName("InvoiceNum")
                .setValue("************01TMB03P");
        AdditionalParam additionalParamCustName = new AdditionalParam();
        additionalParamCustName
                .setName("CustName")
                .setValue("นายสายัณห์ พนาศุริยสมบัติ");
        AdditionalParam additionalParamCustAddress = new AdditionalParam();
        additionalParamCustAddress
                .setName("CustAddress")
                .setValue("13/253 ม.17 ม.สวนเทพมณฑล ถ.ศาลาธรรมสพน์ แขวงศาลาธรรมสพน 10170");
        AdditionalParam additionalParamUI = new AdditionalParam();
        additionalParamUI
                .setName("UI")
                .setValue("********");
        AdditionalParam additionalParamTotalInterest = new AdditionalParam();
        additionalParamTotalInterest
                .setName("TotalInterest")
                .setValue("1.00");
        AdditionalParam additionalParamDisconnectedAmount = new AdditionalParam();
        additionalParamDisconnectedAmount
                .setName("DisconnectedAmount")
                .setValue("0.00");
        AdditionalParam additionalParamPayFlag = new AdditionalParam();
        additionalParamPayFlag
                .setName("PayFlag")
                .setValue("Y");

        OCPBillPayment ocpBillPaymentResponse = new OCPBillPayment();
        ocpBillPaymentResponse
                .setRbaNo("********")
                .setRequestId("a962003a-6fa7-48b1-81d2-5df3329f4ff7")
                .setRequestDateTime("2022-06-20T10:48:26+07:00")
                .setBankRefId("2022062010482618300")
                .setPaymentId("2022062010482618300")
                .setRef1("*********")
                .setRef2("************")
                .setRef3("")
                .setRef4("")
                .setAmount("40.90")
                .setCurrency("THB")
                .setToAccount(toOCPAccountPayment)
                .setFee(ocpFee)
                .setAdditionalParams(new ArrayList<>() {
                    {
                        add(additionalParamInvoiceNum);
                        add(additionalParamCustName);
                        add(additionalParamCustAddress);
                        add(additionalParamUI);
                        add(additionalParamTotalInterest);
                        add(additionalParamDisconnectedAmount);
                        add(additionalParamPayFlag);
                    }
                });


        OnlinePmtRec onlinePmtRec = new OnlinePmtRec();
        onlinePmtRec.setOnlinePmtInfo(onlinePmtInfo);
        onlinePmtRec.setOnlinePmtKeys(onlinePmtKeys);
        onlinePmtRec.setOcpBillPayment(ocpBillPaymentResponse);

        Mockito.when(billPaymentCustomBillerOCPService.callOCPValidation(Mockito.any())).thenReturn(onlinePmtRec);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAccountNumber(null);
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setBillerCompCode("2533");
        topUpVerifyRequest.setAmount("");
        MEABillPaymentDetailResponse actual = (MEABillPaymentDetailResponse) billPaymentValidationMEAService.getPaymentDetail(correlationId, topUpVerifyRequest);

        ArgumentCaptor<EteOnlinePaymentOCPRequest> eteOnlinePaymentOCPRequestArgumentCaptor = ArgumentCaptor.forClass(EteOnlinePaymentOCPRequest.class);
        Mockito.verify(billPaymentCustomBillerOCPService, Mockito.times(1))
                .callOCPValidation(eteOnlinePaymentOCPRequestArgumentCaptor.capture());

        EteOnlinePaymentOCPRequest eteOnlinePaymentOCPRequest = eteOnlinePaymentOCPRequestArgumentCaptor.getValue();
        String eteOnlinePaymentOCPRequestString = TMBUtils.convertJavaObjectToString(eteOnlinePaymentOCPRequest);
        Assertions.assertFalse(eteOnlinePaymentOCPRequestString.contains("fromAccount"));
        Assertions.assertEquals("2022062010482618300", eteOnlinePaymentOCPRequest.getPaymentId());
        Assertions.assertEquals("2022062010482618300", eteOnlinePaymentOCPRequest.getBankRefId());
        Assertions.assertEquals("A0478-MB", eteOnlinePaymentOCPRequest.getAppId());
        Assertions.assertEquals("1MB", eteOnlinePaymentOCPRequest.getChannel());
        Assertions.assertEquals("011", eteOnlinePaymentOCPRequest.getBankId());
        Assertions.assertEquals("0001", eteOnlinePaymentOCPRequest.getBranchId());
        Assertions.assertEquals("", eteOnlinePaymentOCPRequest.getTellerId());
        Assertions.assertEquals("0.00", eteOnlinePaymentOCPRequest.getAmount());
        Assertions.assertEquals("THB", eteOnlinePaymentOCPRequest.getCurrency());
        Assertions.assertEquals("2533", eteOnlinePaymentOCPRequest.getCompCode());
        Assertions.assertEquals("*********", eteOnlinePaymentOCPRequest.getRef1());
        Assertions.assertEquals("", eteOnlinePaymentOCPRequest.getRef2());
        Assertions.assertEquals("", eteOnlinePaymentOCPRequest.getRef3());
        Assertions.assertEquals("", eteOnlinePaymentOCPRequest.getRef4());

        MEABillPaymentDetailResponse meaBillPaymentDetailExpected = new MEABillPaymentDetailResponse();
        meaBillPaymentDetailExpected
                .setMeterNo("********")
                .setTotalInterest("1.00")
                .setDisconnectedAmount("0.00")
                .setAmount("39.90")
                .setCustomerName("นายสายัณห์ พนาศุริยสมบัติ")
                .setAddress("13/253 ม.17 ม.สวนเทพมณฑล ถ.ศาลาธรรมสพน์ แขวงศาลาธรรมสพน 10170");

        Assertions.assertEquals(meaBillPaymentDetailExpected.getMeterNo(), actual.getMeterNo());
        Assertions.assertEquals(meaBillPaymentDetailExpected.getTotalInterest(), actual.getTotalInterest());
        Assertions.assertEquals(meaBillPaymentDetailExpected.getDisconnectedAmount(), actual.getDisconnectedAmount());
        Assertions.assertEquals(meaBillPaymentDetailExpected.getAmount(), actual.getAmount());
        Assertions.assertEquals(meaBillPaymentDetailExpected.getCustomerName(), actual.getCustomerName());
        Assertions.assertEquals(meaBillPaymentDetailExpected.getAddress(), actual.getAddress());

        Assertions.assertNotNull(actual);
        Assertions.assertEquals("39.90", actual.getAmount());
        Assertions.assertEquals("40.90", actual.getTotalPaymentAmount());
        Assertions.assertNull(actual.getScheduleConfig().getTabsOfSchedule());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isOnce());
        Assertions.assertFalse(actual.getScheduleConfig().getFrequency().isWeekly());
        Assertions.assertTrue(actual.getScheduleConfig().getFrequency().isMonthly());
        Assertions.assertNull(actual.getScheduleConfig().getPhrase().getTitleEnterPage());
        Assertions.assertEquals(PHRASE_BILLER_UTILITIES_LEGACY_ENTER_PAGE, actual.getScheduleConfig().getPhrase().getDetailEnterPage());
        Assertions.assertEquals(PHRASE_BILLER_UTILITIES_LEGACY_REVIEW_PAGE, actual.getScheduleConfig().getPhrase().getReviewPage());
    }

    @Test
    void getPaymentDetailOtherShouldSuccessTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, JsonProcessingException {
        Mockito.when(commonPaymentService.getTransactionId(ONLINE_TRANS_REF_SEQUENCE, TOP_UP_PAYMENT_SEQUENCE_DIGIT))
                .thenReturn("2022062010482618300");

        BillerInfoResponse biller = new BillerInfoResponse();
        biller.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        BillerTopUpDetailResponse billerTopUpDetailResponse = new BillerTopUpDetailResponse();
        billerTopUpDetailResponse.setBillerInfo(biller);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(Mockito.anyString(), Mockito.anyString())).thenReturn(billerTopUpDetailResponse);

        OnlinePmt onlinePmtInfo = new OnlinePmt();
        OnlinePmtMiscData onlinePmtMiscData = new OnlinePmtMiscData();
        onlinePmtInfo.setOnlinePmtMiscData(List.of(onlinePmtMiscData));
        onlinePmtInfo.setAmt("0.00");
        onlinePmtInfo.setRef2("0.00");
        onlinePmtInfo.setRef3("0.00");

        OnlinePmtRec onlinePmtRec = new OnlinePmtRec();
        onlinePmtRec.setOnlinePmtInfo(onlinePmtInfo);
        Mockito.when(billPaymentLegacyService.callETEValidation(Mockito.any())).thenReturn(onlinePmtRec);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setBillerCompCode("1234");
        MEABillPaymentDetailResponse actual = (MEABillPaymentDetailResponse) billPaymentValidationMEAService.getPaymentDetail(correlationId, topUpVerifyRequest);

        Assertions.assertNotNull(actual);
    }

    @Test
    void validationMEATestShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        Mockito.when(commonPaymentService.getTransactionId(ONLINE_TRANS_REF_SEQUENCE, TOP_UP_PAYMENT_SEQUENCE_DIGIT))
                .thenReturn("2022062010482618300");

        Mockito.when(commonPaymentService.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT))
                .thenReturn("20220**************");

        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        billerInfoResponse.setNameEn("MEA");
        billerInfoResponse.setFee(new BigDecimal("10.00"));
        billerInfoResponse.setToAccountId("**********");
        billerInfoResponse.setPaymentMethod("1");
        billerInfoResponse.setBillerMethod("1");
        billerInfoResponse.setBillerCompCode("2533");
        billerInfoResponse.setBillerCategoryCode("11");
        billerInfoResponse.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        ReferenceTopUpResponse referenceTopUpResponse = new ReferenceTopUpResponse();
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfoResponse);
        billerDetail.setRef1(referenceTopUpResponse);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail("2533", correlationId)).thenReturn(billerDetail);

        DepositAccount depositAccountResp = new DepositAccount();
        depositAccountResp.setWaiveFeeForBillpay("1");
        depositAccountResp.setAccountType(ACCOUNT_TYPE_SAVING);
        depositAccountResp.setAccountNumber("**********");
        depositAccountResp.setProductNickname("FromAccountNickname");
        depositAccountResp.setAccountName("FromAccountName");
        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(
                eq(crmId),
                eq(correlationId),
                eq("**********")
        )).thenReturn(depositAccountResp);

        OCPAccountPayment fromOCPAccountPaymentResp = new OCPAccountPayment();
        fromOCPAccountPaymentResp
                .setFiId("")
                .setAccountId("**********")
                .setAccountType("SDA");

        OCPAccountPayment toOCPAccountPaymentResp = new OCPAccountPayment();
        toOCPAccountPaymentResp
                .setAccountId("**********")
                .setAccountType("DDA")
                .setTitle("Metropolitan Electricity Authority");

        OCPFee ocpFeeResp = new OCPFee();
        ocpFeeResp
                .setBillPmtFee("10.00")
                .setFlagFeeReg("S");

        AdditionalParam additionalParamInvoiceNumResp = new AdditionalParam();
        additionalParamInvoiceNumResp
                .setName("InvoiceNum")
                .setValue("************01TMB03P");
        AdditionalParam additionalParamCustNameResp = new AdditionalParam();
        additionalParamCustNameResp
                .setName("CustName")
                .setValue("นายสายัณห์ พนาศุริยสมบัติ");
        AdditionalParam additionalParamCustAddressResp = new AdditionalParam();
        additionalParamCustAddressResp
                .setName("CustAddress")
                .setValue("13/253 ม.17 ม.สวนเทพมณฑล ถ.ศาลาธรรมสพน์ แขวงศาลาธรรมสพน 10170");
        AdditionalParam additionalParamUI = new AdditionalParam();
        additionalParamUI
                .setName("UI")
                .setValue("********");
        AdditionalParam additionalParamTotalInterestResp = new AdditionalParam();
        additionalParamTotalInterestResp
                .setName("TotalInterest")
                .setValue("1.00");
        AdditionalParam additionalParamDisconnectedAmountResp = new AdditionalParam();
        additionalParamDisconnectedAmountResp
                .setName("DisconnectedAmount")
                .setValue("0.00");
        AdditionalParam additionalParamPayFlagResp = new AdditionalParam();
        additionalParamPayFlagResp
                .setName("PayFlag")
                .setValue("Y");

        OCPBillPayment ocpBillPaymentResponse = new OCPBillPayment();
        ocpBillPaymentResponse
                .setRbaNo("********")
                .setRequestId("a962003a-6fa7-48b1-81d2-5df3329f4ff7")
                .setRequestDateTime("2022-06-20T10:48:26+07:00")
                .setBankRefId("2022062010482618300")
                .setPaymentId("2022062010482618300")
                .setRef1("*********")
                .setRef2("************")
                .setRef3("0.00")
                .setRef4("")
                .setAmount("40.90")
                .setCurrency("THB")
                .setFromAccount(fromOCPAccountPaymentResp)
                .setToAccount(toOCPAccountPaymentResp)
                .setFee(ocpFeeResp)
                .setAdditionalParams(new ArrayList<>() {
                    {
                        add(additionalParamInvoiceNumResp);
                        add(additionalParamCustNameResp);
                        add(additionalParamCustAddressResp);
                        add(additionalParamUI);
                        add(additionalParamTotalInterestResp);
                        add(additionalParamDisconnectedAmountResp);
                        add(additionalParamPayFlagResp);
                    }
                });

        OnlinePmt onlinePmtInfo = new OnlinePmt();
        onlinePmtInfo.setAmt("40.90");
        onlinePmtInfo.setRef1("********");
        onlinePmtInfo.setRef2("1.00");
        onlinePmtInfo.setRef3("0.00");
        onlinePmtInfo.setRef4("");
        onlinePmtInfo.setMobileNumber("*********");
        onlinePmtInfo.setBankRefId("นายสายัณห์ พนาศุริยสมบัติ");
        onlinePmtInfo.setCustRefId("13/253 ม.17 ม.สวนเทพมณฑล ถ.ศาลาธรรมสพน์ แขวงศาลาธรรมสพน 10170");

        OnlinePmt onlinePmtKeys = new OnlinePmt();
        onlinePmtKeys.setTrnId("2022062010482618300");
        onlinePmtKeys.setMobileNumber("*********");
        onlinePmtKeys.setAmt("40.90");
        onlinePmtKeys.setEffDt("2022-06-20T10:48:26+07:00");
        onlinePmtKeys.setBankId("011");
        onlinePmtKeys.setBranchId("0001");

        OnlinePmtRec onlinePmtRec = new OnlinePmtRec();
        onlinePmtRec.setOnlinePmtInfo(onlinePmtInfo);
        onlinePmtRec.setOnlinePmtKeys(onlinePmtKeys);
        onlinePmtRec.setOcpBillPayment(ocpBillPaymentResponse);

        EteOnlinePaymentOCPRequest eteOnlinePaymentOCPRequestVerify = new EteOnlinePaymentOCPRequest();
        eteOnlinePaymentOCPRequestVerify
                .setRequestId("a962003a-6fa7-48b1-81d2-5df3329f4ff7")
                .setRequestDateTime("2022-06-20T10:48:26+07:00")
                .setBankRefId("2022062010482618300")
                .setPaymentId("2022062010482618300")
                .setAppId(REQUEST_APP_ID_OCP_GATEWAY_VALUE)
                .setChannel(OCP_REQUEST_CHANNEL)
                .setBankId(TTB_BANK_CODE_3DIGIT)
                .setBranchId(TOP_UP_BRANCH_ID)
                .setTellerId(BLANK)
                .setAmount("0.00")
                .setCurrency(TOP_UP_CURRENCY)
                .setCompCode(BILL_COMP_CODE_MEA)
                .setRef1("*********")
                .setRef2(BLANK)
                .setRef3(BLANK)
                .setRef4(BLANK);

        FromAccountRequestOCP fromAccountRequestOCP = new FromAccountRequestOCP();
        fromAccountRequestOCP
                .setAccountId("**********")
                .setAccountType(SHORT_SAVING_ACCOUNT_TYPE)
                .setFiId(BLANK);

        eteOnlinePaymentOCPRequestVerify.setFromAccount(fromAccountRequestOCP);

        Mockito.when(billPaymentCustomBillerOCPService.callOCPValidation(Mockito.any(eteOnlinePaymentOCPRequestVerify.getClass()))).thenReturn(onlinePmtRec);

        CustomerCrmProfile dailyLimit = new CustomerCrmProfile();
        Mockito.when(billPaymentValidateTransaction.fetchTransactionLimit(correlationId, crmId)).thenReturn(dailyLimit);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        Mockito.when(commonPaymentService.genTransRefBillPayRef(crmId, "1", "1", "2533"))
                .thenReturn("BILLPAY_2533_12_1_001100000000000000000000123921_ca6242f6-7b2f-4833-a5c4-98cc9d55bc7f");

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2533");
        request.setAccountNumber("**********");
        request.setToFavoriteName("ReqToFavoriteName");
        request.setAmount("40.90");
        request.setReference1("*********");
        request.setReference2("Have value some time if scan from QR");
        MEAValidationResponse actual = (MEAValidationResponse) billPaymentValidationMEAService.validation(request, headers);

        Assertions.assertNotNull(actual.getIsRequireConfirmPin());
        Assertions.assertNotNull(actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());
        Assertions.assertEquals("40.90", actual.getNetAmount());

        ArgumentCaptor<EteOnlinePaymentOCPRequest> eteOnlinePaymentOCPRequestArgumentCaptor = ArgumentCaptor.forClass(EteOnlinePaymentOCPRequest.class);
        Mockito.verify(billPaymentCustomBillerOCPService, Mockito.times(1))
                .callOCPValidation(eteOnlinePaymentOCPRequestArgumentCaptor.capture());

        EteOnlinePaymentOCPRequest eteOnlinePaymentOCPRequest = eteOnlinePaymentOCPRequestArgumentCaptor.getValue();
        Assertions.assertEquals(request.getAmount(), eteOnlinePaymentOCPRequest.getAmount());

        String eteOnlinePaymentOCPRequestString = TMBUtils.convertJavaObjectToString(eteOnlinePaymentOCPRequest);
        Assertions.assertTrue(eteOnlinePaymentOCPRequestString.contains("fromAccount"));
        Assertions.assertEquals("2022062010482618300", eteOnlinePaymentOCPRequest.getPaymentId());
        Assertions.assertEquals("2022062010482618300", eteOnlinePaymentOCPRequest.getBankRefId());
        Assertions.assertEquals("A0478-MB", eteOnlinePaymentOCPRequest.getAppId());
        Assertions.assertEquals("1MB", eteOnlinePaymentOCPRequest.getChannel());
        Assertions.assertEquals("011", eteOnlinePaymentOCPRequest.getBankId());
        Assertions.assertEquals("0001", eteOnlinePaymentOCPRequest.getBranchId());
        Assertions.assertEquals("", eteOnlinePaymentOCPRequest.getTellerId());
        Assertions.assertEquals("40.90", eteOnlinePaymentOCPRequest.getAmount());
        Assertions.assertEquals("THB", eteOnlinePaymentOCPRequest.getCurrency());
        Assertions.assertEquals("2533", eteOnlinePaymentOCPRequest.getCompCode());
        Assertions.assertEquals("*********", eteOnlinePaymentOCPRequest.getRef1());
        Assertions.assertEquals("", eteOnlinePaymentOCPRequest.getRef2());
        Assertions.assertEquals("", eteOnlinePaymentOCPRequest.getRef3());
        Assertions.assertEquals("", eteOnlinePaymentOCPRequest.getRef4());

        ArgumentCaptor<OnlinePmt> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OnlinePmt.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.anyString(), dataSaveToCacheCaptor.capture());

        OnlinePmt actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals(request.getToFavoriteName(), actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        Assertions.assertEquals(depositAccountResp.getProductNickname(), actualDataSaveToCache.getPaymentCacheData().getFromAccountNickname());
        Assertions.assertEquals(depositAccountResp.getAccountName(), actualDataSaveToCache.getPaymentCacheData().getFromAccountName());

        PaymentCacheData paymentCacheDataExpected = new PaymentCacheData();
        MEABillPaymentDetailResponse meaBillPaymentDetailResponse = new MEABillPaymentDetailResponse();
        meaBillPaymentDetailResponse
                .setMeterNo("********")
                .setTotalInterest("1.00")
                .setDisconnectedAmount("0.00")
                .setAmount("39.90")
                .setCustomerName("นายสายัณห์ พนาศุริยสมบัติ")
                .setAddress("13/253 ม.17 ม.สวนเทพมณฑล ถ.ศาลาธรรมสพน์ แขวงศาลาธรรมสพน 10170");
        paymentCacheDataExpected.setMeaBillPaymentDetailResponse(meaBillPaymentDetailResponse);

        Assertions.assertEquals(paymentCacheDataExpected.getMeaBillPaymentDetailResponse().getMeterNo(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getMeterNo());
        Assertions.assertEquals(paymentCacheDataExpected.getMeaBillPaymentDetailResponse().getTotalInterest(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getTotalInterest());
        Assertions.assertEquals(paymentCacheDataExpected.getMeaBillPaymentDetailResponse().getDisconnectedAmount(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getDisconnectedAmount());
        Assertions.assertEquals(paymentCacheDataExpected.getMeaBillPaymentDetailResponse().getAmount(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getAmount());
        Assertions.assertEquals(paymentCacheDataExpected.getMeaBillPaymentDetailResponse().getCustomerName(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getCustomerName());
        Assertions.assertEquals(paymentCacheDataExpected.getMeaBillPaymentDetailResponse().getAddress(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getAddress());
        Assertions.assertEquals("20220**************", actualDataSaveToCache.getOcpBillPaymentConfirmRequest().getEpayCode());
        Assertions.assertEquals("20220**************", actualDataSaveToCache.getBillPmtInfo().getEPAYCode());

        MEAValidationResponse meaValidationResponseExpected = new MEAValidationResponse();
        meaValidationResponseExpected
                .setNetAmount("40.90")
                .setFee(new BigDecimal("0.00"))
                .setTransId("BILLPAY_2533_12_1_001100000000000000000000123921_ca6242f6-7b2f-4833-a5c4-98cc9d55bc7f")
                .setIsRequireConfirmPin(true)
                .setTopUpRef(null)
                .setTopUpAccountName(null)
                .setBillerName(null)
                .setCitizenId(null)
                .setCardEmbossingName(null)
                .setAmount(null);

        Assertions.assertEquals(meaValidationResponseExpected.getNetAmount(), actual.getNetAmount());
        Assertions.assertEquals(meaValidationResponseExpected.getFee(), actual.getFee());
        Assertions.assertEquals(meaValidationResponseExpected.getTransId(), actual.getTransId());
        Assertions.assertEquals(meaValidationResponseExpected.getIsRequireConfirmPin(), actual.getIsRequireConfirmPin());
        Assertions.assertEquals(meaValidationResponseExpected.getTopUpRef(), actual.getTopUpRef());
        Assertions.assertEquals(meaValidationResponseExpected.getTopUpAccountName(), actual.getTopUpAccountName());
        Assertions.assertEquals(meaValidationResponseExpected.getBillerName(), actual.getBillerName());
        Assertions.assertEquals(meaValidationResponseExpected.getCitizenId(), actual.getCitizenId());
        Assertions.assertEquals(meaValidationResponseExpected.getCardEmbossingName(), actual.getCardEmbossingName());
        Assertions.assertEquals(meaValidationResponseExpected.getAmount(), actual.getAmount());

        ArgumentCaptor<ActivityBillPayVerifyEvent> activityEventCapture = ArgumentCaptor.forClass(ActivityBillPayVerifyEvent.class);
        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(activityEventCapture.capture());
        Assertions.assertNull(activityEventCapture.getValue().getReference2());

    }

    @Test
    void validationMEAWhenExecuteCommonAuthenticationShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen();

        Mockito.when(commonPaymentService.getTransactionId(ONLINE_TRANS_REF_SEQUENCE, TOP_UP_PAYMENT_SEQUENCE_DIGIT))
                .thenReturn("2022062010482618300");

        Mockito.when(commonPaymentService.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT))
                .thenReturn("20220**************");

        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        billerInfoResponse.setNameEn("MEA");
        billerInfoResponse.setFee(new BigDecimal("10.00"));
        billerInfoResponse.setToAccountId("**********");
        billerInfoResponse.setPaymentMethod("1");
        billerInfoResponse.setBillerMethod("1");
        billerInfoResponse.setBillerCompCode("2533");
        billerInfoResponse.setBillerCategoryCode("11");
        billerInfoResponse.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        ReferenceTopUpResponse referenceTopUpResponse = new ReferenceTopUpResponse();
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfoResponse);
        billerDetail.setRef1(referenceTopUpResponse);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail("2533", correlationId)).thenReturn(billerDetail);

        DepositAccount depositAccountResp = new DepositAccount();
        depositAccountResp.setWaiveFeeForBillpay("1");
        depositAccountResp.setAccountType(ACCOUNT_TYPE_SAVING);
        depositAccountResp.setAccountNumber("**********");
        depositAccountResp.setProductNickname("FromAccountNickname");
        depositAccountResp.setAccountName("FromAccountName");
        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(
                eq(crmId),
                eq(correlationId),
                eq("**********")
        )).thenReturn(depositAccountResp);

        OCPAccountPayment fromOCPAccountPaymentResp = new OCPAccountPayment();
        fromOCPAccountPaymentResp
                .setFiId("")
                .setAccountId("**********")
                .setAccountType("SDA");

        OCPAccountPayment toOCPAccountPaymentResp = new OCPAccountPayment();
        toOCPAccountPaymentResp
                .setAccountId("**********")
                .setAccountType("DDA")
                .setTitle("Metropolitan Electricity Authority");

        OCPFee ocpFeeResp = new OCPFee();
        ocpFeeResp
                .setBillPmtFee("10.00")
                .setFlagFeeReg("S");

        AdditionalParam additionalParamInvoiceNumResp = new AdditionalParam();
        additionalParamInvoiceNumResp
                .setName("InvoiceNum")
                .setValue("************01TMB03P");
        AdditionalParam additionalParamCustNameResp = new AdditionalParam();
        additionalParamCustNameResp
                .setName("CustName")
                .setValue("นายสายัณห์ พนาศุริยสมบัติ");
        AdditionalParam additionalParamCustAddressResp = new AdditionalParam();
        additionalParamCustAddressResp
                .setName("CustAddress")
                .setValue("13/253 ม.17 ม.สวนเทพมณฑล ถ.ศาลาธรรมสพน์ แขวงศาลาธรรมสพน 10170");
        AdditionalParam additionalParamUI = new AdditionalParam();
        additionalParamUI
                .setName("UI")
                .setValue("********");
        AdditionalParam additionalParamTotalInterestResp = new AdditionalParam();
        additionalParamTotalInterestResp
                .setName("TotalInterest")
                .setValue("1.00");
        AdditionalParam additionalParamDisconnectedAmountResp = new AdditionalParam();
        additionalParamDisconnectedAmountResp
                .setName("DisconnectedAmount")
                .setValue("0.00");
        AdditionalParam additionalParamPayFlagResp = new AdditionalParam();
        additionalParamPayFlagResp
                .setName("PayFlag")
                .setValue("Y");

        OCPBillPayment ocpBillPaymentResponse = new OCPBillPayment();
        ocpBillPaymentResponse
                .setRbaNo("********")
                .setRequestId("a962003a-6fa7-48b1-81d2-5df3329f4ff7")
                .setRequestDateTime("2022-06-20T10:48:26+07:00")
                .setBankRefId("2022062010482618300")
                .setPaymentId("2022062010482618300")
                .setRef1("*********")
                .setRef2("************")
                .setRef3("0.00")
                .setRef4("")
                .setAmount("40.90")
                .setCurrency("THB")
                .setFromAccount(fromOCPAccountPaymentResp)
                .setToAccount(toOCPAccountPaymentResp)
                .setFee(ocpFeeResp)
                .setAdditionalParams(new ArrayList<>() {
                    {
                        add(additionalParamInvoiceNumResp);
                        add(additionalParamCustNameResp);
                        add(additionalParamCustAddressResp);
                        add(additionalParamUI);
                        add(additionalParamTotalInterestResp);
                        add(additionalParamDisconnectedAmountResp);
                        add(additionalParamPayFlagResp);
                    }
                });

        OnlinePmt onlinePmtInfo = new OnlinePmt();
        onlinePmtInfo.setAmt("40.90");
        onlinePmtInfo.setRef1("********");
        onlinePmtInfo.setRef2("1.00");
        onlinePmtInfo.setRef3("0.00");
        onlinePmtInfo.setRef4("");
        onlinePmtInfo.setMobileNumber("*********");
        onlinePmtInfo.setBankRefId("นายสายัณห์ พนาศุริยสมบัติ");
        onlinePmtInfo.setCustRefId("13/253 ม.17 ม.สวนเทพมณฑล ถ.ศาลาธรรมสพน์ แขวงศาลาธรรมสพน 10170");

        OnlinePmt onlinePmtKeys = new OnlinePmt();
        onlinePmtKeys.setTrnId("2022062010482618300");
        onlinePmtKeys.setMobileNumber("*********");
        onlinePmtKeys.setAmt("40.90");
        onlinePmtKeys.setEffDt("2022-06-20T10:48:26+07:00");
        onlinePmtKeys.setBankId("011");
        onlinePmtKeys.setBranchId("0001");

        OnlinePmtRec onlinePmtRec = new OnlinePmtRec();
        onlinePmtRec.setOnlinePmtInfo(onlinePmtInfo);
        onlinePmtRec.setOnlinePmtKeys(onlinePmtKeys);
        onlinePmtRec.setOcpBillPayment(ocpBillPaymentResponse);

        EteOnlinePaymentOCPRequest eteOnlinePaymentOCPRequestVerify = new EteOnlinePaymentOCPRequest();
        eteOnlinePaymentOCPRequestVerify
                .setRequestId("a962003a-6fa7-48b1-81d2-5df3329f4ff7")
                .setRequestDateTime("2022-06-20T10:48:26+07:00")
                .setBankRefId("2022062010482618300")
                .setPaymentId("2022062010482618300")
                .setAppId(REQUEST_APP_ID_OCP_GATEWAY_VALUE)
                .setChannel(OCP_REQUEST_CHANNEL)
                .setBankId(TTB_BANK_CODE_3DIGIT)
                .setBranchId(TOP_UP_BRANCH_ID)
                .setTellerId(BLANK)
                .setAmount("0.00")
                .setCurrency(TOP_UP_CURRENCY)
                .setCompCode(BILL_COMP_CODE_MEA)
                .setRef1("*********")
                .setRef2(BLANK)
                .setRef3(BLANK)
                .setRef4(BLANK);

        FromAccountRequestOCP fromAccountRequestOCP = new FromAccountRequestOCP();
        fromAccountRequestOCP
                .setAccountId("**********")
                .setAccountType(SHORT_SAVING_ACCOUNT_TYPE)
                .setFiId(BLANK);

        eteOnlinePaymentOCPRequestVerify.setFromAccount(fromAccountRequestOCP);

        Mockito.when(billPaymentCustomBillerOCPService.callOCPValidation(Mockito.any(eteOnlinePaymentOCPRequestVerify.getClass()))).thenReturn(onlinePmtRec);

        CustomerCrmProfile dailyLimit = new CustomerCrmProfile();
        Mockito.when(billPaymentValidateTransaction.fetchTransactionLimit(correlationId, crmId)).thenReturn(dailyLimit);

        Mockito.when(commonPaymentService.genTransRefBillPayRef(crmId, "1", "1", "2533"))
                .thenReturn("BILLPAY_2533_12_1_001100000000000000000000123921_ca6242f6-7b2f-4833-a5c4-98cc9d55bc7f");

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2533");
        request.setAccountNumber("**********");
        request.setToFavoriteName("ReqToFavoriteName");
        request.setAmount("40.90");
        request.setReference1("*********");
        request.setReference2("Have value some time if scan from QR");

        MEAValidationResponse actual = (MEAValidationResponse) billPaymentValidationMEAService.validation(request, headers);

        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());

        ArgumentCaptor<OnlinePmt> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OnlinePmt.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.anyString(), dataSaveToCacheCaptor.capture());

        OnlinePmt actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertTrue(actualDataSaveToCache.getPaymentCacheData().isRequireCommonAuthentication());
        Assertions.assertNotNull(actualDataSaveToCache.getPaymentCacheData().getCommonAuthenticationInformation());
    }

    @Test
    void validationMEAPayWithCreditCardShouldSuccessTest() throws JsonProcessingException, TMBCommonException, SQLException, TMBCustomCommonExceptionWithResponse {
        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        billerInfoResponse.setNameEn("MEA");
        billerInfoResponse.setFee(new BigDecimal("10.00"));
        billerInfoResponse.setToAccountId("**********");
        billerInfoResponse.setPaymentMethod("1");
        billerInfoResponse.setBillerMethod("1");
        billerInfoResponse.setBillerCompCode("2533");
        billerInfoResponse.setBillerCategoryCode("11");
        billerInfoResponse.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        ReferenceTopUpResponse referenceTopUpResponse = new ReferenceTopUpResponse();
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfoResponse);
        billerDetail.setRef1(referenceTopUpResponse);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail("2533", correlationId)).thenReturn(billerDetail);

        TmbServiceResponse<CreditCardResponse> creditCardResponse = new TmbServiceResponse<>();
        CreditCardAccount creditCardAccount = new CreditCardAccount();
        creditCardAccount.setAccountId("0000000050082630661000344");
        creditCardResponse.setData(new CreditCardResponse(
                List.of(creditCardAccount),
                Collections.emptyList()
        ));
        Mockito.when(customerExpFeignClient.getAccountsCreditCard(correlationId, crmId)).thenReturn(ResponseEntity.ok(creditCardResponse));

        CreditCardDetail creditCardDetail = new CreditCardDetail();
        creditCardDetail.setCardId("477025XXXXXX0167");
        creditCardDetail.setProductId("VSOCHI");
        CardInfo cardInfo = new CardInfo();
        cardInfo.setCardEmbossingName1("test test");
        cardInfo.setExpiredBy("2906");
        creditCardDetail.setCardInfo(cardInfo);
        Mockito.when(billPaymentCreditCardService.getCreditCardDetailByAccountId("0000000050082630661000344", correlationId)).thenReturn(creditCardDetail);

        OCPAccountPayment fromOCPAccountPaymentResp = new OCPAccountPayment();
        fromOCPAccountPaymentResp
                .setFiId("")
                .setAccountId("000000050082630661000344")
                .setAccountType("CCA");

        OCPAccountPayment toOCPAccountPaymentResp = new OCPAccountPayment();
        toOCPAccountPaymentResp
                .setAccountId("**********")
                .setAccountType("DDA")
                .setTitle("Metropolitan Electricity Authority");

        OCPFee ocpFeeResp = new OCPFee();
        ocpFeeResp
                .setBillPmtFee("0.00")
                .setFlagFeeReg("S");

        AdditionalParam additionalParamInvoiceNum = new AdditionalParam();
        additionalParamInvoiceNum
                .setName("InvoiceNum")
                .setValue("22189000062201TMB03P");
        AdditionalParam additionalParamCustName = new AdditionalParam();
        additionalParamCustName
                .setName("CustName")
                .setValue("นายสายัณห์ พนาศุริยสมบัติ");
        AdditionalParam additionalParamCustAddress = new AdditionalParam();
        additionalParamCustAddress
                .setName("CustAddress")
                .setValue("13/253 ม.17 ม.สวนเทพมณฑล ถ.ศาลาธรรมสพน์ แขวงศาลาธรรมสพน 10170");
        AdditionalParam additionalParamUI = new AdditionalParam();
        additionalParamUI
                .setName("UI")
                .setValue("********");
        AdditionalParam additionalParamTotalInterest = new AdditionalParam();
        additionalParamTotalInterest
                .setName("TotalInterest")
                .setValue("0.00");
        AdditionalParam additionalParamDisconnectedAmount = new AdditionalParam();
        additionalParamDisconnectedAmount
                .setName("DisconnectedAmount")
                .setValue("0.00");
        AdditionalParam additionalParamPayFlag = new AdditionalParam();
        additionalParamPayFlag
                .setName("PayFlag")
                .setValue("Y");

        OCPBillPayment ocpBillPaymentResponse = new OCPBillPayment();
        ocpBillPaymentResponse
                .setAmount("100.50")
                .setFromAccount(fromOCPAccountPaymentResp)
                .setToAccount(toOCPAccountPaymentResp)
                .setFee(ocpFeeResp)
                .setAdditionalParams(new ArrayList<>() {
                    {
                        add(additionalParamInvoiceNum);
                        add(additionalParamCustName);
                        add(additionalParamCustAddress);
                        add(additionalParamUI);
                        add(additionalParamTotalInterest);
                        add(additionalParamDisconnectedAmount);
                        add(additionalParamPayFlag);
                    }
                });

        OnlinePmt onlinePmtKeys = new OnlinePmt();
        onlinePmtKeys.setRef2("1.00");
        onlinePmtKeys.setEffDt("2021-06-10T18:53:52+07:00");
        onlinePmtKeys.setAmt("300.00");
        onlinePmtKeys.setRef1("********");
        onlinePmtKeys.setRef3("0.00");
        onlinePmtKeys.setBankRefId("นางสาวนวรัตน์ จิตรจำเริญรุ่ง");
        onlinePmtKeys.setCustRefId("189/268 อาคารแกรนด์ พาร์ควิว อโศก");

        OnlinePmtRec onlinePmtRec = new OnlinePmtRec();
        onlinePmtRec.setOnlinePmtInfo(onlinePmtKeys);
        onlinePmtRec.setOnlinePmtKeys(onlinePmtKeys);
        onlinePmtRec.setOcpBillPayment(ocpBillPaymentResponse);

        EteOnlinePaymentOCPRequest eteOnlinePaymentOCPRequestVerify = new EteOnlinePaymentOCPRequest();
        eteOnlinePaymentOCPRequestVerify
                .setRequestId("a962003a-6fa7-48b1-81d2-5df3329f4ff7")
                .setRequestDateTime("2022-06-20T10:48:26+07:00")
                .setBankRefId("2022062010482618300")
                .setPaymentId("2022062010482618300")
                .setAppId(REQUEST_APP_ID_OCP_GATEWAY_VALUE)
                .setChannel(OCP_REQUEST_CHANNEL)
                .setBankId(TTB_BANK_CODE_3DIGIT)
                .setBranchId(TOP_UP_BRANCH_ID)
                .setTellerId(BLANK)
                .setAmount("0.00")
                .setCurrency(TOP_UP_CURRENCY)
                .setCompCode(BILL_COMP_CODE_MEA)
                .setRef1("*********")
                .setRef2(BLANK)
                .setRef3(BLANK)
                .setRef4(BLANK)
                .setTranCode("8890");

        FromAccountRequestOCP fromAccountRequestOCP = new FromAccountRequestOCP();
        fromAccountRequestOCP
                .setAccountId("000000050082630661000344")
                .setAccountType(ACCOUNT_TYPE_CCA)
                .setFiId(BLANK);

        eteOnlinePaymentOCPRequestVerify.setFromAccount(fromAccountRequestOCP);

        Mockito.when(billPaymentCustomBillerOCPService.callOCPValidation(Mockito.any(eteOnlinePaymentOCPRequestVerify.getClass()))).thenReturn(onlinePmtRec);

        CustomerCrmProfile dailyLimit = new CustomerCrmProfile();
        Mockito.when(billPaymentValidateTransaction.fetchTransactionLimit(correlationId, crmId)).thenReturn(dailyLimit);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        Mockito.when(commonPaymentService.genTransRefBillPayRef(crmId, "1", "1", "2533"))
                .thenReturn("trnas-**********");

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2533");
        request.setAccountNumber("0000000050082630661000344");
        request.setToFavoriteName("ReqToFavoriteName");
        request.setAmount("100.50");
        request.setIsCreditCard(true);
        MEAValidationResponse actual = (MEAValidationResponse) billPaymentValidationMEAService.validation(request, headers);

        ArgumentCaptor<EteOnlinePaymentOCPRequest> eteOnlinePaymentOCPRequestArgumentCaptor = ArgumentCaptor.forClass(EteOnlinePaymentOCPRequest.class);
        Mockito.verify(billPaymentCustomBillerOCPService, Mockito.times(1))
                .callOCPValidation(eteOnlinePaymentOCPRequestArgumentCaptor.capture());

        EteOnlinePaymentOCPRequest eteOnlinePaymentOCPRequest = eteOnlinePaymentOCPRequestArgumentCaptor.getValue();
        Assertions.assertEquals("000000050082630661000344", eteOnlinePaymentOCPRequest.getFromAccount().getAccountId());

        Assertions.assertNotNull(actual.getIsRequireConfirmPin());
        Assertions.assertNotNull(actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());
        Assertions.assertEquals("300.00", actual.getNetAmount());

        ArgumentCaptor<OnlinePmt> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OnlinePmt.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.anyString(), dataSaveToCacheCaptor.capture());

        OnlinePmt actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals("CRD", actualDataSaveToCache.getRef1());
        Assertions.assertEquals("0.00", actualDataSaveToCache.getRef3());
        Assertions.assertEquals("CardId", actualDataSaveToCache.getOnlinePmtMiscData().get(0).getMiscName());
        Assertions.assertEquals("477025XXXXXX0167", actualDataSaveToCache.getOnlinePmtMiscData().get(0).getMiscText());
        Assertions.assertEquals("CardProductId", actualDataSaveToCache.getOnlinePmtMiscData().get(1).getMiscName());
        Assertions.assertEquals("VSOCHI", actualDataSaveToCache.getOnlinePmtMiscData().get(1).getMiscText());
        Assertions.assertEquals("CardEmbossName", actualDataSaveToCache.getOnlinePmtMiscData().get(2).getMiscName());
        Assertions.assertEquals("test test", actualDataSaveToCache.getOnlinePmtMiscData().get(2).getMiscText());
        Assertions.assertEquals("ExpiredDt", actualDataSaveToCache.getOnlinePmtMiscData().get(3).getMiscName());
        Assertions.assertEquals("2906", actualDataSaveToCache.getOnlinePmtMiscData().get(3).getMiscText());
        Assertions.assertEquals("8790", actualDataSaveToCache.getBillPmtInfo().getTranCode());
        Assertions.assertEquals("MEA", actualDataSaveToCache.getBillPmtInfo().getToAcctRef().getToAcctName());
        Assertions.assertEquals("test test", actualDataSaveToCache.getBillPmtInfo().getBillRefCustomerFirstName());
        Assertions.assertEquals(request.getToFavoriteName(), actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        Assertions.assertEquals("0.00", actualDataSaveToCache.getOcpBillPaymentConfirmRequest().getFee().getBillPmtFee());
        Assertions.assertEquals("0000000050082630661000344", actualDataSaveToCache.getOcpBillPaymentConfirmRequest().getFromAccount().getAccountId());

        PaymentCacheData expected = new PaymentCacheData();
        MEABillPaymentDetailResponse meaBillPaymentDetailResponse = new MEABillPaymentDetailResponse();
        meaBillPaymentDetailResponse
                .setMeterNo("********")
                .setTotalInterest("1.00")
                .setDisconnectedAmount("0.00")
                .setAmount("299.00")
                .setCustomerName("นางสาวนวรัตน์ จิตรจำเริญรุ่ง")
                .setAddress("189/268 อาคารแกรนด์ พาร์ควิว อโศก");
        expected.setMeaBillPaymentDetailResponse(meaBillPaymentDetailResponse);
        expected.setBillerResp(billerDetail);

        List<AdditionalParam> additionalParams = actualDataSaveToCache.getOcpBillPaymentConfirmRequest().getAdditionalParams();
        int lastIndexAdditionalParams = additionalParams.size() - 1;

        Assertions.assertEquals(expected.getMeaBillPaymentDetailResponse().getMeterNo(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getMeterNo());
        Assertions.assertEquals(expected.getMeaBillPaymentDetailResponse().getTotalInterest(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getTotalInterest());
        Assertions.assertEquals(expected.getMeaBillPaymentDetailResponse().getDisconnectedAmount(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getDisconnectedAmount());
        Assertions.assertEquals(expected.getMeaBillPaymentDetailResponse().getAmount(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getAmount());
        Assertions.assertEquals(expected.getMeaBillPaymentDetailResponse().getCustomerName(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getCustomerName());
        Assertions.assertEquals(expected.getMeaBillPaymentDetailResponse().getAddress(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getAddress());
        Assertions.assertEquals(expected.getBillerResp().getBillerInfo().getNameEn(), additionalParams.get(lastIndexAdditionalParams).getValue());
        Assertions.assertEquals("billerName", additionalParams.get(lastIndexAdditionalParams).getName());
    }

    @Test
    void getPaymentDetailMEAWhenBillerExpireShouldThrowsExceptionTest() throws TMBCommonException {
        BillerInfoResponse billerExpire = new BillerInfoResponse();
        billerExpire.setExpiredDate("1990-12-31T00:00:00.000000+07:00");
        BillerTopUpDetailResponse billerTopUpDetailResponse = new BillerTopUpDetailResponse();
        billerTopUpDetailResponse.setBillerInfo(billerExpire);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(Mockito.anyString(), Mockito.anyString())).thenReturn(billerTopUpDetailResponse);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setBillerCompCode("1234");
        TMBCommonException actualException = Assertions.assertThrows(TMBCommonException.class,
                () -> billPaymentValidationMEAService.getPaymentDetail(correlationId, topUpVerifyRequest));

        Assertions.assertEquals(ResponseCode.BILLER_EXPIRED.getCode(), actualException.getErrorCode());
    }

    @Test
    void validationMEAWhenBillerExpireShouldThrowsExceptionTest() throws TMBCommonException {
        BillerInfoResponse billerExpire = new BillerInfoResponse();
        billerExpire.setExpiredDate("1990-12-31T00:00:00.000000+07:00");
        BillerTopUpDetailResponse billerTopUpDetailResponse = new BillerTopUpDetailResponse();
        billerTopUpDetailResponse.setBillerInfo(billerExpire);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail(Mockito.anyString(), Mockito.anyString())).thenReturn(billerTopUpDetailResponse);

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2533");
        TMBCommonException actualException = Assertions.assertThrows(TMBCommonException.class,
                () -> billPaymentValidationMEAService.validation(request, headers));

        Assertions.assertEquals(ResponseCode.BILLER_EXPIRED.getCode(), actualException.getErrorCode());
    }

    @Test
    void validationMEANoteTestShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        Mockito.when(commonPaymentService.getTransactionId(ONLINE_TRANS_REF_SEQUENCE, TOP_UP_PAYMENT_SEQUENCE_DIGIT))
                .thenReturn("2022062010482618300");

        Mockito.when(commonPaymentService.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT))
                .thenReturn("20220**************");

        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        billerInfoResponse.setNameEn("MEA");
        billerInfoResponse.setFee(new BigDecimal("10.00"));
        billerInfoResponse.setToAccountId("**********");
        billerInfoResponse.setPaymentMethod("1");
        billerInfoResponse.setBillerMethod("1");
        billerInfoResponse.setBillerCompCode("2533");
        billerInfoResponse.setBillerCategoryCode("11");
        billerInfoResponse.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        ReferenceTopUpResponse referenceTopUpResponse = new ReferenceTopUpResponse();
        BillerTopUpDetailResponse billerDetail = new BillerTopUpDetailResponse();
        billerDetail.setBillerInfo(billerInfoResponse);
        billerDetail.setRef1(referenceTopUpResponse);
        Mockito.when(commonPaymentService.fetchBillerBillPayDetail("2533", correlationId)).thenReturn(billerDetail);

        DepositAccount depositAccountResp = new DepositAccount();
        depositAccountResp.setWaiveFeeForBillpay("1");
        depositAccountResp.setAccountType(ACCOUNT_TYPE_SAVING);
        depositAccountResp.setAccountNumber("**********");
        depositAccountResp.setProductNickname("FromAccountNickname");
        depositAccountResp.setAccountName("FromAccountName");
        Mockito.when(v1AccountTopUpService.getAccountBelongToCrmId(
                eq(crmId),
                eq(correlationId),
                eq("**********")
        )).thenReturn(depositAccountResp);

        OCPAccountPayment fromOCPAccountPaymentResp = new OCPAccountPayment();
        fromOCPAccountPaymentResp
                .setFiId("")
                .setAccountId("**********")
                .setAccountType("SDA");

        OCPAccountPayment toOCPAccountPaymentResp = new OCPAccountPayment();
        toOCPAccountPaymentResp
                .setAccountId("**********")
                .setAccountType("DDA")
                .setTitle("Metropolitan Electricity Authority");

        OCPFee ocpFeeResp = new OCPFee();
        ocpFeeResp
                .setBillPmtFee("10.00")
                .setFlagFeeReg("S");

        AdditionalParam additionalParamInvoiceNumResp = new AdditionalParam();
        additionalParamInvoiceNumResp
                .setName("InvoiceNum")
                .setValue("************01TMB03P");
        AdditionalParam additionalParamCustNameResp = new AdditionalParam();
        additionalParamCustNameResp
                .setName("CustName")
                .setValue("นายสายัณห์ พนาศุริยสมบัติ");
        AdditionalParam additionalParamCustAddressResp = new AdditionalParam();
        additionalParamCustAddressResp
                .setName("CustAddress")
                .setValue("13/253 ม.17 ม.สวนเทพมณฑล ถ.ศาลาธรรมสพน์ แขวงศาลาธรรมสพน 10170");
        AdditionalParam additionalParamUI = new AdditionalParam();
        additionalParamUI
                .setName("UI")
                .setValue("********");
        AdditionalParam additionalParamTotalInterestResp = new AdditionalParam();
        additionalParamTotalInterestResp
                .setName("TotalInterest")
                .setValue("1.00");
        AdditionalParam additionalParamDisconnectedAmountResp = new AdditionalParam();
        additionalParamDisconnectedAmountResp
                .setName("DisconnectedAmount")
                .setValue("0.00");
        AdditionalParam additionalParamPayFlagResp = new AdditionalParam();
        additionalParamPayFlagResp
                .setName("PayFlag")
                .setValue("Y");

        OCPBillPayment ocpBillPaymentResponse = new OCPBillPayment();
        ocpBillPaymentResponse
                .setRbaNo("********")
                .setRequestId("a962003a-6fa7-48b1-81d2-5df3329f4ff7")
                .setRequestDateTime("2022-06-20T10:48:26+07:00")
                .setBankRefId("2022062010482618300")
                .setPaymentId("2022062010482618300")
                .setRef1("*********")
                .setRef2("************")
                .setRef3("0.00")
                .setRef4("")
                .setAmount("40.90")
                .setCurrency("THB")
                .setFromAccount(fromOCPAccountPaymentResp)
                .setToAccount(toOCPAccountPaymentResp)
                .setFee(ocpFeeResp)
                .setAdditionalParams(new ArrayList<>() {
                    {
                        add(additionalParamInvoiceNumResp);
                        add(additionalParamCustNameResp);
                        add(additionalParamCustAddressResp);
                        add(additionalParamUI);
                        add(additionalParamTotalInterestResp);
                        add(additionalParamDisconnectedAmountResp);
                        add(additionalParamPayFlagResp);
                    }
                });

        OnlinePmt onlinePmtInfo = new OnlinePmt();
        onlinePmtInfo.setAmt("40.90");
        onlinePmtInfo.setRef1("********");
        onlinePmtInfo.setRef2("1.00");
        onlinePmtInfo.setRef3("0.00");
        onlinePmtInfo.setRef4("");
        onlinePmtInfo.setMobileNumber("*********");
        onlinePmtInfo.setBankRefId("นายสายัณห์ พนาศุริยสมบัติ");
        onlinePmtInfo.setCustRefId("13/253 ม.17 ม.สวนเทพมณฑล ถ.ศาลาธรรมสพน์ แขวงศาลาธรรมสพน 10170");

        OnlinePmt onlinePmtKeys = new OnlinePmt();
        onlinePmtKeys.setTrnId("2022062010482618300");
        onlinePmtKeys.setMobileNumber("*********");
        onlinePmtKeys.setAmt("40.90");
        onlinePmtKeys.setEffDt("2022-06-20T10:48:26+07:00");
        onlinePmtKeys.setBankId("011");
        onlinePmtKeys.setBranchId("0001");

        OnlinePmtRec onlinePmtRec = new OnlinePmtRec();
        onlinePmtRec.setOnlinePmtInfo(onlinePmtInfo);
        onlinePmtRec.setOnlinePmtKeys(onlinePmtKeys);
        onlinePmtRec.setOcpBillPayment(ocpBillPaymentResponse);

        EteOnlinePaymentOCPRequest eteOnlinePaymentOCPRequestVerify = new EteOnlinePaymentOCPRequest();
        eteOnlinePaymentOCPRequestVerify
                .setRequestId("a962003a-6fa7-48b1-81d2-5df3329f4ff7")
                .setRequestDateTime("2022-06-20T10:48:26+07:00")
                .setBankRefId("2022062010482618300")
                .setPaymentId("2022062010482618300")
                .setAppId(REQUEST_APP_ID_OCP_GATEWAY_VALUE)
                .setChannel(OCP_REQUEST_CHANNEL)
                .setBankId(TTB_BANK_CODE_3DIGIT)
                .setBranchId(TOP_UP_BRANCH_ID)
                .setTellerId(BLANK)
                .setAmount("0.00")
                .setCurrency(TOP_UP_CURRENCY)
                .setCompCode(BILL_COMP_CODE_MEA)
                .setRef1("*********")
                .setRef2(BLANK)
                .setRef3(BLANK)
                .setRef4(BLANK);

        FromAccountRequestOCP fromAccountRequestOCP = new FromAccountRequestOCP();
        fromAccountRequestOCP
                .setAccountId("**********")
                .setAccountType(SHORT_SAVING_ACCOUNT_TYPE)
                .setFiId(BLANK);

        eteOnlinePaymentOCPRequestVerify.setFromAccount(fromAccountRequestOCP);

        Mockito.when(billPaymentCustomBillerOCPService.callOCPValidation(Mockito.any(eteOnlinePaymentOCPRequestVerify.getClass()))).thenReturn(onlinePmtRec);

        CustomerCrmProfile dailyLimit = new CustomerCrmProfile();
        Mockito.when(billPaymentValidateTransaction.fetchTransactionLimit(correlationId, crmId)).thenReturn(dailyLimit);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        Mockito.when(commonPaymentService.genTransRefBillPayRef(crmId, "1", "1", "2533"))
                .thenReturn("BILLPAY_2533_12_1_001100000000000000000000123921_ca6242f6-7b2f-4833-a5c4-98cc9d55bc7f");

        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2533");
        request.setAccountNumber("**********");
        request.setToFavoriteName("ReqToFavoriteName");
        request.setAmount("40.90");
        request.setReference1("*********");
        request.setReference2("Have value some time if scan from QR");
        request.setNote("๑๒๓๔๕๖๗๘๙๐");
        MEAValidationResponse actual = (MEAValidationResponse) billPaymentValidationMEAService.validation(request, headers);

        Assertions.assertNotNull(actual.getIsRequireConfirmPin());
        Assertions.assertNotNull(actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());
        Assertions.assertEquals("40.90", actual.getNetAmount());

        ArgumentCaptor<EteOnlinePaymentOCPRequest> eteOnlinePaymentOCPRequestArgumentCaptor = ArgumentCaptor.forClass(EteOnlinePaymentOCPRequest.class);
        Mockito.verify(billPaymentCustomBillerOCPService, Mockito.times(1))
                .callOCPValidation(eteOnlinePaymentOCPRequestArgumentCaptor.capture());

        EteOnlinePaymentOCPRequest eteOnlinePaymentOCPRequest = eteOnlinePaymentOCPRequestArgumentCaptor.getValue();
        Assertions.assertEquals(request.getAmount(), eteOnlinePaymentOCPRequest.getAmount());

        String eteOnlinePaymentOCPRequestString = TMBUtils.convertJavaObjectToString(eteOnlinePaymentOCPRequest);
        Assertions.assertTrue(eteOnlinePaymentOCPRequestString.contains("fromAccount"));
        Assertions.assertEquals("2022062010482618300", eteOnlinePaymentOCPRequest.getPaymentId());
        Assertions.assertEquals("2022062010482618300", eteOnlinePaymentOCPRequest.getBankRefId());
        Assertions.assertEquals("A0478-MB", eteOnlinePaymentOCPRequest.getAppId());
        Assertions.assertEquals("1MB", eteOnlinePaymentOCPRequest.getChannel());
        Assertions.assertEquals("011", eteOnlinePaymentOCPRequest.getBankId());
        Assertions.assertEquals("0001", eteOnlinePaymentOCPRequest.getBranchId());
        Assertions.assertEquals("", eteOnlinePaymentOCPRequest.getTellerId());
        Assertions.assertEquals("40.90", eteOnlinePaymentOCPRequest.getAmount());
        Assertions.assertEquals("THB", eteOnlinePaymentOCPRequest.getCurrency());
        Assertions.assertEquals("2533", eteOnlinePaymentOCPRequest.getCompCode());
        Assertions.assertEquals("*********", eteOnlinePaymentOCPRequest.getRef1());
        Assertions.assertEquals("", eteOnlinePaymentOCPRequest.getRef2());
        Assertions.assertEquals("", eteOnlinePaymentOCPRequest.getRef3());
        Assertions.assertEquals("", eteOnlinePaymentOCPRequest.getRef4());

        ArgumentCaptor<OnlinePmt> dataSaveToCacheCaptor = ArgumentCaptor.forClass(OnlinePmt.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.anyString(), dataSaveToCacheCaptor.capture());

        OnlinePmt actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals(request.getToFavoriteName(), actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        Assertions.assertEquals(depositAccountResp.getProductNickname(), actualDataSaveToCache.getPaymentCacheData().getFromAccountNickname());
        Assertions.assertEquals(depositAccountResp.getAccountName(), actualDataSaveToCache.getPaymentCacheData().getFromAccountName());

        PaymentCacheData paymentCacheDataExpected = new PaymentCacheData();
        MEABillPaymentDetailResponse meaBillPaymentDetailResponse = new MEABillPaymentDetailResponse();
        meaBillPaymentDetailResponse
                .setMeterNo("********")
                .setTotalInterest("1.00")
                .setDisconnectedAmount("0.00")
                .setAmount("39.90")
                .setCustomerName("นายสายัณห์ พนาศุริยสมบัติ")
                .setAddress("13/253 ม.17 ม.สวนเทพมณฑล ถ.ศาลาธรรมสพน์ แขวงศาลาธรรมสพน 10170");
        paymentCacheDataExpected.setMeaBillPaymentDetailResponse(meaBillPaymentDetailResponse);

        Assertions.assertEquals(paymentCacheDataExpected.getMeaBillPaymentDetailResponse().getMeterNo(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getMeterNo());
        Assertions.assertEquals(paymentCacheDataExpected.getMeaBillPaymentDetailResponse().getTotalInterest(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getTotalInterest());
        Assertions.assertEquals(paymentCacheDataExpected.getMeaBillPaymentDetailResponse().getDisconnectedAmount(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getDisconnectedAmount());
        Assertions.assertEquals(paymentCacheDataExpected.getMeaBillPaymentDetailResponse().getAmount(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getAmount());
        Assertions.assertEquals(paymentCacheDataExpected.getMeaBillPaymentDetailResponse().getCustomerName(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getCustomerName());
        Assertions.assertEquals(paymentCacheDataExpected.getMeaBillPaymentDetailResponse().getAddress(), actualDataSaveToCache.getPaymentCacheData().getMeaBillPaymentDetailResponse().getAddress());
        Assertions.assertEquals("20220**************", actualDataSaveToCache.getOcpBillPaymentConfirmRequest().getEpayCode());
        Assertions.assertEquals("20220**************", actualDataSaveToCache.getBillPmtInfo().getEPAYCode());

        MEAValidationResponse meaValidationResponseExpected = new MEAValidationResponse();
        meaValidationResponseExpected
                .setNetAmount("40.90")
                .setFee(new BigDecimal("0.00"))
                .setTransId("BILLPAY_2533_12_1_001100000000000000000000123921_ca6242f6-7b2f-4833-a5c4-98cc9d55bc7f")
                .setIsRequireConfirmPin(true)
                .setTopUpRef(null)
                .setTopUpAccountName(null)
                .setBillerName(null)
                .setCitizenId(null)
                .setCardEmbossingName(null)
                .setAmount(null);

        Assertions.assertEquals(meaValidationResponseExpected.getNetAmount(), actual.getNetAmount());
        Assertions.assertEquals(meaValidationResponseExpected.getFee(), actual.getFee());
        Assertions.assertEquals(meaValidationResponseExpected.getTransId(), actual.getTransId());
        Assertions.assertEquals(meaValidationResponseExpected.getIsRequireConfirmPin(), actual.getIsRequireConfirmPin());
        Assertions.assertEquals(meaValidationResponseExpected.getTopUpRef(), actual.getTopUpRef());
        Assertions.assertEquals(meaValidationResponseExpected.getTopUpAccountName(), actual.getTopUpAccountName());
        Assertions.assertEquals(meaValidationResponseExpected.getBillerName(), actual.getBillerName());
        Assertions.assertEquals(meaValidationResponseExpected.getCitizenId(), actual.getCitizenId());
        Assertions.assertEquals(meaValidationResponseExpected.getCardEmbossingName(), actual.getCardEmbossingName());
        Assertions.assertEquals(meaValidationResponseExpected.getAmount(), actual.getAmount());

        ArgumentCaptor<ActivityBillPayVerifyEvent> activityEventCapture = ArgumentCaptor.forClass(ActivityBillPayVerifyEvent.class);
        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(activityEventCapture.capture());
        Assertions.assertNull(activityEventCapture.getValue().getReference2());

    }

    @Test
    void validationMEAWhenBillerNoteShouldThrowsExceptionTest() throws TMBCommonException {
        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2533");
        request.setNote("\\๑๒๓๔๕๖๗๘๙๐\\");

        Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentValidationMEAService.validation(request, headers)
        );
    }

    @Test
    void validationMEAWhenBillerNoteShouldThrowsExceptionTest2() throws TMBCommonException {
        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2533");
        request.setNote("๑๒>๓๔๕='<๖๗๘๙๐~");

        Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentValidationMEAService.validation(request, headers)
        );
    }

    @Test
    void validationMEAWhenBillerNoteShouldThrowsExceptionTest3() throws TMBCommonException {
        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2533");
        request.setNote("๑๒๓๔๕๖๗๘๙๐");
        request.setReference1("<></>");
        Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentValidationMEAService.validation(request, headers)
        );
    }

    @Test
    void validationMEAWhenBillerNoteShouldThrowsExceptionTest4() throws TMBCommonException {
        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2533");
        request.setNote("๑๒๓๔๕๖๗๘๙๐");
        request.setReference1("123456");
        request.setReference2("123_456");
        request.setReference3("<test>");
        Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentValidationMEAService.validation(request, headers)
        );
    }

    @Test
    void validationMEAWhenBillerNoteShouldThrowsExceptionTest5() throws TMBCommonException {
        TopUpVerifyRequest request = new TopUpVerifyRequest();
        request.setBillerCompCode("2533");
        request.setNote("๑๒๓๔๕๖๗๘๙๐");
        request.setReference1("123456");
        request.setReference2("<test>");
        request.setReference3("<test>");
        Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentValidationMEAService.validation(request, headers)
        );
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen() throws TMBCommonException {
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(new VerifyTransactionResult(true, new FaceRecognizeResponse().setIsRequireFr(true).setPaymentAccuUsgAmt(BigDecimal.ZERO), new CommonAuthenResult()));
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen() throws TMBCommonException {
        CommonAuthenResult commonAuthenResult = new CommonAuthenResult()
                .setRequireCommonAuthen(true)
                .setIsForceFR(true)
                .setPinFree(true);
        VerifyTransactionResult resultWhenExecuteCommonAuthen = new VerifyTransactionResult(false, null, commonAuthenResult);
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(resultWhenExecuteCommonAuthen);
    }
}