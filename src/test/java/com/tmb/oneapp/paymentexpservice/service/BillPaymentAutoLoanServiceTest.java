package com.tmb.oneapp.paymentexpservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.paymentexpservice.client.HpExpServiceFeignClient;
import com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant;
import com.tmb.oneapp.paymentexpservice.constant.ResponseCode;
import com.tmb.oneapp.paymentexpservice.factory.Factory;
import com.tmb.oneapp.paymentexpservice.model.AccountBalance;
import com.tmb.oneapp.paymentexpservice.model.ActivityTopUpEvent;
import com.tmb.oneapp.paymentexpservice.model.AutoLoanDetail;
import com.tmb.oneapp.paymentexpservice.model.AutoLoanPaymentDetail;
import com.tmb.oneapp.paymentexpservice.model.AutoLoanTBankCustomer;
import com.tmb.oneapp.paymentexpservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.CacheResponse;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenResult;
import com.tmb.oneapp.paymentexpservice.model.CommonAuthenticationInformation;
import com.tmb.oneapp.paymentexpservice.model.CustomerKYCResponse;
import com.tmb.oneapp.paymentexpservice.model.DepositAccount;
import com.tmb.oneapp.paymentexpservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.paymentexpservice.model.LegacyAuthenticationRequest;
import com.tmb.oneapp.paymentexpservice.model.PaymentCacheData;
import com.tmb.oneapp.paymentexpservice.model.PaymentDetailResponse;
import com.tmb.oneapp.paymentexpservice.model.ScheduleConfigFrequency;
import com.tmb.oneapp.paymentexpservice.model.ScheduleConfigPhrase;
import com.tmb.oneapp.paymentexpservice.model.TopUpAccount;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpConfirmResponse;
import com.tmb.oneapp.paymentexpservice.model.TopUpETEPaymentRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpETETransaction;
import com.tmb.oneapp.paymentexpservice.model.TopUpFee;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyRequest;
import com.tmb.oneapp.paymentexpservice.model.TopUpVerifyResponse;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityAutoLoanServiceRequest;
import com.tmb.oneapp.paymentexpservice.model.activitylog.ActivityBillPayVerifyEvent;
import com.tmb.oneapp.paymentexpservice.model.commonauth.CommonAuthenWithPayloadRequest;
import com.tmb.oneapp.paymentexpservice.model.commonauth.VerifyTransactionResult;
import com.tmb.oneapp.paymentexpservice.model.deeplink.DeepLinkRequest;
import com.tmb.oneapp.paymentexpservice.model.financiallog.FinancialAutoLoan;
import com.tmb.oneapp.paymentexpservice.model.hpexpservice.K2AddServiceRequestResponse;
import com.tmb.oneapp.paymentexpservice.model.hpexpservice.K2UpdatePaymentFlagResponse;
import com.tmb.oneapp.paymentexpservice.model.notification.NotificationAutoLoan;
import com.tmb.oneapp.paymentexpservice.model.transfer.CustomerCrmProfile;
import com.tmb.oneapp.paymentexpservice.utils.CacheService;
import feign.FeignException;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.internal.matchers.apachecommons.ReflectionEquals;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static com.tmb.oneapp.paymentexpservice.constant.CommonAuthenticationConstant.COMMON_AUTH_TOP_UP_FEATURE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.CommonAuthenticationConstant.COMMON_AUTH_TOP_UP_FLOW_NAME;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.ACTIVITY_LOG_BILL_PAY_VERIFY_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.APP_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_MODULE_PIN;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_PAYMENT_TYPE_FULL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.BILL_PAYMENT_TYPE_TOTAL_INSTALLMENT_SPECIFIED;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.CHANNEL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.DEVICE_MODEL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.HP_EXP_SERVICE_ADD_SERVICE_REQUEST_ERROR_CODE;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.OS_VERSION;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.TXN_TYPE_BILL;
import static com.tmb.oneapp.paymentexpservice.constant.PaymentServiceConstant.X_CRMID;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_AUTO_LOAN_DETAIL_ENTER_PAGE;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_AUTO_LOAN_REVIEW_PAGE;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.PHRASE_BILLER_AUTO_LOAN_TITLE_ENTER_PAGE;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.SCHEDULE_CONFIG_TAB_AUTO_LOAN;
import static com.tmb.oneapp.paymentexpservice.constant.ScheduleConstant.SCHEDULE_CONFIG_TAB_ONLY_AMOUNT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;

@ExtendWith(MockitoExtension.class)
class BillPaymentAutoLoanServiceTest {
    @Mock
    TopUpETEService topUpETEService;

    @Mock
    AccountService accountService;

    @Mock
    CommonPaymentService commonPaymentService;

    @Mock
    BillPaymentValidateTransaction billPaymentValidateTransaction;

    @Mock
    LogService logService;

    @Mock
    HpExpServiceFeignClient hpExpServiceFeignClient;

    @Mock
    CacheService cacheService;

    @Mock
    OauthService oauthService;

    @Mock
    TransactionLimitService transactionLimitService;

    static ExecutorService executor;

    @Spy
    @InjectMocks
    BillPaymentAutoLoanService billPaymentAutoLoanService;

    String crmId;
    String correlationId;
    String transId;
    ActivityBillPayVerifyEvent activityBillPayVerifyEvent;
    ActivityTopUpEvent activityEvent;
    HttpHeaders headers;
    TopUpVerifyRequest topUpVerifyRequest;
    TopUpConfirmRequest confirmRequest;
    BillerTopUpDetailResponse billerDetail;
    DepositAccount depositAccount;
    PaymentCacheData paymentCacheData;
    String transactionDateTime;

    @BeforeAll
    static void init() {
        executor = Mockito.mock(ExecutorService.class);
        doAnswer(
                (InvocationOnMock invocation) -> {
                    ((Runnable) invocation.getArguments()[0]).run();
                    return null;
                }
        ).when(executor).execute(any(Runnable.class));
    }

    @BeforeEach
    void setUp() throws TMBCommonException {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        transId = "TOPUP_001100000000000000000006534675_cdb34655-f62b-4dca-b47f-6788d4341489";
        headers = new HttpHeaders();
        headers.add(X_CRMID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);
        headers.add(OS_VERSION, "");
        headers.add(CHANNEL, "");
        headers.add(APP_VERSION, "");
        headers.add(DEVICE_ID, "");
        headers.add(DEVICE_MODEL, "");

        topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("110.00");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("12345");
        topUpVerifyRequest.setBillerCompCode("5003");
        topUpVerifyRequest.setAccountNumber("**********");
        topUpVerifyRequest.setToFavoriteName("ReqToFavoriteName");

        billerDetail = Factory.createBillerDetail();

        paymentCacheData = Factory.createPaymentCacheDataWithNickName();
        paymentCacheData.setBillerResp(billerDetail);
        paymentCacheData.setFeeBillpay("0.00");
        paymentCacheData.setToFavoriteNickname("ReqToFavoriteName");

        activityBillPayVerifyEvent = new ActivityBillPayVerifyEvent(
                correlationId,
                ACTIVITY_LOG_BILL_PAY_VERIFY_ID,
                headers,
                topUpVerifyRequest,
                billerDetail
        );
        activityEvent = new ActivityTopUpEvent("", "", "");
        depositAccount = Factory.createDepositAccount();
        billPaymentAutoLoanService.setExecutor(executor);
        transactionDateTime = "*************";

        confirmRequest = new TopUpConfirmRequest();
        confirmRequest.setTransId(transId);
    }

    @Test
    void getAutoLoanPaymentDetailWhenIsOwnerAndLast2DigitsRef1Is00ShouldReturnCorrectlyTest() throws TMBCommonException, ExecutionException, InterruptedException {
        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("*********");
        Mockito.when(commonPaymentService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKYCResponse);
        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);

        TopUpETETransaction topUpETETransaction = new TopUpETETransaction();
        topUpETETransaction.setAmount(new BigDecimal("1000.00"));
        topUpETETransaction.setInstallmentAmount(new BigDecimal("100.00"));
        topUpETETransaction.setTbankCustomer(new AutoLoanTBankCustomer());
        topUpETETransaction.getTbankCustomer().setName("นาย 5069606 ทดสอบ 5069606 นามสกุล");
        topUpETETransaction.getTbankCustomer().setCustomerNumber("8900");
        topUpETETransaction.getTbankCustomer().setCustomerStatus("10");
        topUpETETransaction.setDueDate("19");
        Mockito.when(topUpETEService.billPaymentAutoLoanVerify(any())).thenReturn(topUpETETransaction);

        TopUpVerifyRequest paymentDetailRequest = new TopUpVerifyRequest();
        paymentDetailRequest.setBillerCompCode("5003");
        paymentDetailRequest.setReference1("*********00");
        paymentDetailRequest.setReference2("8900");
        PaymentDetailResponse actual = billPaymentAutoLoanService.getAutoLoanPaymentDetail(crmId, correlationId, paymentDetailRequest);

        Assertions.assertTrue(actual.isOwner());
        Assertions.assertFalse(actual.isDirectDebit());
        Assertions.assertEquals(BILL_PAYMENT_TYPE_TOTAL_INSTALLMENT_SPECIFIED, actual.getPaymentType());
        Assertions.assertNull(actual.getAmount());
        Assertions.assertEquals("นาย 5069606 ทดสอบ 5069606 นามสกุล", actual.getPaymentName());
        Assertions.assertEquals("1000.00", actual.getFull().getAmount());
        Assertions.assertFalse(actual.getFull().isEditable());
        Assertions.assertEquals("100.00", actual.getMin().getAmount());
        Assertions.assertFalse(actual.getMin().isEditable());
        Assertions.assertEquals("0.00", actual.getSpecified().getAmount());
        Assertions.assertTrue(actual.getSpecified().isEditable());
        Assertions.assertEquals(SCHEDULE_CONFIG_TAB_AUTO_LOAN, actual.getScheduleConfig().getTabsOfSchedule());
        Assertions.assertTrue(new ReflectionEquals(new ScheduleConfigPhrase(PHRASE_BILLER_AUTO_LOAN_TITLE_ENTER_PAGE, PHRASE_BILLER_AUTO_LOAN_DETAIL_ENTER_PAGE, PHRASE_BILLER_AUTO_LOAN_REVIEW_PAGE))
                .matches(actual.getScheduleConfig().getPhrase()));
        Assertions.assertTrue(new ReflectionEquals(new ScheduleConfigFrequency(true, false, true))
                .matches(actual.getScheduleConfig().getFrequency()));
        Assertions.assertEquals(10, actual.getScheduleConfig().getDueDate().length());
    }

    @Test
    void getAutoLoanPaymentDetailWhenIsOwnerAndLast2DigitsRef1Is11ShouldReturnCorrectlyTest() throws TMBCommonException, ExecutionException, InterruptedException {
        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("*********");
        Mockito.when(commonPaymentService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKYCResponse);

        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);

        TopUpETETransaction topUpETETransaction = new TopUpETETransaction();
        topUpETETransaction.setAmount(new BigDecimal("1000.00"));
        topUpETETransaction.setInstallmentAmount(new BigDecimal("100.00"));
        topUpETETransaction.setTbankCustomer(new AutoLoanTBankCustomer());
        topUpETETransaction.getTbankCustomer().setName("นาย 5069606 ทดสอบ 5069606 นามสกุล");
        topUpETETransaction.getTbankCustomer().setCustomerNumber("5069606");
        topUpETETransaction.setDueDate("19");
        Mockito.when(topUpETEService.billPaymentAutoLoanVerify(any())).thenReturn(topUpETETransaction);

        TopUpVerifyRequest paymentDetailRequest = new TopUpVerifyRequest();
        paymentDetailRequest.setBillerCompCode("5003");
        paymentDetailRequest.setReference1("*********11");
        paymentDetailRequest.setReference2("5069606");
        PaymentDetailResponse actual = billPaymentAutoLoanService.getAutoLoanPaymentDetail(crmId, correlationId, paymentDetailRequest);

        Assertions.assertTrue(actual.isOwner());
        Assertions.assertFalse(actual.isDirectDebit());
        Assertions.assertEquals(BILL_PAYMENT_TYPE_FULL, actual.getPaymentType());
        Assertions.assertEquals("นาย 5069606 ทดสอบ 5069606 นามสกุล", actual.getPaymentName());
        Assertions.assertEquals("0.00", actual.getAmount().getAmount());
        Assertions.assertTrue(actual.getAmount().isEditable());
        Assertions.assertNull(actual.getFull());
        Assertions.assertNull(actual.getMin());
        Assertions.assertNull(actual.getSpecified());
        Assertions.assertEquals(SCHEDULE_CONFIG_TAB_ONLY_AMOUNT, actual.getScheduleConfig().getTabsOfSchedule());
        Assertions.assertTrue(new ReflectionEquals(new ScheduleConfigPhrase(PHRASE_BILLER_AUTO_LOAN_TITLE_ENTER_PAGE, PHRASE_BILLER_AUTO_LOAN_DETAIL_ENTER_PAGE, PHRASE_BILLER_AUTO_LOAN_REVIEW_PAGE))
                .matches(actual.getScheduleConfig().getPhrase()));
        Assertions.assertTrue(new ReflectionEquals(new ScheduleConfigFrequency(true, false, true))
                .matches(actual.getScheduleConfig().getFrequency()));
        Assertions.assertEquals(10, actual.getScheduleConfig().getDueDate().length());
    }

    @Test
    void autoLoanPaymentVerifyWhenSuccessShouldWriteActivityLogWithStatusSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, ExecutionException, InterruptedException {
        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("*********0001");
        customerKYCResponse.setCustomerFirstNameEn("name en");
        Mockito.when(commonPaymentService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKYCResponse);

        Mockito.when(commonPaymentService.getTransactionId(Mockito.anyString(), Mockito.anyInt()))
                .thenReturn("REF0000000001");

        Mockito.when(commonPaymentService.getSequencePaymentId()).thenReturn("REF0000000001");

        TopUpETETransaction paymentTransaction = new TopUpETETransaction();
        paymentTransaction.setAmount(new BigDecimal("500.00"));
        paymentTransaction.setFee(new TopUpFee());
        paymentTransaction.setTbankCustomer(new AutoLoanTBankCustomer());
        paymentTransaction.getTbankCustomer().setName("Tbank bank");
        paymentTransaction.getTbankCustomer().setCustomerNumber("12345");
        Mockito.when(topUpETEService.billPaymentAutoLoanVerify(any()))
                .thenReturn(paymentTransaction);

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setEbMaxLimitAmtCurrent(1000.00);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(1);
        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(customerCrmProfile);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        depositAccount.setProductNickname("FromNickname");
        depositAccount.setAccountName("FromAccountName");
        TopUpVerifyResponse actual = billPaymentAutoLoanService.autoLoanPaymentVerify(
                topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertNull(actual.getTopUpAccountName());
        Assertions.assertNull(actual.getTopUpRef());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
        Assertions.assertEquals(transId, actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());

        ArgumentCaptor<TopUpETEPaymentRequest> dataSaveToCacheCaptor = ArgumentCaptor.forClass(TopUpETEPaymentRequest.class);

        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.eq(transId), dataSaveToCacheCaptor.capture());

        TopUpETEPaymentRequest actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals("ReqToFavoriteName", actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        Assertions.assertEquals("FromNickname", actualDataSaveToCache.getPaymentCacheData().getFromAccountNickname());
        Assertions.assertEquals("FromAccountName", actualDataSaveToCache.getPaymentCacheData().getFromAccountName());
    }

    @Test
    void autoLoanPaymentVerifyWhenUseCommonAuthenticationSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, ExecutionException, InterruptedException {
        mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen();

        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("*********0001");
        customerKYCResponse.setCustomerFirstNameEn("name en");
        Mockito.when(commonPaymentService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKYCResponse);

        Mockito.when(commonPaymentService.getTransactionId(Mockito.anyString(), Mockito.anyInt()))
                .thenReturn("REF0000000001");

        Mockito.when(commonPaymentService.getSequencePaymentId()).thenReturn("REF0000000001");

        TopUpETETransaction paymentTransaction = new TopUpETETransaction();
        paymentTransaction.setAmount(new BigDecimal("500.00"));
        paymentTransaction.setFee(new TopUpFee());
        paymentTransaction.setTbankCustomer(new AutoLoanTBankCustomer());
        paymentTransaction.getTbankCustomer().setName("Tbank bank");
        paymentTransaction.getTbankCustomer().setCustomerNumber("12345");
        Mockito.when(topUpETEService.billPaymentAutoLoanVerify(any()))
                .thenReturn(paymentTransaction);

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setEbMaxLimitAmtCurrent(1000.00);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(1);
        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(customerCrmProfile);

        depositAccount.setProductNickname("FromNickname");
        depositAccount.setAccountName("FromAccountName");
        TopUpVerifyResponse actual = billPaymentAutoLoanService.autoLoanPaymentVerify(
                topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());

        ArgumentCaptor<TopUpETEPaymentRequest> captor = ArgumentCaptor.forClass(TopUpETEPaymentRequest.class);
        Mockito.verify(commonPaymentService, Mockito.times(1)).saveDataToCache(Mockito.eq(transId), captor.capture());
        TopUpETEPaymentRequest actualDataSaveToCache = captor.getValue();
        Assertions.assertTrue(actualDataSaveToCache.getPaymentCacheData().isRequireCommonAuthentication());
        Assertions.assertNotNull(actualDataSaveToCache.getPaymentCacheData().getCommonAuthenticationInformation());
    }


    @Test
    void autoLoanPaymentVerifyWithDeepLinkWhenCallFromNotAutoLoanShouldSkipMethodCheckIfAmountIsNotModifiedOrThrowExceptionTest() throws TMBCommonException, SQLException, JsonProcessingException, ExecutionException, InterruptedException {
        topUpVerifyRequest.setDeepLinkTransData("AutoLoanDeepLink");
        DeepLinkRequest mockDeepLink = new DeepLinkRequest();
        mockDeepLink.setTransType("01");
        mockDeepLink.setRef1("*************");
        mockDeepLink.setAmount("500");
        mockDeepLink.setCallFrom("NotAutoLoan");
        Mockito.when(commonPaymentService.getDeepLinkRequestFromRedis(anyString())).thenReturn(mockDeepLink);

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("*********0001");
        customerKYCResponse.setCustomerFirstNameEn("name en");
        Mockito.when(commonPaymentService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKYCResponse);

        Mockito.when(commonPaymentService.getTransactionId(Mockito.anyString(), Mockito.anyInt()))
                .thenReturn("REF0000000001");

        Mockito.when(commonPaymentService.getSequencePaymentId()).thenReturn("REF0000000001");

        TopUpETETransaction paymentTransaction = new TopUpETETransaction();
        paymentTransaction.setAmount(new BigDecimal("500.00"));
        paymentTransaction.setFee(new TopUpFee());
        paymentTransaction.setTbankCustomer(new AutoLoanTBankCustomer());
        paymentTransaction.getTbankCustomer().setName("Tbank bank");
        paymentTransaction.getTbankCustomer().setCustomerNumber("12345");
        Mockito.when(topUpETEService.billPaymentAutoLoanVerify(any()))
                .thenReturn(paymentTransaction);

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setEbMaxLimitAmtCurrent(1000.00);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(1);
        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(customerCrmProfile);

        depositAccount.setProductNickname("FromNickname");
        depositAccount.setAccountName("FromAccountName");
        TopUpVerifyResponse actual = billPaymentAutoLoanService.autoLoanPaymentVerify(
                topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertNull(actual.getTopUpAccountName());
        Assertions.assertNull(actual.getTopUpRef());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
        Assertions.assertEquals(transId, actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());

        String autoLoanKeyWhenCallFromAutoLoan = crmId + "_" + mockDeepLink.getTransType() + "_" + "26-6109397_" + "_ALDX_FEE";
        Mockito.verify(cacheService, never())
                .get(autoLoanKeyWhenCallFromAutoLoan);

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());

        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.eq(transId), Mockito.any());
    }


    @Test
    void autoLoanPaymentVerifyWithDeepLinkWhenSuccessShouldWriteActivityLogWithStatusSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, ExecutionException, InterruptedException {
        String autoloanPaymentKey = crmId + "_01_26-6109397_ALDX_FEE";

        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("*********0001");
        customerKYCResponse.setCustomerFirstNameEn("name en");
        Mockito.when(commonPaymentService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKYCResponse);

        Mockito.when(commonPaymentService.getTransactionId(Mockito.anyString(), Mockito.anyInt()))
                .thenReturn("REF0000000001");

        Mockito.when(commonPaymentService.getSequencePaymentId()).thenReturn("REF0000000001");

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        topUpVerifyRequest.setDeepLinkTransData("AutoLoanDeepLink");
        DeepLinkRequest mockDeepLink = new DeepLinkRequest();
        mockDeepLink.setTransType("01");
        mockDeepLink.setRef1("*************");
        mockDeepLink.setAmount("500");
        mockDeepLink.setCallFrom("autoLoan");
        Mockito.when(commonPaymentService.getDeepLinkRequestFromRedis(anyString())).thenReturn(mockDeepLink);
        AutoLoanPaymentDetail paymentDetail = new AutoLoanPaymentDetail();
        paymentDetail.setDebtFeetotalAmt(500.00);

        TmbOneServiceResponse oneServiceResponse = new TmbOneServiceResponse<>();
        oneServiceResponse.setStatus(
                new TmbStatus(PaymentServiceConstant.STATUS_SUCCESS_CODE, PaymentServiceConstant.SUCCESS,
                        PaymentServiceConstant.SERVICE_NAME, PaymentServiceConstant.SUCCESS));
        oneServiceResponse.setData(new CacheResponse().setKey(autoloanPaymentKey).setValue(TMBUtils.convertJavaObjectToString(paymentDetail)));

        Mockito.when(cacheService.handleResponseFromRedis(any(), any(), any(), any(), any(), any(), any())).thenReturn(ResponseEntity.ok(oneServiceResponse));

        TopUpETETransaction paymentTransaction = new TopUpETETransaction();
        paymentTransaction.setAmount(new BigDecimal("500.00"));
        paymentTransaction.setFee(new TopUpFee());
        paymentTransaction.setTbankCustomer(new AutoLoanTBankCustomer());
        paymentTransaction.getTbankCustomer().setName("Tbank bank");
        paymentTransaction.getTbankCustomer().setCustomerNumber("12345");
        Mockito.when(topUpETEService.billPaymentAutoLoanVerify(any()))
                .thenReturn(paymentTransaction);

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setEbMaxLimitAmtCurrent(1000.00);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(1);
        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(customerCrmProfile);

        depositAccount.setProductNickname("FromNickname");
        depositAccount.setAccountName("FromAccountName");
        TopUpVerifyResponse actual = billPaymentAutoLoanService.autoLoanPaymentVerify(
                topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertNull(actual.getTopUpAccountName());
        Assertions.assertNull(actual.getTopUpRef());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
        Assertions.assertEquals(transId, actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());

        Mockito.verify(cacheService, Mockito.times(1))
                .handleResponseFromRedis(any(), any(), any(), eq(crmId), eq("01"), eq("26-6109397"), any());

        ArgumentCaptor<TopUpETEPaymentRequest> dataSaveToCacheCaptor = ArgumentCaptor.forClass(TopUpETEPaymentRequest.class);

        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.eq(transId), dataSaveToCacheCaptor.capture());

        TopUpETEPaymentRequest actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals("ReqToFavoriteName", actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        Assertions.assertEquals("FromNickname", actualDataSaveToCache.getPaymentCacheData().getFromAccountNickname());
        Assertions.assertEquals("FromAccountName", actualDataSaveToCache.getPaymentCacheData().getFromAccountName());
    }

    @Test
    void autoLoanPaymentVerifyWithDeepLinkShouldNotCheckAmountIfTransTypeIsNotRequired() throws TMBCommonException, SQLException, JsonProcessingException, ExecutionException, InterruptedException {
        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("*********0001");
        customerKYCResponse.setCustomerFirstNameEn("name en");
        Mockito.when(commonPaymentService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKYCResponse);

        Mockito.when(commonPaymentService.getTransactionId(Mockito.anyString(), Mockito.anyInt()))
                .thenReturn("REF0000000001");

        Mockito.when(commonPaymentService.getSequencePaymentId()).thenReturn("REF0000000001");

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        topUpVerifyRequest.setDeepLinkTransData("AutoLoanDeepLink");
        DeepLinkRequest mockDeepLink = new DeepLinkRequest();
        mockDeepLink.setTransType("00");
        mockDeepLink.setRef1("*************");
        mockDeepLink.setAmount("500");
        mockDeepLink.setCallFrom("autoLoan");
        Mockito.when(commonPaymentService.getDeepLinkRequestFromRedis(anyString())).thenReturn(mockDeepLink);

        TopUpETETransaction paymentTransaction = new TopUpETETransaction();
        paymentTransaction.setAmount(new BigDecimal("500.00"));
        paymentTransaction.setFee(new TopUpFee());
        paymentTransaction.setTbankCustomer(new AutoLoanTBankCustomer());
        paymentTransaction.getTbankCustomer().setName("Tbank bank");
        paymentTransaction.getTbankCustomer().setCustomerNumber("12345");
        Mockito.when(topUpETEService.billPaymentAutoLoanVerify(any()))
                .thenReturn(paymentTransaction);

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setEbMaxLimitAmtCurrent(1000.00);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(1);
        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(customerCrmProfile);

        depositAccount.setProductNickname("FromNickname");
        depositAccount.setAccountName("FromAccountName");
        TopUpVerifyResponse actual = billPaymentAutoLoanService.autoLoanPaymentVerify(
                topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount);

        Assertions.assertNull(actual.getTopUpAccountName());
        Assertions.assertNull(actual.getTopUpRef());
        Assertions.assertFalse(actual.getIsRequireConfirmPin());
        Assertions.assertEquals(transId, actual.getTransId());
        Assertions.assertEquals(new BigDecimal("0.00"), actual.getFee());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());

        String autoloanPaymentKey = crmId + "_00_26-6109397_ALDX_FEE";
        Mockito.verify(cacheService, Mockito.times(0))
                .get(eq(autoloanPaymentKey));

        ArgumentCaptor<TopUpETEPaymentRequest> dataSaveToCacheCaptor = ArgumentCaptor.forClass(TopUpETEPaymentRequest.class);

        Mockito.verify(commonPaymentService, Mockito.times(1))
                .saveDataToCache(Mockito.eq(transId), dataSaveToCacheCaptor.capture());

        TopUpETEPaymentRequest actualDataSaveToCache = dataSaveToCacheCaptor.getValue();
        Assertions.assertEquals("ReqToFavoriteName", actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        Assertions.assertEquals("FromNickname", actualDataSaveToCache.getPaymentCacheData().getFromAccountNickname());
        Assertions.assertEquals("FromAccountName", actualDataSaveToCache.getPaymentCacheData().getFromAccountName());
    }

    @Test
    void autoLoanPaymentVerifyWithDeepLinkAndAmountIncorrectShouldThrowTmbExceptionTest() throws TMBCommonException, SQLException, JsonProcessingException, ExecutionException, InterruptedException {
        String autoloanPaymentKey = crmId + "_01_26-6109397_ALDX_FEE";
        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("*********0001");
        customerKYCResponse.setCustomerFirstNameEn("name en");
        Mockito.when(commonPaymentService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKYCResponse);

        Mockito.when(commonPaymentService.getTransactionId(Mockito.anyString(), Mockito.anyInt()))
                .thenReturn("REF0000000001");

        Mockito.when(commonPaymentService.getSequencePaymentId()).thenReturn("REF0000000001");

        mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen();

        topUpVerifyRequest.setDeepLinkTransData("AutoLoanDeepLink");
        DeepLinkRequest mockDeepLink = new DeepLinkRequest();
        mockDeepLink.setTransType("01");
        mockDeepLink.setRef1("*************");
        mockDeepLink.setAmount("500");
        mockDeepLink.setCallFrom("autoLoan");
        Mockito.when(commonPaymentService.getDeepLinkRequestFromRedis(anyString())).thenReturn(mockDeepLink);
        AutoLoanPaymentDetail paymentDetail = new AutoLoanPaymentDetail();
        paymentDetail.setDebtFeetotalAmt(1000.00);

        TmbOneServiceResponse oneServiceResponse = new TmbOneServiceResponse<>();
        oneServiceResponse.setStatus(
                new TmbStatus(PaymentServiceConstant.STATUS_SUCCESS_CODE, PaymentServiceConstant.SUCCESS,
                        PaymentServiceConstant.SERVICE_NAME, PaymentServiceConstant.SUCCESS));
        oneServiceResponse.setData(new CacheResponse().setKey(autoloanPaymentKey).setValue(TMBUtils.convertJavaObjectToString(paymentDetail)));

        Mockito.when(cacheService.handleResponseFromRedis(any(), any(), any(), any(), any(), any(), any())).thenReturn(ResponseEntity.ok(oneServiceResponse));

        TopUpETETransaction paymentTransaction = new TopUpETETransaction();
        paymentTransaction.setAmount(new BigDecimal("500.00"));
        paymentTransaction.setFee(new TopUpFee());
        paymentTransaction.setTbankCustomer(new AutoLoanTBankCustomer());
        paymentTransaction.getTbankCustomer().setName("Tbank bank");
        paymentTransaction.getTbankCustomer().setCustomerNumber("12345");
        Mockito.when(topUpETEService.billPaymentAutoLoanVerify(any()))
                .thenReturn(paymentTransaction);

        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setEbMaxLimitAmtCurrent(1000.00);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeBpLimit(200.00);
        customerCrmProfile.setPinFreeTxnCount(1);
        Mockito.when(transactionLimitService.fetchTransactionLimit(correlationId, crmId)).thenReturn(customerCrmProfile);

        depositAccount.setProductNickname("FromNickname");
        depositAccount.setAccountName("FromAccountName");

        Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentAutoLoanService.autoLoanPaymentVerify(
                        topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount)
        );

        Mockito.verify(cacheService, Mockito.times(1))
                .handleResponseFromRedis(any(), any(), any(), eq(crmId), eq("01"), eq("26-6109397"), any());


    }

    @Test
    void autoLoanPaymentVerifyWhenThrowTMBCommonExceptionShouldWriteActivityLogWithStatusFailTest() throws TMBCommonException, ExecutionException, InterruptedException {
        Mockito.when(commonPaymentService.getCustomerKyc(crmId, correlationId)).thenThrow(TMBCommonException.class);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("110.00");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("12345");
        topUpVerifyRequest.setBillerCompCode("5003");
        topUpVerifyRequest.setAccountNumber("**********");

        Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentAutoLoanService.autoLoanPaymentVerify(
                        topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount)
        );

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());
    }

    @Test
    void autoLoanPaymentVerifyWhenThrowExceptionShouldWriteActivityLogWithStatusFailTest() throws TMBCommonException, ExecutionException, InterruptedException {
        Mockito.when(commonPaymentService.getCustomerKyc(crmId, correlationId)).thenThrow(FeignException.FeignClientException.class);

        TopUpVerifyRequest topUpVerifyRequest = new TopUpVerifyRequest();
        topUpVerifyRequest.setAmount("110.00");
        topUpVerifyRequest.setReference1("*********");
        topUpVerifyRequest.setReference2("12345");
        topUpVerifyRequest.setBillerCompCode("5003");
        topUpVerifyRequest.setAccountNumber("**********");

        Assertions.assertThrows(Exception.class, () ->
                billPaymentAutoLoanService.autoLoanPaymentVerify(
                        topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount)
        );

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());
    }

    @Test
    void autoLoanPaymentConfirmShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        TopUpETEPaymentRequest topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setAmount(new BigDecimal("100.00"));
        topUpETEPaymentRequest.setReference1("*********");
        topUpETEPaymentRequest.setReference2(null);
        topUpETEPaymentRequest.setCompCode("AL01");
        topUpETEPaymentRequest.setFromAccount(new TopUpAccount());
        topUpETEPaymentRequest.getFromAccount().setAccountId("**********");
        topUpETEPaymentRequest.setToAccount(new TopUpAccount());
        topUpETEPaymentRequest.getToAccount().setAccountId("*********00");
        topUpETEPaymentRequest.setEpayCode("REF00001");
        paymentCacheData.setRequirePin(true);
        paymentCacheData.setToFavoriteNickname("Auto Loan");
        topUpETEPaymentRequest.setPaymentCacheData(paymentCacheData);
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TopUpETEPaymentRequest.class, crmId))
                .thenReturn(topUpETEPaymentRequest);

        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(correlationId, crmId, "*********"))
                .thenReturn(true);

        TopUpETETransaction topUpETETransaction = new TopUpETETransaction();
        topUpETETransaction.setFromAccount(new TopUpAccount());
        topUpETETransaction.getFromAccount().setAccountId("*********0");
        topUpETETransaction.getFromAccount().setBalances(new AccountBalance());
        topUpETETransaction.getFromAccount().getBalances().setAvailable("1000.00");
        topUpETETransaction.setToAccount(new TopUpAccount());
        topUpETETransaction.getToAccount().setAccountId("*********0");
        topUpETETransaction.setAmount(new BigDecimal("1000"));
        topUpETEPaymentRequest.setAutoLoanDetails(new AutoLoanDetail());
        topUpETEPaymentRequest.getAutoLoanDetails().setCustomerName("คุณ ซีวายซี ทดสอบสี่");
        Mockito.when(topUpETEService.billPaymentAutoLoanConfirm(any()))
                .thenReturn(topUpETETransaction);

        Mockito.when(commonPaymentService.getCurrentDateTime()).thenReturn(transactionDateTime);

        TopUpConfirmResponse actual = billPaymentAutoLoanService.autoLoanPaymentConfirm(crmId, correlationId, confirmRequest, headers);

        Assertions.assertNotNull(actual.getReference1());
        Assertions.assertNotNull(actual.getTopUpCreatedDatetime());
        Assertions.assertEquals("1000.00", actual.getRemainingBalance());

        Mockito.verify(oauthService, times(1))
                .validateAuthentication(any(), any(), any(), anyString());

        ArgumentCaptor<FinancialAutoLoan> finLogCaptor = ArgumentCaptor.forClass(FinancialAutoLoan.class);

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), finLogCaptor.capture(), any());

        FinancialAutoLoan actualFinLog = finLogCaptor.getValue();
        Assertions.assertEquals("Auto Loan", actualFinLog.getToAccNickName());
        Assertions.assertEquals(topUpETEPaymentRequest.getPaymentCacheData().getBillerResp().getBillerInfo().getNameEn(), actualFinLog.getToAccName());
        Assertions.assertEquals(correlationId, actualFinLog.getActivityRefId());
        Assertions.assertEquals("FromNickName", actualFinLog.getFromAccNickName());
        Assertions.assertEquals("FromAccountName", actualFinLog.getFromAccName());
        Assertions.assertEquals(TXN_TYPE_BILL, actualFinLog.getTxnType());

        NotificationAutoLoan notificationAutoLoanExpected = new NotificationAutoLoan();
        notificationAutoLoanExpected.setAddDateTimeEN("18 Aug 22 - 1:37 PM");
        notificationAutoLoanExpected.setAddDateTimeTH("18 ส.ค. 65 - 13:37 น.");

        ArgumentCaptor<NotificationAutoLoan> notificationAutoLoanArgumentCaptor = ArgumentCaptor.forClass(NotificationAutoLoan.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .sendENotificationPayment(notificationAutoLoanArgumentCaptor.capture());

        Assertions.assertEquals(notificationAutoLoanExpected.getAddDateTimeTH(), notificationAutoLoanArgumentCaptor.getValue().getAddDateTimeTH());
        Assertions.assertEquals(notificationAutoLoanExpected.getAddDateTimeEN(), notificationAutoLoanArgumentCaptor.getValue().getAddDateTimeEN());
    }

    @Test
    void autoLoanPaymentConfirmWhenNotOwnerShouldSuccessAndCallValidateTransactionAndCallUpdateDailyUsageTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        TopUpETEPaymentRequest topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setAmount(new BigDecimal("100.11"));
        topUpETEPaymentRequest.setReference1("*********");
        topUpETEPaymentRequest.setFromAccount(new TopUpAccount());
        topUpETEPaymentRequest.getFromAccount().setAccountId("**********");
        topUpETEPaymentRequest.setToAccount(new TopUpAccount());
        topUpETEPaymentRequest.getToAccount().setAccountId("*********00");
        topUpETEPaymentRequest.setPaymentCacheData(new PaymentCacheData());
        topUpETEPaymentRequest.getPaymentCacheData().setBillerResp(billerDetail);
        topUpETEPaymentRequest.getPaymentCacheData().setFeeBillpay("0.00");
        topUpETEPaymentRequest.getPaymentCacheData().setRequirePin(false);
        topUpETEPaymentRequest.setEpayCode("REF00001");
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TopUpETEPaymentRequest.class, crmId))
                .thenReturn(topUpETEPaymentRequest);

        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(correlationId, crmId, "*********"))
                .thenReturn(false);

        TopUpETETransaction topUpETETransaction = new TopUpETETransaction();
        topUpETETransaction.setFromAccount(new TopUpAccount());
        topUpETETransaction.getFromAccount().setAccountId("*********0");
        topUpETETransaction.getFromAccount().setBalances(new AccountBalance());
        topUpETETransaction.getFromAccount().getBalances().setAvailable("1000.00");
        topUpETETransaction.setToAccount(new TopUpAccount());
        topUpETETransaction.getToAccount().setAccountId("*********0");
        topUpETETransaction.setAmount(new BigDecimal("1000"));
        topUpETEPaymentRequest.setAutoLoanDetails(new AutoLoanDetail());
        topUpETEPaymentRequest.getAutoLoanDetails().setCustomerName("คุณ ซีวายซี ทดสอบสี่");
        Mockito.when(topUpETEService.billPaymentAutoLoanConfirm(any()))
                .thenReturn(topUpETETransaction);

        Mockito.when(commonPaymentService.getCurrentDateTime()).thenReturn(transactionDateTime);

        TopUpConfirmResponse actual = billPaymentAutoLoanService.autoLoanPaymentConfirm(crmId, correlationId, confirmRequest, headers);

        Assertions.assertNotNull(actual.getReference1());
        Assertions.assertNotNull(actual.getTopUpCreatedDatetime());
        Assertions.assertEquals("1000.00", actual.getRemainingBalance());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(eq(correlationId), any(), any());

        Mockito.verify(billPaymentValidateTransaction, Mockito.times(1))
                .updateBillDailyUsage(eq(crmId), eq(100.11), any(), eq(false), eq(correlationId));

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(any());
    }

    @Test
    void autoLoanPaymentConfirmWhenThrowTMBCommonExceptionShouldSaveActivityLogTest() throws TMBCommonException, JsonProcessingException {
        TopUpETEPaymentRequest topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setAmount(new BigDecimal("100.11"));
        topUpETEPaymentRequest.setReference1("*********");
        topUpETEPaymentRequest.setFromAccount(new TopUpAccount());
        topUpETEPaymentRequest.getFromAccount().setAccountId("*********0");
        topUpETEPaymentRequest.setToAccount(new TopUpAccount());
        topUpETEPaymentRequest.getToAccount().setAccountId("*********00");
        topUpETEPaymentRequest.setPaymentCacheData(new PaymentCacheData());
        topUpETEPaymentRequest.getPaymentCacheData().setBillerResp(billerDetail);
        topUpETEPaymentRequest.getPaymentCacheData().setFeeBillpay("0.00");
        topUpETEPaymentRequest.getPaymentCacheData().setRequirePin(true);

        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TopUpETEPaymentRequest.class, crmId))
                .thenReturn(topUpETEPaymentRequest);

        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(correlationId, crmId, "*********"))
                .thenReturn(false);

        Mockito.when(topUpETEService.billPaymentAutoLoanConfirm(any()))
                .thenThrow(TMBCommonException.class);

        Assertions.assertThrows(TMBCommonException.class, () ->
                billPaymentAutoLoanService.autoLoanPaymentConfirm(crmId, correlationId, confirmRequest, headers)
        );

        Mockito.verify(oauthService, times(1))
                .validateAuthentication(any(), any(), any(), anyString());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(any());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(anyString(), any(), any());
    }

    @Test
    void autoLoanPaymentConfirmWhenThrowExceptionShouldSaveActivityLogTest() throws TMBCommonException, JsonProcessingException {
        TopUpETEPaymentRequest topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setAmount(new BigDecimal("100.11"));
        topUpETEPaymentRequest.setReference1("*********");
        topUpETEPaymentRequest.setFromAccount(new TopUpAccount());
        topUpETEPaymentRequest.getFromAccount().setAccountId("*********0");
        topUpETEPaymentRequest.setToAccount(new TopUpAccount());
        topUpETEPaymentRequest.getToAccount().setAccountId("*********00");
        topUpETEPaymentRequest.setPaymentCacheData(new PaymentCacheData());
        topUpETEPaymentRequest.getPaymentCacheData().setBillerResp(billerDetail);
        topUpETEPaymentRequest.getPaymentCacheData().setFeeBillpay("0.00");
        topUpETEPaymentRequest.getPaymentCacheData().setRequirePin(false);
        topUpETEPaymentRequest.setEpayCode("REF00001");
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TopUpETEPaymentRequest.class, crmId))
                .thenReturn(topUpETEPaymentRequest);

        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(correlationId, crmId, "*********"))
                .thenReturn(false);

        Mockito.when(topUpETEService.billPaymentAutoLoanConfirm(any()))
                .thenThrow(FeignException.class);


        Assertions.assertThrows(Exception.class, () ->
                billPaymentAutoLoanService.autoLoanPaymentConfirm(crmId, correlationId, confirmRequest, headers)
        );

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(any());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogTransactionEvent(anyString(), any(), any());
    }


    @Test
    void autoLoanPaymentConfirmWithCallingHpExpServiceShouldSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        TopUpAccount mockFromAccount = new TopUpAccount();
        mockFromAccount.setAccountId("***************");
        mockFromAccount.setAccountType("SDC");
        mockFromAccount.setTitle("Saving");
        AccountBalance mockBalance = new AccountBalance();
        mockBalance.setAvailable("1000.00");
        mockBalance.setLedger("abc");
        mockFromAccount.setBalances(mockBalance);

        TopUpAccount mockToAccount = new TopUpAccount();
        mockToAccount.setAccountId("***************");
        mockToAccount.setAccountId("234");


        TopUpETEPaymentRequest topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setAmount(new BigDecimal("100.00"));
        topUpETEPaymentRequest.setReference1("*********");
        topUpETEPaymentRequest.setFromAccount(mockFromAccount);
        topUpETEPaymentRequest.setToAccount(mockToAccount);

        topUpETEPaymentRequest.setEpayCode("REF00001");
        paymentCacheData.setRequirePin(false);
        topUpETEPaymentRequest.setPaymentCacheData(paymentCacheData);
        topUpETEPaymentRequest.setAutoLoanDetails(new AutoLoanDetail());
        topUpETEPaymentRequest.getAutoLoanDetails().setCustomerName("คุณ ซีวายซี ทดสอบสี่");

        DeepLinkRequest deepLink = new DeepLinkRequest();
        deepLink.setTransId("AUTO_LOAN");
        deepLink.setAmount("500.00");
        deepLink.setTransType("02");
        deepLink.setCallFrom("autoLoan");
        topUpETEPaymentRequest.getPaymentCacheData().setDeepLinkRequest(deepLink);
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TopUpETEPaymentRequest.class, crmId))
                .thenReturn(topUpETEPaymentRequest);

        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(correlationId, crmId, "*********"))
                .thenReturn(true);

        K2AddServiceRequestResponse k2AddServiceRequestResponse = new K2AddServiceRequestResponse();
        k2AddServiceRequestResponse.setSrId("abc");
        k2AddServiceRequestResponse.setSrNo("123");
        k2AddServiceRequestResponse.setCode(AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE);
        TmbOneServiceResponse<K2AddServiceRequestResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        TmbStatus tmbStatus = new TmbStatus();
        tmbStatus.setCode(ResponseCode.SUCCESS.getCode());
        tmbOneServiceResponse.setStatus(tmbStatus);
        tmbOneServiceResponse.setData(k2AddServiceRequestResponse);
        Mockito.when(hpExpServiceFeignClient.addServiceRequest(any(), any())).thenReturn(tmbOneServiceResponse);


        TmbOneServiceResponse<K2UpdatePaymentFlagResponse> mockUpdatePaymentFlagResponse = new TmbOneServiceResponse<>();
        mockUpdatePaymentFlagResponse.setStatus(tmbStatus);
        K2UpdatePaymentFlagResponse mockUpdatePaymentFlagOneAppResponseData = new K2UpdatePaymentFlagResponse();
        mockUpdatePaymentFlagOneAppResponseData.setCode(AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE);
        mockUpdatePaymentFlagResponse.setData(mockUpdatePaymentFlagOneAppResponseData);
        Mockito.when(hpExpServiceFeignClient.updatePaymentFlag(any(), any())).thenReturn(mockUpdatePaymentFlagResponse);

        TopUpETETransaction topUpETETransaction = new TopUpETETransaction();
        topUpETETransaction.setFromAccount(mockFromAccount);
        topUpETETransaction.setToAccount(mockFromAccount);
        topUpETETransaction.setAmount(new BigDecimal("1000"));
        Mockito.when(topUpETEService.billPaymentAutoLoanConfirm(any()))
                .thenReturn(topUpETETransaction);

        Mockito.when(commonPaymentService.getCurrentDateTime()).thenReturn(transactionDateTime);

        TopUpConfirmResponse actual = billPaymentAutoLoanService.autoLoanPaymentConfirm(crmId, correlationId, confirmRequest, headers);

        Assertions.assertNotNull(actual.getReference1());
        Assertions.assertNotNull(actual.getTopUpCreatedDatetime());
        Assertions.assertEquals("1000.00", actual.getRemainingBalance());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(eq(correlationId), Mockito.any(), Mockito.any());

        Mockito.verify(hpExpServiceFeignClient, Mockito.times(1))
                .addServiceRequest(any(), any());

        Mockito.verify(hpExpServiceFeignClient, Mockito.times(1))
                .updatePaymentFlag(any(), any());
    }

    @Test
    void autoLoanPaymentConfirmWithCallingHpExpServiceAddServiceRequestSuccessPayETEFailUpdatePaymentShouldBeCalled() throws TMBCommonException, JsonProcessingException {
        TopUpETEPaymentRequest topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setAmount(new BigDecimal("100.11"));
        topUpETEPaymentRequest.setReference1("*********");
        topUpETEPaymentRequest.setFromAccount(new TopUpAccount());
        topUpETEPaymentRequest.getFromAccount().setAccountId("*********0");
        topUpETEPaymentRequest.setToAccount(new TopUpAccount());
        topUpETEPaymentRequest.getToAccount().setAccountId("*********00");
        topUpETEPaymentRequest.setEpayCode("REF00001");
        DeepLinkRequest deepLink = new DeepLinkRequest();
        deepLink.setTransId("AUTO_LOAN");
        deepLink.setAmount("100.00");
        deepLink.setCallFrom("autoLoan");
        paymentCacheData.setRequirePin(false);
        topUpETEPaymentRequest.setPaymentCacheData(paymentCacheData);
        topUpETEPaymentRequest.getPaymentCacheData().setDeepLinkRequest(deepLink);
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TopUpETEPaymentRequest.class, crmId))
                .thenReturn(topUpETEPaymentRequest);

        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(correlationId, crmId, "*********"))
                .thenReturn(false);

        Mockito.when(topUpETEService.billPaymentAutoLoanConfirm(any()))
                .thenThrow(TMBCommonException.class);

        K2AddServiceRequestResponse k2AddServiceRequestResponse = new K2AddServiceRequestResponse();
        k2AddServiceRequestResponse.setSrId("abc");
        k2AddServiceRequestResponse.setSrNo("123");
        k2AddServiceRequestResponse.setCode(AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE);
        TmbOneServiceResponse<K2AddServiceRequestResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        TmbStatus tmbStatus = new TmbStatus();
        tmbStatus.setCode(ResponseCode.SUCCESS.getCode());
        tmbOneServiceResponse.setStatus(tmbStatus);
        tmbOneServiceResponse.setData(k2AddServiceRequestResponse);

        Mockito.when(hpExpServiceFeignClient.addServiceRequest(any(), any())).thenReturn(tmbOneServiceResponse);

        TmbOneServiceResponse<K2UpdatePaymentFlagResponse> mockUpdatePaymentFlagResponse = new TmbOneServiceResponse<>();
        mockUpdatePaymentFlagResponse.setStatus(tmbStatus);
        K2UpdatePaymentFlagResponse mockUpdatePaymentFlagOneAppResponseData = new K2UpdatePaymentFlagResponse();
        mockUpdatePaymentFlagOneAppResponseData.setCode(AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE);
        mockUpdatePaymentFlagResponse.setData(mockUpdatePaymentFlagOneAppResponseData);
        Mockito.when(hpExpServiceFeignClient.updatePaymentFlag(any(), any())).thenReturn(mockUpdatePaymentFlagResponse);

        Assertions.assertThrows(Exception.class, () ->
                billPaymentAutoLoanService.autoLoanPaymentConfirm(crmId, correlationId, confirmRequest, headers)
        );

        Mockito.verify(hpExpServiceFeignClient, Mockito.times(1))
                .addServiceRequest(any(), any());

        Mockito.verify(hpExpServiceFeignClient, Mockito.times(1))
                .updatePaymentFlag(any(), any());

    }

    @Test
    void autoLoanPaymentConfirmIfDeepLinkIsInstallmentShouldSuccessAndNotCallingHpServiceTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        TopUpETEPaymentRequest topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setAmount(new BigDecimal("100.00"));
        topUpETEPaymentRequest.setReference1("*********");
        topUpETEPaymentRequest.setFromAccount(new TopUpAccount());
        topUpETEPaymentRequest.getFromAccount().setAccountId("**********");
        topUpETEPaymentRequest.setToAccount(new TopUpAccount());
        topUpETEPaymentRequest.getToAccount().setAccountId("*********00");
        paymentCacheData.setRequirePin(false);
        topUpETEPaymentRequest.setPaymentCacheData(paymentCacheData);
        topUpETEPaymentRequest.setEpayCode("REF00001");
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TopUpETEPaymentRequest.class, crmId))
                .thenReturn(topUpETEPaymentRequest);

        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(correlationId, crmId, "*********"))
                .thenReturn(true);

        TopUpETETransaction topUpETETransaction = new TopUpETETransaction();
        topUpETETransaction.setFromAccount(new TopUpAccount());
        topUpETETransaction.getFromAccount().setAccountId("*********0");
        topUpETETransaction.getFromAccount().setBalances(new AccountBalance());
        topUpETETransaction.getFromAccount().getBalances().setAvailable("1000.00");
        topUpETETransaction.setToAccount(new TopUpAccount());
        topUpETETransaction.getToAccount().setAccountId("*********0");
        topUpETETransaction.setAmount(new BigDecimal("1000"));
        topUpETEPaymentRequest.setAutoLoanDetails(new AutoLoanDetail());
        topUpETEPaymentRequest.getAutoLoanDetails().setCustomerName("คุณ ซีวายซี ทดสอบสี่");
        Mockito.when(topUpETEService.billPaymentAutoLoanConfirm(any()))
                .thenReturn(topUpETETransaction);


        Mockito.when(commonPaymentService.getCurrentDateTime()).thenReturn(transactionDateTime);

        TopUpConfirmResponse actual = billPaymentAutoLoanService.autoLoanPaymentConfirm(crmId, correlationId, confirmRequest, headers);

        Assertions.assertNotNull(actual.getReference1());
        Assertions.assertNotNull(actual.getTopUpCreatedDatetime());
        Assertions.assertEquals("1000.00", actual.getRemainingBalance());

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(eq(correlationId), any(), any());

        Mockito.verify(hpExpServiceFeignClient, Mockito.times(0))
                .addServiceRequest(any(), any());

        Mockito.verify(hpExpServiceFeignClient, Mockito.times(0))
                .updatePaymentFlag(any(), any());
    }

    @Test
    void autoLoanPaymentConfirmWithCallingHpExpServiceAddServiceRequestServiceUnavailableShouldLogActivity() throws JsonProcessingException, TMBCommonException {
        TopUpAccount mockFromAccount = new TopUpAccount();
        mockFromAccount.setAccountId("***************");
        mockFromAccount.setAccountType("SDC");
        mockFromAccount.setTitle("Saving");
        AccountBalance mockBalance = new AccountBalance();
        mockBalance.setAvailable("1000.00");
        mockBalance.setLedger("abc");
        mockFromAccount.setBalances(mockBalance);

        TopUpAccount mockToAccount = new TopUpAccount();
        mockToAccount.setAccountId("***************");
        mockToAccount.setAccountId("234");

        TopUpETEPaymentRequest topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setAmount(new BigDecimal("100.00"));
        topUpETEPaymentRequest.setReference1("*********");
        topUpETEPaymentRequest.setFromAccount(mockFromAccount);
        topUpETEPaymentRequest.setToAccount(mockToAccount);
        topUpETEPaymentRequest.getToAccount().setAccountId("*********00");
        paymentCacheData.setRequirePin(false);
        topUpETEPaymentRequest.setPaymentCacheData(paymentCacheData);

        DeepLinkRequest deepLink = new DeepLinkRequest();
        deepLink.setTransId("AUTO_LOAN");
        deepLink.setAmount("100.00");
        deepLink.setTransType("01");
        deepLink.setCallFrom("autoLoan");
        topUpETEPaymentRequest.getPaymentCacheData().setDeepLinkRequest(deepLink);
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TopUpETEPaymentRequest.class, crmId))
                .thenReturn(topUpETEPaymentRequest);

        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(correlationId, crmId, "*********"))
                .thenReturn(true);

        K2AddServiceRequestResponse k2AddServiceRequestResponse = new K2AddServiceRequestResponse();
        k2AddServiceRequestResponse.setSrId("abc");
        k2AddServiceRequestResponse.setSrNo("123");
        TmbOneServiceResponse<K2AddServiceRequestResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        TmbStatus tmbStatus = new TmbStatus();
        tmbStatus.setCode(HP_EXP_SERVICE_ADD_SERVICE_REQUEST_ERROR_CODE);
        tmbOneServiceResponse.setStatus(tmbStatus);
        tmbOneServiceResponse.setData(k2AddServiceRequestResponse);
        Mockito.when(hpExpServiceFeignClient.addServiceRequest(any(), any())).thenThrow(FeignException.ServiceUnavailable.class);

        List<String> accLogTracker = new ArrayList<>();
        doAnswer(invocation -> {
                    Object[] args = invocation.getArguments();
                    ActivityAutoLoanServiceRequest request = (ActivityAutoLoanServiceRequest) args[0];
                    accLogTracker.add(request.getActivityTypeId());
                    return null;
                }

        ).when(logService).saveLogActivityBillPayEvent(any(ActivityAutoLoanServiceRequest.class));
        Assertions.assertThrows(FeignException.ServiceUnavailable.class, () ->
                billPaymentAutoLoanService.autoLoanPaymentConfirm(crmId, correlationId, confirmRequest, headers)
        );
        List<String> actIdThatShouldBeLogged = accLogTracker.stream().filter(s -> s.equals("*********")).collect(Collectors.toList());
        Assertions.assertEquals(1, actIdThatShouldBeLogged.size());

    }

    @Test
    void autoLoanPaymentConfirmWithCallingHpExpServiceAddServiceRequestBadRequestShouldLogActivity() throws JsonProcessingException, TMBCommonException {
        TopUpAccount mockFromAccount = new TopUpAccount();
        mockFromAccount.setAccountId("***************");
        mockFromAccount.setAccountType("SDC");
        mockFromAccount.setTitle("Saving");
        AccountBalance mockBalance = new AccountBalance();
        mockBalance.setAvailable("1000.00");
        mockBalance.setLedger("abc");
        mockFromAccount.setBalances(mockBalance);

        TopUpAccount mockToAccount = new TopUpAccount();
        mockToAccount.setAccountId("***************");
        mockToAccount.setAccountId("234");

        TopUpETEPaymentRequest topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setAmount(new BigDecimal("100.00"));
        topUpETEPaymentRequest.setReference1("*********");
        topUpETEPaymentRequest.setFromAccount(mockFromAccount);
        topUpETEPaymentRequest.setToAccount(mockToAccount);
        topUpETEPaymentRequest.getToAccount().setAccountId("*********00");
        paymentCacheData.setRequirePin(false);
        topUpETEPaymentRequest.setPaymentCacheData(paymentCacheData);

        DeepLinkRequest deepLink = new DeepLinkRequest();
        deepLink.setTransId("AUTO_LOAN");
        deepLink.setAmount("100.00");
        deepLink.setTransType("01");
        deepLink.setCallFrom("autoLoan");
        topUpETEPaymentRequest.getPaymentCacheData().setDeepLinkRequest(deepLink);
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TopUpETEPaymentRequest.class, crmId))
                .thenReturn(topUpETEPaymentRequest);

        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(correlationId, crmId, "*********"))
                .thenReturn(true);

        K2AddServiceRequestResponse k2AddServiceRequestResponse = new K2AddServiceRequestResponse();
        k2AddServiceRequestResponse.setSrId("abc");
        k2AddServiceRequestResponse.setSrNo("123");
        TmbOneServiceResponse<K2AddServiceRequestResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        TmbStatus tmbStatus = new TmbStatus();
        tmbStatus.setCode(HP_EXP_SERVICE_ADD_SERVICE_REQUEST_ERROR_CODE);
        tmbOneServiceResponse.setStatus(tmbStatus);
        tmbOneServiceResponse.setData(k2AddServiceRequestResponse);
        Mockito.when(hpExpServiceFeignClient.addServiceRequest(any(), any())).thenThrow(FeignException.BadRequest.class);

        List<String> accLogTracker = new ArrayList<>();
        doAnswer(invocation -> {
                    Object[] args = invocation.getArguments();
                    ActivityAutoLoanServiceRequest request = (ActivityAutoLoanServiceRequest) args[0];
                    accLogTracker.add(request.getActivityTypeId());
                    return null;
                }

        ).when(logService).saveLogActivityBillPayEvent(any(ActivityAutoLoanServiceRequest.class));
        Assertions.assertThrows(FeignException.BadRequest.class, () ->
                billPaymentAutoLoanService.autoLoanPaymentConfirm(crmId, correlationId, confirmRequest, headers)
        );
        List<String> actIdThatShouldBeLogged = accLogTracker.stream().filter(s -> s.equals("*********")).collect(Collectors.toList());
        Assertions.assertEquals(1, actIdThatShouldBeLogged.size());

    }

    @Test
    void autoLoanPaymentConfirmWithCallingHpExpServiceEteFailedShouldCallUpdatePayment() throws TMBCommonException, JsonProcessingException {
        TopUpAccount mockFromAccount = new TopUpAccount();
        mockFromAccount.setAccountId("***************");
        mockFromAccount.setAccountType("SDC");
        mockFromAccount.setTitle("Saving");
        AccountBalance mockBalance = new AccountBalance();
        mockBalance.setAvailable("1000.00");
        mockBalance.setLedger("abc");
        mockFromAccount.setBalances(mockBalance);

        TopUpAccount mockToAccount = new TopUpAccount();
        mockToAccount.setAccountId("***************");
        mockToAccount.setAccountId("234");


        TopUpETEPaymentRequest topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setAmount(new BigDecimal("100.00"));
        topUpETEPaymentRequest.setReference1("*********");
        topUpETEPaymentRequest.setFromAccount(mockFromAccount);
        topUpETEPaymentRequest.setToAccount(mockToAccount);

        topUpETEPaymentRequest.setEpayCode("REF00001");
        paymentCacheData.setRequirePin(false);
        topUpETEPaymentRequest.setPaymentCacheData(paymentCacheData);

        DeepLinkRequest deepLink = new DeepLinkRequest();
        deepLink.setTransId("AUTO_LOAN");
        deepLink.setAmount("500.00");
        deepLink.setTransType("02");
        deepLink.setCallFrom("autoLoan");
        topUpETEPaymentRequest.getPaymentCacheData().setDeepLinkRequest(deepLink);
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TopUpETEPaymentRequest.class, crmId))
                .thenReturn(topUpETEPaymentRequest);

        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(correlationId, crmId, "*********"))
                .thenReturn(true);

        K2AddServiceRequestResponse k2AddServiceRequestResponse = new K2AddServiceRequestResponse();
        k2AddServiceRequestResponse.setSrId("abc");
        k2AddServiceRequestResponse.setSrNo("123");
        k2AddServiceRequestResponse.setCode(AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE);
        TmbOneServiceResponse<K2AddServiceRequestResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        TmbStatus tmbStatus = new TmbStatus();
        tmbStatus.setCode(ResponseCode.SUCCESS.getCode());
        tmbOneServiceResponse.setStatus(tmbStatus);
        tmbOneServiceResponse.setData(k2AddServiceRequestResponse);
        Mockito.when(hpExpServiceFeignClient.addServiceRequest(any(), any())).thenReturn(tmbOneServiceResponse);


        TmbOneServiceResponse<K2UpdatePaymentFlagResponse> mockUpdatePaymentFlagResponse = new TmbOneServiceResponse<>();
        mockUpdatePaymentFlagResponse.setStatus(tmbStatus);
        K2UpdatePaymentFlagResponse mockUpdatePaymentFlagOneAppResponseData = new K2UpdatePaymentFlagResponse();
        mockUpdatePaymentFlagOneAppResponseData.setCode(AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE);
        mockUpdatePaymentFlagResponse.setData(mockUpdatePaymentFlagOneAppResponseData);
        Mockito.when(hpExpServiceFeignClient.updatePaymentFlag(any(), any())).thenReturn(mockUpdatePaymentFlagResponse);

        TopUpETETransaction topUpETETransaction = new TopUpETETransaction();
        topUpETETransaction.setFromAccount(mockFromAccount);
        topUpETETransaction.setToAccount(mockFromAccount);
        topUpETETransaction.setAmount(new BigDecimal("1000"));
        Mockito.when(topUpETEService.billPaymentAutoLoanConfirm(any()))
                .thenThrow(NullPointerException.class);
        List<ActivityAutoLoanServiceRequest> accLogTracker = new ArrayList<>();
        doAnswer(invocation -> {
                    Object[] args = invocation.getArguments();
                    Object obj = args[0];
                    if (obj instanceof ActivityAutoLoanServiceRequest) {
                        ActivityAutoLoanServiceRequest request = (ActivityAutoLoanServiceRequest) args[0];
                        accLogTracker.add(request);
                    }

                    return null;
                }

        ).when(logService).saveLogActivityBillPayEvent(any());

        Assertions.assertThrows(NullPointerException.class, () ->
                billPaymentAutoLoanService.autoLoanPaymentConfirm(crmId, correlationId, confirmRequest, headers)
        );

        Mockito.verify(hpExpServiceFeignClient, Mockito.times(1))
                .addServiceRequest(any(), any());

        Mockito.verify(hpExpServiceFeignClient, Mockito.times(1))
                .updatePaymentFlag(any(), any());

        ActivityAutoLoanServiceRequest updatePayment = accLogTracker.get(0);
        Assertions.assertTrue(updatePayment.getFailReason().contains("Failed payment from ETE"));

    }

    @Test
    void autoLoanPaymentConfirmIsCreditCardShouldNotUpdateDailyUsageSuccessTest() throws TMBCommonException, SQLException, JsonProcessingException, TMBCustomCommonExceptionWithResponse {
        TopUpETEPaymentRequest topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setAmount(new BigDecimal("100.00"));
        topUpETEPaymentRequest.setReference1("*********");
        topUpETEPaymentRequest.setReference2(null);
        topUpETEPaymentRequest.setCompCode("AL01");
        topUpETEPaymentRequest.setFromAccount(new TopUpAccount());
        topUpETEPaymentRequest.getFromAccount().setAccountId("**********");
        topUpETEPaymentRequest.setToAccount(new TopUpAccount());
        topUpETEPaymentRequest.getToAccount().setAccountId("*********00");
        topUpETEPaymentRequest.setEpayCode("REF00001");
        paymentCacheData.setToFavoriteNickname("Auto Loan");
        topUpETEPaymentRequest.setPaymentCacheData(paymentCacheData);
        topUpETEPaymentRequest.getPaymentCacheData().setIsCreditCard(true);
        topUpETEPaymentRequest.getPaymentCacheData().setCardNumber("*********0123456");
        topUpETEPaymentRequest.getPaymentCacheData().setRequirePin(false);
        Mockito.when(commonPaymentService.getDataFromCacheAndValidateDuplicateTransaction(transId, TopUpETEPaymentRequest.class, crmId))
                .thenReturn(topUpETEPaymentRequest);

        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(correlationId, crmId, "*********"))
                .thenReturn(false);

        TopUpETETransaction topUpETETransaction = new TopUpETETransaction();
        topUpETETransaction.setFromAccount(new TopUpAccount());
        topUpETETransaction.getFromAccount().setAccountId("*********0");
        topUpETETransaction.getFromAccount().setBalances(new AccountBalance());
        topUpETETransaction.getFromAccount().getBalances().setAvailable("1000.00");
        topUpETETransaction.setToAccount(new TopUpAccount());
        topUpETETransaction.getToAccount().setAccountId("*********0");
        topUpETETransaction.setAmount(new BigDecimal("1000"));
        topUpETEPaymentRequest.setAutoLoanDetails(new AutoLoanDetail());
        topUpETEPaymentRequest.getAutoLoanDetails().setCustomerName("คุณ ซีวายซี ทดสอบสี่");
        Mockito.when(topUpETEService.billPaymentAutoLoanConfirm(any()))
                .thenReturn(topUpETETransaction);

        Mockito.when(commonPaymentService.getCurrentDateTime()).thenReturn(transactionDateTime);

        TopUpConfirmResponse actual = billPaymentAutoLoanService.autoLoanPaymentConfirm(crmId, correlationId, confirmRequest, headers);

        Assertions.assertNotNull(actual.getReference1());
        Assertions.assertNotNull(actual.getTopUpCreatedDatetime());
        Assertions.assertEquals("1000.00", actual.getRemainingBalance());

        Mockito.verify(transactionLimitService, never())
                .validateDailyLimitExceeded(any(), anyDouble(), anyBoolean(), anyString());

        Mockito.verify(billPaymentValidateTransaction, never())
                .updateBillDailyUsage(anyString(), any(), any(), anyBoolean(), anyString());

        ArgumentCaptor<FinancialAutoLoan> finLogCaptor = ArgumentCaptor.forClass(FinancialAutoLoan.class);

        Mockito.verify(logService, Mockito.times(1))
                .saveLogFinancialAndTransactionEvent(Mockito.eq(correlationId), finLogCaptor.capture(), any());

        FinancialAutoLoan actualFinLog = finLogCaptor.getValue();
        Assertions.assertEquals("Auto Loan", actualFinLog.getToAccNickName());
        Assertions.assertEquals(topUpETEPaymentRequest.getPaymentCacheData().getBillerResp().getBillerInfo().getNameEn(), actualFinLog.getToAccName());
        Assertions.assertEquals(correlationId, actualFinLog.getActivityRefId());
        Assertions.assertEquals("FromNickName", actualFinLog.getFromAccNickName());
        Assertions.assertEquals("FromAccountName", actualFinLog.getFromAccName());
        Assertions.assertEquals(TXN_TYPE_BILL, actualFinLog.getTxnType());

        NotificationAutoLoan notificationAutoLoanExpected = new NotificationAutoLoan();
        notificationAutoLoanExpected.setAddDateTimeEN("18 Aug 22 - 1:37 PM");
        notificationAutoLoanExpected.setAddDateTimeTH("18 ส.ค. 65 - 13:37 น.");

        ArgumentCaptor<NotificationAutoLoan> notificationAutoLoanArgumentCaptor = ArgumentCaptor.forClass(NotificationAutoLoan.class);
        Mockito.verify(commonPaymentService, Mockito.times(1))
                .sendENotificationPayment(notificationAutoLoanArgumentCaptor.capture());

        Assertions.assertEquals(notificationAutoLoanExpected.getAddDateTimeTH(), notificationAutoLoanArgumentCaptor.getValue().getAddDateTimeTH());
        Assertions.assertEquals(notificationAutoLoanExpected.getAddDateTimeEN(), notificationAutoLoanArgumentCaptor.getValue().getAddDateTimeEN());
    }

    @Test
    void getAutoLoanPaymentDetailWhenLast2DigitsRef1Is00ButRef2MissMatchShouldThrowsTMBCommonExceptionALError() throws TMBCommonException, ExecutionException, InterruptedException {
        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("*********");
        Mockito.when(commonPaymentService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKYCResponse);
        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);

        TopUpETETransaction topUpETETransaction = new TopUpETETransaction();
        topUpETETransaction.setAmount(new BigDecimal("1000.00"));
        topUpETETransaction.setInstallmentAmount(new BigDecimal("100.00"));
        topUpETETransaction.setTbankCustomer(new AutoLoanTBankCustomer());
        topUpETETransaction.getTbankCustomer().setName("นาย 5069606 ทดสอบ 5069606 นามสกุล");
        topUpETETransaction.getTbankCustomer().setCustomerNumber("9800");
        topUpETETransaction.setDueDate("19");
        TopUpETETransaction topUpETETransaction2 = new TopUpETETransaction();
        topUpETETransaction2.setAmount(new BigDecimal("1000.00"));
        topUpETETransaction2.setInstallmentAmount(new BigDecimal("100.00"));
        topUpETETransaction2.setDueDate("19");
        Mockito.when(topUpETEService.billPaymentAutoLoanVerify(any())).thenReturn(topUpETETransaction).thenReturn(topUpETETransaction2);

        TopUpVerifyRequest paymentDetailRequest = new TopUpVerifyRequest();
        paymentDetailRequest.setBillerCompCode("5003");
        paymentDetailRequest.setReference1("*********00");
        paymentDetailRequest.setReference2("8900");
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> billPaymentAutoLoanService.getAutoLoanPaymentDetail(crmId, correlationId, paymentDetailRequest));
        Assertions.assertTrue(StringUtils.equals(exception.getErrorCode(), ResponseCode.AL_REF2_MISS_MATCH.getCode()));
        Assertions.assertTrue(StringUtils.equals(exception.getErrorMessage(), ResponseCode.AL_REF2_MISS_MATCH.getMessage()));
        TMBCommonException exception2 = Assertions.assertThrows(TMBCommonException.class, () -> billPaymentAutoLoanService.getAutoLoanPaymentDetail(crmId, correlationId, paymentDetailRequest));
        Assertions.assertTrue(StringUtils.equals(exception2.getErrorCode(), ResponseCode.AL_REF2_MISS_MATCH.getCode()));
        Assertions.assertTrue(StringUtils.equals(exception2.getErrorMessage(), "Validate ref2 has error"));
    }

    @ParameterizedTest
    @CsvSource({
            "00, 10", "00, 11", "00, 12", "00, 13", "00, 21", "00, 22",
            "40, 10", "40, 11", "40, 12", "40, 13", "40, 21", "40, 22"
    })
    void getAutoLoanPaymentDetailShouldReturnAmount3TabTest(String last2Digit, String customerStatusFromETE) throws TMBCommonException, ExecutionException, InterruptedException {
        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("*********");
        Mockito.when(commonPaymentService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKYCResponse);
        Mockito.when(accountService.isPayBillBySelfTTBAutoLoan(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(false);

        TopUpETETransaction topUpETETransaction = new TopUpETETransaction();
        topUpETETransaction.setAmount(new BigDecimal("1000.00"));
        topUpETETransaction.setInstallmentAmount(new BigDecimal("100.00"));
        topUpETETransaction.setTbankCustomer(new AutoLoanTBankCustomer());
        topUpETETransaction.getTbankCustomer().setName("นาย 5069606 ทดสอบ 5069606 นามสกุล");
        topUpETETransaction.getTbankCustomer().setCustomerNumber("8900");
        topUpETETransaction.getTbankCustomer().setCustomerStatus(customerStatusFromETE);
        topUpETETransaction.setDueDate("19");
        Mockito.when(topUpETEService.billPaymentAutoLoanVerify(any())).thenReturn(topUpETETransaction);

        TopUpVerifyRequest paymentDetailRequest = new TopUpVerifyRequest();
        paymentDetailRequest.setBillerCompCode("5003");
        paymentDetailRequest.setReference1("*********" + last2Digit);
        paymentDetailRequest.setReference2("8900");
        PaymentDetailResponse actual = billPaymentAutoLoanService.getAutoLoanPaymentDetail(crmId, correlationId, paymentDetailRequest);

        Assertions.assertFalse(actual.isOwner());
        Assertions.assertFalse(actual.isDirectDebit());
        Assertions.assertEquals(BILL_PAYMENT_TYPE_TOTAL_INSTALLMENT_SPECIFIED, actual.getPaymentType());
        Assertions.assertNull(actual.getAmount());
        Assertions.assertEquals("นาย 5069606 ทดสอบ 5069606 นามสกุล", actual.getPaymentName());
        Assertions.assertEquals("1000.00", actual.getFull().getAmount());
        Assertions.assertFalse(actual.getFull().isEditable());
        Assertions.assertEquals("100.00", actual.getMin().getAmount());
        Assertions.assertFalse(actual.getMin().isEditable());
        Assertions.assertEquals("0.00", actual.getSpecified().getAmount());
        Assertions.assertTrue(actual.getSpecified().isEditable());
        Assertions.assertEquals(SCHEDULE_CONFIG_TAB_AUTO_LOAN, actual.getScheduleConfig().getTabsOfSchedule());
        Assertions.assertTrue(new ReflectionEquals(new ScheduleConfigPhrase(PHRASE_BILLER_AUTO_LOAN_TITLE_ENTER_PAGE, PHRASE_BILLER_AUTO_LOAN_DETAIL_ENTER_PAGE, PHRASE_BILLER_AUTO_LOAN_REVIEW_PAGE))
                .matches(actual.getScheduleConfig().getPhrase()));
        Assertions.assertTrue(new ReflectionEquals(new ScheduleConfigFrequency(true, false, true))
                .matches(actual.getScheduleConfig().getFrequency()));
        Assertions.assertEquals(10, actual.getScheduleConfig().getDueDate().length());
    }

    @Test
    void autoLoanPaymentVerifyWhenCustomerNumberNotMatchShouldThrowsTmbCommonExceptionTest() throws TMBCommonException, ExecutionException, InterruptedException {
        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setIdNo("*********0001");
        customerKYCResponse.setCustomerFirstNameEn("name en");
        Mockito.when(commonPaymentService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKYCResponse);

        Mockito.when(commonPaymentService.getSequencePaymentId()).thenReturn("REF0000000001");

        TopUpETETransaction paymentTransaction = new TopUpETETransaction();
        paymentTransaction.setAmount(new BigDecimal("500.00"));
        paymentTransaction.setFee(new TopUpFee());
        paymentTransaction.setTbankCustomer(new AutoLoanTBankCustomer());
        paymentTransaction.getTbankCustomer().setName("Tbank bank");
        paymentTransaction.getTbankCustomer().setCustomerNumber("12344");
        Mockito.when(topUpETEService.billPaymentAutoLoanVerify(any()))
                .thenReturn(paymentTransaction);

        depositAccount.setProductNickname("FromNickname");
        depositAccount.setAccountName("FromAccountName");

        Assertions.assertThrows(Exception.class, () ->
                billPaymentAutoLoanService.autoLoanPaymentVerify(
                        topUpVerifyRequest, headers, transId, billerDetail, activityBillPayVerifyEvent, depositAccount)
        );

        Mockito.verify(logService, Mockito.times(1))
                .saveLogActivityBillPayEvent(Mockito.any());
    }


    @Test
    void validateAuthenticationWhenUseCommonAuthenticationTest() throws TMBCommonException {
        String transId = "transId";

        var paymentCacheData = new PaymentCacheData()
                .setRequireCommonAuthentication(false)
                .setRequirePin(true);

        Assertions.assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(billPaymentAutoLoanService, "validateAuthentication", transId, headers, paymentCacheData, "10.50"));

        ArgumentCaptor<LegacyAuthenticationRequest> captor = ArgumentCaptor.forClass(LegacyAuthenticationRequest.class);
        Mockito.verify(oauthService, Mockito.times(1)).validateAuthentication(any(), eq(null), captor.capture(), anyString());
        assertEquals(BILL_MODULE_PIN, captor.getValue().getModule());
    }

    @Test
    void validateAuthenticationWhenUseLegacyAuthenticationTest() throws TMBCommonException {
        String transId = "transId";
        String amount = "10.50";

        var paymentCacheData = new PaymentCacheData()
                .setRequireCommonAuthentication(true)
                .setRequirePin(false)
                .setCommonAuthenticationInformation(new CommonAuthenticationInformation()
                        .setFeatureId(COMMON_AUTH_TOP_UP_FEATURE_ID)
                        .setFlowName(COMMON_AUTH_TOP_UP_FLOW_NAME)
                        .setBillerCompCode("comp-code-form-cache")
                        .setTotalPaymentAccumulateUsage(new BigDecimal("1000.00"))
                );

        Assertions.assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(billPaymentAutoLoanService, "validateAuthentication", transId, headers, paymentCacheData, amount));

        ArgumentCaptor<CommonAuthenWithPayloadRequest> captor = ArgumentCaptor.forClass(CommonAuthenWithPayloadRequest.class);
        Mockito.verify(oauthService, Mockito.times(1)).validateAuthentication(any(), captor.capture(), eq(null), anyString());
        assertEquals(COMMON_AUTH_TOP_UP_FEATURE_ID, captor.getValue().getFeatureId());
        assertEquals(COMMON_AUTH_TOP_UP_FLOW_NAME, captor.getValue().getFlowName());
        assertEquals("comp-code-form-cache", captor.getValue().getBillerCompCode());
        assertEquals("1000.00", captor.getValue().getDailyAmount());
        assertEquals(transId, captor.getValue().getRefId());
        assertEquals(amount, captor.getValue().getAmount());
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenNotExecuteCommonAuthen() throws TMBCommonException {
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(new VerifyTransactionResult(false, new FaceRecognizeResponse().setIsRequireFr(true).setPaymentAccuUsgAmt(BigDecimal.ZERO), new CommonAuthenResult()));
    }

    private void mockValidateIsRequireVerifyTransactionBillWhenExecuteCommonAuthen() throws TMBCommonException {
        CommonAuthenResult commonAuthenResult = new CommonAuthenResult()
                .setRequireCommonAuthen(true)
                .setIsForceFR(true)
                .setPinFree(true);
        VerifyTransactionResult resultWhenExecuteCommonAuthen = new VerifyTransactionResult(false, null, commonAuthenResult);
        Mockito.when(billPaymentValidateTransaction.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(resultWhenExecuteCommonAuthen);
    }
}

