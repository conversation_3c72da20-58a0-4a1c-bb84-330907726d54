{
  "port": 8080,
  "protocol": "http",
  "name": "proxy",
  "stubs": [
    <% include predicates/post-transfer-confirmation.ejs %>,
    <% include predicates/post-transfer-td-Validate.ejs %>,
    <% include predicates/post-transfer-bankInfo.ejs %>,
    <% include predicates/post-transfer-category.ejs %>,
    <% include predicates/post-transfer-category-transfer.ejs %>,
    <% include predicates/post-transfer-validate.ejs %>,
    <% include predicates/post-activity.ejs %>,
    <% include predicates/post-activity-search.ejs %>,
    <% include predicates/post-activity-note.ejs %>,
    <% include predicates/post-generate-pdf-statement.ejs %>,
    <% include predicates/post-generate-pdf-statement.ejs %>,
    <% include predicates/topup-verify-true.ejs %>,
    <% include predicates/topup-verify-easy-pass.ejs %>,
    <% include predicates/topup-verify-e-wallet.ejs %>,
    <% include predicates/topup-confirm-true.ejs %>,
    <% include predicates/topup-confirm-easy-pass.ejs %>,
    <% include predicates/topup-confirm-e-wallet.ejs %>,
    <% include predicates/billpay-amount-detail-full-min-specifed.ejs %>,
    <% include predicates/billpay-amount-detail-full-specifed.ejs %>,
    <% include predicates/billpay-amount-detail-full-with-owner.ejs %>,
    <% include predicates/billpay-amount-detail-full.ejs %>,
    <% include predicates/billpay-amount-detail-total-installment-specifed.ejs %>,
    <% include predicates/billpay-amount-detail-total-specifed.ejs %>,
    <% include predicates/scan-fail-no-information-in-system-end-fnf.ejs %>,
    <% include predicates/scan-fail-no-information-in-system.ejs %>,
    <% include predicates/scan-fail-qr-invalid-end-fqr.ejs %>,
    <% include predicates/scan-fail-qr-invalid.ejs %>,
    <% include predicates/scan-success-type-transfer.ejs %>,
    <% include predicates/scan-success-type-ewallet.ejs %>,
    <% include predicates/billpay-amount-detail-mea.ejs %>,
    <% include predicates/billpay-amount-detail-pea.ejs %>,
    <% include predicates/billpay-verify-mea.ejs %>,
    <% include predicates/billpay-verify-pea.ejs %>,
    <% include predicates/billpay-confirm-mea.ejs %>,
    <% include predicates/billpay-confirm-pea.ejs %>,
    <% include predicates/billpay-verify-pea-6-month.ejs %>,
    <% include predicates/get-transfer-bank-info.ejs %>,
    <% include predicates/billpay-amount-detail-mwa.ejs %>,
    <% include predicates/billpay-verify-mwa-6-month.ejs %>,
    <% include predicates/billpay-verify-mwa.ejs %>,
    <% include predicates/billpay-confirm-mwa.ejs %>,
    <% include predicates/post-transfer-other-bank-confirm.ejs %>,
    <% include predicates/post-transfer-other-bank-validate.ejs %>,
    <% include predicates/post-transfer-promptpay-confirm.ejs %>,
    <% include predicates/post-transfer-promptpay-validate.ejs %>,
    <% include predicates/verify-slip-transfer.ejs %>,
    <% include predicates/verify-slip-thai-qr.ejs %>,
    <% include predicates/verify-slip-billpay.ejs %>,
    <% include predicates/scan-success-type-verify-slip-billpay.ejs %>,
    <% include predicates/scan-success-type-verify-slip-thai-qr.ejs %>,
    <% include predicates/scan-success-type-verify-slip-tranfer.ejs %>,
    <% include predicates/topup-verify-credit-card.ejs %>,
    <% include predicates/topup-confirm-credit-card.ejs %>,
    {
      "responses": [
        {
          "proxy": {
            "to": "http://localhost",
            "mode": "proxyAlways",
            "predicateGenerators": [
              {
                "matches": {
                  "path": true
                }
              }
            ]
          }
        }
      ]
    }
  ]
}