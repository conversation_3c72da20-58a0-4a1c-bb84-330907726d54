{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/bill-payment/validation"}}, {"equals": {"body": {"note": "test-credit-card"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"fee": "0.00", "trans_id": "TOPUP_test-credit-card", "is_require_confirm_pin": false, "top_up_ref": null, "top_up_account_name": null}}}}]}