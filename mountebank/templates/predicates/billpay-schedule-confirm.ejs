{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/schedule/billpay/confirm"}}, {"equals": {"body": {"trans_id": "billpay-schedule-test"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"reference_1": "20210330120000024", "top_up_created_datetime": "2021-11-22T14:22:43"}}}}]}