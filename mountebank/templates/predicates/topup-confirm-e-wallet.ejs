{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/transfer/prompt-pay/confirm"}}, {"equals": {"body": {"trans_id": "TOPUP_test-e-wallet"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"remaining_balance": "1000.00", "reference_1": "20210330120000024", "top_up_created_datetime": "2021-11-22T14:22:43", "balance_after_top_up": null}}}}]}