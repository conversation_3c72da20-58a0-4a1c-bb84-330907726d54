{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/bill-payment/mwa/validation", "body": {"reference_1": "1234567890", "biller_comp_code": "2699"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"fee": "10.00", "trans_id": "billpay_test_mwa", "is_require_confirm_pin": false, "net_amount": "1605.36", "bill": [{"bill_number": "06/2564", "amount": "1500.00", "vat": "105.00"}]}}}}]}