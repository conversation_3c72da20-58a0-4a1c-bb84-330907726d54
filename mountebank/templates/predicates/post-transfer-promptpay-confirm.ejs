{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/transfer/promptpay-confirm", "body": {"trans_id": "TRANSFER_000000000000000000000000000000_mock_promptpay"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "transfer-experience-service", "description": "Success"}, "data": {"reference_no": "202106111500000025", "remaining_balance": "78563412.98", "transfer_created_datetime": "2021-06-23T15:48:05"}}}}]}