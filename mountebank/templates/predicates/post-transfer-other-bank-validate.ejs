{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/transfer/other-bank-validate", "headers": {"Mock": "true"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "transfer-experience-service", "description": "Success"}, "data": {"trans_id": "TRANSFER_000000000000000000000000000000_mock_other_bank", "amount": "5000.00", "fee": "25", "to_account_name": "<PERSON><PERSON><PERSON> k<PERSON>", "is_require_confirm_pin": true}}}}]}