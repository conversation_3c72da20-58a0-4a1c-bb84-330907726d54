{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/transfer/validate"}}, {"equals": {"headers": {"Mock": "true"}, "body": {"note": "TD"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "transfer-experience-service", "description": "Success"}, "data": {"trans_id": "TRANSFER_000000000000000000000000000000_mock", "interest": "10", "principal": "1000.00", "penalty": "0", "net_amount": "1007.00", "tax": "3", "fee": "0", "amount": "1000.00", "isRequireConfirmPin": false}}}}]}