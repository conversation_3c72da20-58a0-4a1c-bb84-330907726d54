{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/transfer/prompt-pay/validate"}}, {"equals": {"body": {"note": "test-e-wallet"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"fee": "0.00", "trans_id": "TOPUP_test-e-wallet", "is_require_confirm_pin": false, "top_up_ref": null, "top_up_account_name": "<PERSON><PERSON><PERSON><PERSON> jat<PERSON>"}}}}]}