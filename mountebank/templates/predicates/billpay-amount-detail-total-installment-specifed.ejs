{"predicates": [{"and": [{"equals": {"method": "POST", "path": "/apis/payment/bill-payment/payment-detail"}}, {"matches": {"body": {"reference_1": "^32999.*"}}}]}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"payment_type": "TOTAL_INSTALLMENT_SPECIFIED", "is_owner": false, "amount": null, "full": {"amount": "1000.00", "editable": false}, "min": {"amount": "100.00", "editable": false}, "specified": {"amount": "0.00", "editable": true}}}}}]}