{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/bill-payment/scan"}}, {"equals": {"body": {"text": "e-wallet1"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"comp_code": "EW01", "is_online_bill_payment": false, "ref1": {"value": "123456789012345", "editable": false}, "ref2": null, "ref3": null, "amount": {"value": "0.00", "editable": true}, "qr_type": "E-WALLET", "qr": "PROMPTPAY"}}}}]}