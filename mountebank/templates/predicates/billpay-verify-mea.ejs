{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/bill-payment/mea/validation", "body": {"reference_1": "123456789", "biller_comp_code": "2533"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"fee": "10.00", "trans_id": "billpay_test_mea", "is_require_confirm_pin": false, "net_amount": "1605.36"}}}}]}