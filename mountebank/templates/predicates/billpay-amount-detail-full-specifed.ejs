{"predicates": [{"and": [{"equals": {"method": "POST", "path": "/apis/payment/bill-payment/payment-detail"}}, {"matches": {"body": {"reference_1": "^21999.*"}}}]}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"payment_type": "FULL_SPECIFIED", "is_owner": false, "amount": null, "full": {"amount": "1000.00", "editable": false}, "min": null, "specified": {"amount": "0.00", "editable": true}}}}}]}