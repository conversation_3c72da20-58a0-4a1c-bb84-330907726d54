{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/transfer/confirm"}}, {"equals": {"body": {"trans_id": "TRANSFER_000000000000000000000000000000_mock"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "transfer-experience-service", "description": "Success"}, "data": {"reference_no": "202106111500000025", "remaining_balance": "34973941.24", "transfer_created_datetime": "2021-06-11T15:48:05"}}}}]}