{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/bill-payment/scan"}}, {"matches": {"body": {"mount_bank": "fail-not-found"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0001", "message": "failed", "service": "common", "description": {"en": "Sorry, this biller cannot be found in the system.", "th": "ขออภัย ไม่พบข้อมูลผู้ให้บริการนี้ในระบบ"}}, "data": null}}}]}