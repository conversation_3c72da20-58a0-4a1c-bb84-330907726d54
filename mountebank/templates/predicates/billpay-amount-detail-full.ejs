{"predicates": [{"and": [{"equals": {"method": "POST", "path": "/apis/payment/bill-payment/payment-detail"}}, {"matches": {"body": {"reference_1": "^12999.*"}}}]}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"payment_type": "FULL", "is_owner": false, "amount": {"amount": "100.00", "editable": false}, "full": null, "min": null, "specified": null}}}}]}