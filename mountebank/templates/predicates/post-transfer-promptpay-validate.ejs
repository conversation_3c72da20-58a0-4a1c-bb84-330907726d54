{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/transfer/promptpay-validate", "headers": {"Mock": "true"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "transfer-experience-service", "description": "Success"}, "data": {"trans_id": "TRANSFER_000000000000000000000000000000_mock_promptpay", "amount": "4500.00", "fee": "0", "to_account_name": "Kunnobnom pp", "is_require_confirm_pin": true}}}}]}