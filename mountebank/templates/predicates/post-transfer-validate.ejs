{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/transfer/validate"}}, {"equals": {"headers": {"Mock": "true"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "transfer-experience-service", "description": "Success"}, "data": {"trans_id": "TRANSFER_000000000000000000000000000000_mock", "interest": null, "principal": null, "penalty": null, "net_amount": null, "tax": "3", "fee": "0", "amount": "1000.00", "to_name": "Name test", "is_require_confirm_pin": false}}}}]}