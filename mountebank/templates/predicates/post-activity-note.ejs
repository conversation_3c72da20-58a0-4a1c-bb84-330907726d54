{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/financial/txnmemo"}}, {"equals": {"body": {"event_id": "21000000000011506939", "fin_txn_memo": "Test Notes", "txn_call": "UPDATE"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service", "description": "success"}}}}]}