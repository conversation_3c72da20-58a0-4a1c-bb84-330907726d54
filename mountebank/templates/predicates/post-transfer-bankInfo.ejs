{"predicates": [{"equals": {"method": "POST", "path": "/apis/transfer/bankInfo"}}, {"equals": {"headers": {"X-Correlation-ID": "true"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "transfer-experience-service", "description": "Success"}, "data": [{"bank_cd": "25", "bank_name_th": "ธนาคารกรุงศรีอยุธยา จำกัด (มหาชน)", "bank_name_en": "Bank of Ayudhya", "bank_shortname": "BAY", "bank_acct_length": "10", "bank_status": "Active", "orft_effective_date": *************, "orft_expire_date": *************, "smart_effective_date": *************, "smart_expire_date": *************, "display_order": 5, "promptpay_effective_date": *************, "promptpay_expire_date": *************, "promptpay_status": "Available", "bank_logo": "https://storage.googleapis.com/oneapp-vit.appspot.com/bank/logo/bank-logo-25.png", "update_date": *************, "update_by": "f2f149"}, {"bank_cd": "65", "bank_name_th": "ธนาคารธนชาต จำกัด (มหาชน)", "bank_name_en": "Thanachart Bank", "bank_shortname": "TBANK", "bank_acct_length": "10", "bank_status": "Active", "orft_effective_date": *************, "orft_expire_date": *************, "smart_effective_date": *************, "smart_expire_date": *************, "display_order": 32, "promptpay_effective_date": *************, "promptpay_expire_date": *************, "promptpay_status": "Available", "bank_logo": "https://storage.googleapis.com/oneapp-vit.appspot.com/bank/logo/bank-logo-65.png", "update_date": *************, "update_by": "Admin"}]}}}]}