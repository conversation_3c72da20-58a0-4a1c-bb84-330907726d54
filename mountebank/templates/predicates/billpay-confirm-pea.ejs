{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/bill-payment/pea/confirm"}}, {"equals": {"body": {"trans_id": "billpay_test_pea"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"remaining_balance": "1000000.00", "reference_1": "20210330120000024", "top_up_created_datetime": "2021-11-22T14:22:43", "balance_after_top_up": null, "qr": "iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQAAAABRBrPYAAACCklEQVR4Xu2ZPW7DMAxGaWTImCPoKD6afTQfJUfI6CEwy/dJblq1RTsWoLhEFp+WD/yTYv4Xe1i/860NrLOBdTawzgbW2f/HNsPKpo+7LZPvN98W7dqSEdOysIyd2f15ZXN6+dJhZtdH2ZYDoWbXl/v0RMzMmB0XPsOvQwMzu0RK+Uog3eefdUuAsSzyQ/tT8fQ1s9JghhVCp0WQfrRrGbHT1uOyW3E/IqxCt5dlw0K3i6OU+45SJnolnrSZD6uNOByucaX5ka/IkQ2rEp1JZJSaG7U3xjgOJcTqzuyMKzV0iK7WkxJitdoGwrgix04zUmvSkYRY/WnjSgwvJJgqjlTMh9UIMskXE36rvd5UTIdFP0YwMKO4xBkuxfiL3PmwOszTpMPWUBG6HcqIrSh1n9fmqFfBoLsSnQiLZsQkqxqjzCKQ2hU5JUYEhX9SP2Z4OceVlmDpsNaPSSkcQbPZTzVpMBxKKc21BJI2240wIUZKVT+6UYk1uPXPJomwlZSqO/UQGAnG2YQYfk1sCCaHhpdKZ8SabaZ3ge1Dnn2uvWkwJDJ0q3PtIhW5GKonZcQWlsUpunE3xg+mnvRRt0TYmUQqukz4zajEebGNxwLog4dYehLkkhgzUipMbfmdTomxLPzjqRpTL39rnK18Pgyxwm+6CvLmqpTqL8V5sF9tYJ0NrLOBdTawzhJhb3yD8NVyPEF1AAAAAElFTkSuQmCC"}}}}]}