{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/bill-payment/pea/validation", "body": {"reference_1": "123456789000", "biller_comp_code": "2700"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"fee": "10.00", "trans_id": "billpay_test_pea", "is_require_confirm_pin": false, "net_amount": "1605.36", "over_due": true, "bill": [{"eletricity_of": "06/2564", "amount": "1500.00", "vat": "105.00"}]}}}}]}