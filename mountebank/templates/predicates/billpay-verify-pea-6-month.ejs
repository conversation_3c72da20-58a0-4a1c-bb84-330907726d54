{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/bill-payment/pea/validation", "body": {"reference_1": "123456789666", "biller_comp_code": "2700"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"fee": "10.00", "trans_id": "billpay_test_pea", "is_require_confirm_pin": false, "net_amount": "1605.00", "over_due": false, "bill": [{"eletricity_of": "06/2564", "amount": "250.00", "vat": "17.50"}, {"eletricity_of": "05/2564", "amount": "250.00", "vat": "17.50"}, {"eletricity_of": "04/2564", "amount": "250.00", "vat": "17.50"}, {"eletricity_of": "03/2564", "amount": "250.00", "vat": "17.50"}, {"eletricity_of": "02/2564", "amount": "250.00", "vat": "17.50"}, {"eletricity_of": "01/2564", "amount": "250.00", "vat": "17.50"}]}}}}]}