{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/bill-payment/confirm"}}, {"equals": {"body": {"trans_id": "TOPUP_test-easy-pass"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"remaining_balance": "1000111.00", "reference_1": "20210330120000024", "top_up_created_datetime": "2021-11-22T14:22:43", "balance_after_top_up": "2700.00"}}}}]}