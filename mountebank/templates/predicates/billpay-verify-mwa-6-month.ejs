{"predicates": [{"equals": {"method": "POST", "path": "/apis/payment/bill-payment/mwa/validation", "body": {"reference_1": "1234567891", "biller_comp_code": "2699"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "payment-experience-service"}, "data": {"fee": "10.00", "trans_id": "billpay_test_mwa", "is_require_confirm_pin": false, "net_amount": "1605.00", "bill": [{"bill_number": "06/2564", "amount": "250.00", "vat": "17.50"}, {"bill_number": "05/2564", "amount": "250.00", "vat": "17.50"}, {"bill_number": "04/2564", "amount": "250.00", "vat": "17.50"}, {"bill_number": "03/2564", "amount": "250.00", "vat": "17.50"}, {"bill_number": "02/2564", "amount": "250.00", "vat": "17.50"}, {"bill_number": "01/2564", "amount": "250.00", "vat": "17.50"}]}}}}]}