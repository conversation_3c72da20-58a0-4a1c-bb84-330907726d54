
## [ Version 4.3.0 ] ##

* [ONEAPP-93480](https://jira.tau2904.com/browse/ONEAPP-93480) [RQA][RGT2-R2][PRB]: Account Detail screen display duplicate transaction for payment HP
* [ONEAPP-94371](https://jira.tau2904.com/browse/ONEAPP-94371) [RQA][RGT2-R2][PRB]: Cannot verify QR slip that not have logo and show error screen
* [ONEAPP-91401](https://jira.tau2904.com/browse/ONEAPP-91401) [BE]- Improve code smell for GenerateCSVUtils
* [ONEAPP-91387](https://jira.tau2904.com/browse/ONEAPP-91387) InternationalTransferLogService Code Coverage
* [ONEAPP-91385](https://jira.tau2904.com/browse/ONEAPP-91385) InternationalTransferController Code Coverage
* [ONEAPP-90366](https://jira.tau2904.com/browse/ONEAPP-90366) R3.0_UAT_OTT : Email display wrong format
* [ONEAPP-90359](https://jira.tau2904.com/browse/ONEAPP-90359) R2.0_UAT2_OTT: Swift - CSV file doesn't upload to SFTP
* [ONEAPP-87720](https://jira.tau2904.com/browse/ONEAPP-87720) OTT_email: displays incomplete Account No. on transfer result email.
* [ONEAPP-87547](https://jira.tau2904.com/browse/ONEAPP-87547) [BE] Display incorrect amount when scan QR generate from U CHOOSE APP
* [ONEAPP-90654](https://jira.tau2904.com/browse/ONEAPP-90654) Transaction Activity Controller Code smell improvement
* [ONEAPP-88778](https://jira.tau2904.com/browse/ONEAPP-88778) [BE] Fix code smell - Repo Payment-exp-service
* [ONEAPP-89371](https://jira.tau2904.com/browse/ONEAPP-89371) R3.0_SIT_defect_BE: FCD Convert currency: Batch Report (Murex)> Invalid template of case Non-Split (To Sell)
* [ONEAPP-89126](https://jira.tau2904.com/browse/ONEAPP-89126) [FCD Transfer] - On next day the Activity screen will display activity from calendar and EXIM. When transaction confirmed will display only calendar
* [ONEAPP-89125](https://jira.tau2904.com/browse/ONEAPP-89125) [FCD Transfer] - On Day one the Activity screen will display activity from calendar and EXIM. When transaction confirmed will display only calendar

## [ Version 4.0.0 ] ##

* [ONEAPP-90359](https://jira.tau2904.com/browse/ONEAPP-90359) R2.0_UAT2_OTT: Swift - CSV file doesn't upload to SFTP
* [ONEAPP-30842](https://jira.tau2904.com/browse/ONEAPP-30842) Update new title for History transaction type : Transfer / Bill Pay / Top up
* [ONEAPP-87547](https://jira.tau2904.com/browse/ONEAPP-87547) [BE] Display incorrect amount when scan QR generate from U CHOOSE APP
* [ONEAPP-86585](https://jira.tau2904.com/browse/ONEAPP-86585) Transfer on-us success, missing write available balance in financial log
* [ONEAPP-86333](https://jira.tau2904.com/browse/ONEAPP-86333) [UAT][RGT-R2][PRB] : Email noti's subject when make transaction OTT was displayed Value of channel incorrectly.
* [ONEAPP-84137](https://jira.tau2904.com/browse/ONEAPP-84137) Account Activity - Hide see detail button for Bill pay / Top up (Configuration)
* [ONEAPP-81771](https://jira.tau2904.com/browse/ONEAPP-81771) [RQA][RGT-R1.5] Email Noti of OTT was displayed incompletely.
* [ONEAPP-59187](https://jira.tau2904.com/browse/ONEAPP-59187) Pay with credit card - Not display biller name on Credit card statement
* [ONEAPP-82342](https://jira.tau2904.com/browse/ONEAPP-82342) R2.0_UAT2_OTT: Swift - CSV file doesn't upload to SFTP
* [ONEAPP-76718](https://jira.tau2904.com/browse/ONEAPP-76718) [UAT][RGT-R2][PRB] : ttb fleet card (5004) - activity log , transaction detail on history and e-slip are wrong when pay again on history menu
* [ONEAPP-75640](https://jira.tau2904.com/browse/ONEAPP-75640) [UAT][RGT-R2][PRB] : Activity log for Top up eWallet should not write Ref1/Mobile number with masking
* [ONEAPP-75417](https://jira.tau2904.com/browse/ONEAPP-75417) [RQA][RGT-R2][PRB] : Email Notification for eDonation does not marking CID on email (template id 11419)
* [ONEAPP-52231](https://jira.tau2904.com/browse/ONEAPP-52231) OTT: SWIFT copy and debit advise are missing on email notification.
* [ONEAPP-75469](https://jira.tau2904.com/browse/ONEAPP-75469) Update logic check ref no. for Toyota
* [ONEAPP-73400](https://jira.tau2904.com/browse/ONEAPP-73400) [Scan QR] Cannot scan bill payment QR code/barcode
* [ONEAPP-72288](https://jira.tau2904.com/browse/ONEAPP-72288) [BE] Remove paymentCacheData in Bill Payment service
* [ONEAPP-69121](https://jira.tau2904.com/browse/ONEAPP-69121) Improve code oneapp-activity-lib
* [ONEAPP-60604](https://jira.tau2904.com/browse/ONEAPP-60604) [Redis Tuning] Change endpoint and call API to new cluster group 1 (Session Management)
* [ONEAPP-59172](https://jira.tau2904.com/browse/ONEAPP-59172) Apply Mongo pool to all service
* [ONEAPP-52238](https://jira.tau2904.com/browse/ONEAPP-52238) Circuit Breaker​ / Rate Limit
* [ONEAPP-51511](https://jira.tau2904.com/browse/ONEAPP-51511) Async call but waiting database processing
* [ONEAPP-44841](https://jira.tau2904.com/browse/ONEAPP-44841) [Yellow] 6. Timeout​ analysis
* [ONEAPP-35226](https://jira.tau2904.com/browse/ONEAPP-35226) Bill payment - Move MEA to OCP
* [ONEAPP-64772](https://jira.tau2904.com/browse/ONEAPP-64772) [Statement] Unable to generate PDF statement for current account
* [ONEAPP-62833](https://jira.tau2904.com/browse/ONEAPP-62833) Pay with QR PromptPay - Send transaction type incorrect
* [ONEAPP-61455](https://jira.tau2904.com/browse/ONEAPP-61455) [PDF Statement] Term Deposit (TD) statement sorting in wrong direction
* [ONEAPP-59127](https://jira.tau2904.com/browse/ONEAPP-59127) Request Cash for you to another customer’s account number
* [ONEAPP-59115](https://jira.tau2904.com/browse/ONEAPP-59115) Cash for You - Modification of installment payment amount
* [ONEAPP-58722](https://jira.tau2904.com/browse/ONEAPP-58722) Update phrase service hour
* [ONEAPP-57164](https://jira.tau2904.com/browse/ONEAPP-57164) R1.5-DEV-[BE] Tuning : Favorite Bill Payment / Topup list: Bill expired able pay from history screen
* [ONEAPP-52257](https://jira.tau2904.com/browse/ONEAPP-52257) Bill payment - Able to pay ttb Drive comp code ****************
* [ONEAPP-53471](https://jira.tau2904.com/browse/ONEAPP-53471) [RQA][RGT-R2][PRB] : OTT-Navigate to next screen wrong when click transfer more on e-Slip on transaction
* [ONEAPP-49840](https://jira.tau2904.com/browse/ONEAPP-49840) Tuning : Transfer Other Bank / PromptPay / Topup eWallet send citizen id if transaction amount >= 700,000
* [ONEAPP-54896](https://jira.tau2904.com/browse/ONEAPP-54896) R2.0_RGT_UAT_defect BE: Activity log Other Dream account Reason Failure Incorrectly
* [ONEAPP-42815](https://jira.tau2904.com/browse/ONEAPP-42815) [OTT] Create connection pool for sftp function.
* [ONEAPP-49693](https://jira.tau2904.com/browse/ONEAPP-49693) Cannot edit amount when scan standard QR/Barcode with amount
* [ONEAPP-48713](https://jira.tau2904.com/browse/ONEAPP-48713) Cannot scan miniQR on UOB slip
* [ONEAPP-47561](https://jira.tau2904.com/browse/ONEAPP-47561) [RQA][RGT-R2][PRB] : QR e-Donation error \
* [ONEAPP-45094](https://jira.tau2904.com/browse/ONEAPP-45094) [RQA][RGT-R2][PRB] : Billpay/TopUp-Type of from account missing with some biller such as GSB credit card , ttb credit card
* [ONEAPP-44718](https://jira.tau2904.com/browse/ONEAPP-44718) [RQA][RGT-R2][PRB] : {typeTH} and {typeEN} value is missing on email noti when QR Transfer to eWallet ID
* [ONEAPP-44902](https://jira.tau2904.com/browse/ONEAPP-44902) [RQA][RGT-R2][PRB] : Activity Log for eDonation write ref2 value without masking
* [ONEAPP-44535](https://jira.tau2904.com/browse/ONEAPP-44535) [RQA][RGT-R2][PRB] : Activity Log for \
* [ONEAPP-43550](https://jira.tau2904.com/browse/ONEAPP-43550) [BE] Duplicate transaction when Redis slow
* [ONEAPP-43659](https://jira.tau2904.com/browse/ONEAPP-43659) Handle editable amount from deeplink
* [ONEAPP-43620](https://jira.tau2904.com/browse/ONEAPP-43620) Auto Loan (5003) : Customer cannot pay bill found error \
* [ONEAPP-42917](https://jira.tau2904.com/browse/ONEAPP-42917) [API Crossborder] create script change config for API crossborder
* [ONEAPP-42747](https://jira.tau2904.com/browse/ONEAPP-42747) R2.0_UAT_Defect_BE: Enoti Transaction date (Transfer/BillPay/Topup) display incorrect
* [ONEAPP-43028](https://jira.tau2904.com/browse/ONEAPP-43028) R2.0_UAT_defect BE: Write activity log IOS scan QR with amount incorrect
* [ONEAPP-40492](https://jira.tau2904.com/browse/ONEAPP-40492) OTT: customer cannot delete recipient.
* [ONEAPP-41524](https://jira.tau2904.com/browse/ONEAPP-41524) R2-Deeplink-OTT-Display message wrong when service error
* [ONEAPP-42187](https://jira.tau2904.com/browse/ONEAPP-42187) ttb credit card - Change ref1 when 'Pay again' form History, inquiry and pay with same account no.
* [ONEAPP-41737](https://jira.tau2904.com/browse/ONEAPP-41737) R1.5_UAT_Regression_HotFIX : IOS Transfer From own TD Account display duplicate transaction
* [ONEAPP-42321](https://jira.tau2904.com/browse/ONEAPP-42321) 
* [ONEAPP-41943](https://jira.tau2904.com/browse/ONEAPP-41943) 
* [ONEAPP-41805](https://jira.tau2904.com/browse/ONEAPP-41805) 
* [ONEAPP-41784](https://jira.tau2904.com/browse/ONEAPP-41784) 
* [ONEAPP-41645](https://jira.tau2904.com/browse/ONEAPP-41645) 
* [ONEAPP-40933](https://jira.tau2904.com/browse/ONEAPP-40933) 
* [ONEAPP-41148](https://jira.tau2904.com/browse/ONEAPP-41148) 
* [ONEAPP-41172](https://jira.tau2904.com/browse/ONEAPP-41172) 

## [ Version 3.1.0 ] ##

* [ONEAPP-60471](https://jira.tau2904.com/browse/ONEAPP-60471) [Tech] [BE] Call API oneapp-auth-service to get redis key VERIFY_PIN_REF_ID_
* [ONEAPP-46899](https://jira.tau2904.com/browse/ONEAPP-46899) payment-exp-service
* [ONEAPP-42815](https://jira.tau2904.com/browse/ONEAPP-42815) [OTT] Create connection pool for sftp function.
* [ONEAPP-49693](https://jira.tau2904.com/browse/ONEAPP-49693) Cannot edit amount when scan standard QR/Barcode with amount
* [ONEAPP-43620](https://jira.tau2904.com/browse/ONEAPP-43620) Auto Loan (5003) : Customer cannot pay bill found error \
* [ONEAPP-43550](https://jira.tau2904.com/browse/ONEAPP-43550) [BE] Duplicate transaction when Redis slow
* [ONEAPP-43028](https://jira.tau2904.com/browse/ONEAPP-43028) R2.0_UAT_defect BE: Write activity log IOS scan QR with amount incorrect
* [ONEAPP-40492](https://jira.tau2904.com/browse/ONEAPP-40492) OTT: customer cannot delete recipient.
* [ONEAPP-42187](https://jira.tau2904.com/browse/ONEAPP-42187) ttb credit card - Change ref1 when 'Pay again' form History, inquiry and pay with same account no.
* [ONEAPP-41737](https://jira.tau2904.com/browse/ONEAPP-41737) R1.5_UAT_Regression_HotFIX : IOS Transfer From own TD Account display duplicate transaction
* [ONEAPP-41943](https://jira.tau2904.com/browse/ONEAPP-41943) 
* [ONEAPP-41784](https://jira.tau2904.com/browse/ONEAPP-41784) 
* [ONEAPP-41805](https://jira.tau2904.com/browse/ONEAPP-41805) 
* [ONEAPP-41751](https://jira.tau2904.com/browse/ONEAPP-41751) 
* [ONEAPP-41645](https://jira.tau2904.com/browse/ONEAPP-41645) 
* [ONEAPP-41534](https://jira.tau2904.com/browse/ONEAPP-41534) 
* [ONEAPP-41172](https://jira.tau2904.com/browse/ONEAPP-41172) 
* [ONEAPP-40933](https://jira.tau2904.com/browse/ONEAPP-40933) 
* [ONEAPP-40863](https://jira.tau2904.com/browse/ONEAPP-40863) 
* [ONEAPP-40895](https://jira.tau2904.com/browse/ONEAPP-40895) 
* [ONEAPP-40783](https://jira.tau2904.com/browse/ONEAPP-40783) 
* [ONEAPP-40616](https://jira.tau2904.com/browse/ONEAPP-40616) [BE] Transfer - Display incorrect mobile number when got error 'Not registered Promptpay yet'
* [ONEAPP-40527](https://jira.tau2904.com/browse/ONEAPP-40527) System send ref2 (CID) with masking in eDonation flow
* [ONEAPP-40540](https://jira.tau2904.com/browse/ONEAPP-40540) Cannot scan barcode TRUE (0328)
* [ONEAPP-40158](https://jira.tau2904.com/browse/ONEAPP-40158) [BE] Top up Fleet Card (0012) : Can not set schedule
* [ONEAPP-40280](https://jira.tau2904.com/browse/ONEAPP-40280) Cannot pay AIA (0002, 2095). Ref1 is incorrect.
* [ONEAPP-40251](https://jira.tau2904.com/browse/ONEAPP-40251) [BE] Pay ttb credit card fail, transaction does not found in Fin log
* [ONEAPP-40252](https://jira.tau2904.com/browse/ONEAPP-40252) [BE] Pay legacy biller fail, mapping error incorrect
* [ONEAPP-39255](https://jira.tau2904.com/browse/ONEAPP-39255) [BE] Create fail payment transaction into Calendar
* [ONEAPP-39305](https://jira.tau2904.com/browse/ONEAPP-39305) Statement: statement TD account is missing.
* [ONEAPP-39855](https://jira.tau2904.com/browse/ONEAPP-39855) OTT: system already deduct the balance and write transaction on DB fail.
* [ONEAPP-39895](https://jira.tau2904.com/browse/ONEAPP-39895) Activity log_Statement: need more details when cannot generate statement.
* [ONEAPP-39856](https://jira.tau2904.com/browse/ONEAPP-39856) OTT: Wrong converting exchange rate from USD to THB.
* [ONEAPP-39558](https://jira.tau2904.com/browse/ONEAPP-39558) Statement : Display customer name in statement
* [ONEAPP-39500](https://jira.tau2904.com/browse/ONEAPP-39500) [BE] Confirm service at Review screen
* [ONEAPP-35225](https://jira.tau2904.com/browse/ONEAPP-35225) [BE] : Get Payment Detail (call service at Ref1)
* [ONEAPP-39684](https://jira.tau2904.com/browse/ONEAPP-39684) OTT : Retry to upload file when upload failed
* [ONEAPP-39655](https://jira.tau2904.com/browse/ONEAPP-39655) OTT : Customer unable to transfer
* [ONEAPP-39688](https://jira.tau2904.com/browse/ONEAPP-39688) OTT: incorrect Fee when transfer with JPY.
* [ONEAPP-39603](https://jira.tau2904.com/browse/ONEAPP-39603) Cannot pay bill MEA, PEA, MWA, mPAy
* [ONEAPP-39592](https://jira.tau2904.com/browse/ONEAPP-39592) OTT : Unable to transfer
* [ONEAPP-37929](https://jira.tau2904.com/browse/ONEAPP-37929) [BE] Bill Payment PromptPay service have slowe response times
* [ONEAPP-39048](https://jira.tau2904.com/browse/ONEAPP-39048) Cannot pay bill - S 11 GROUP PUBLIC CO LIMITED (2896)
* [ONEAPP-39006](https://jira.tau2904.com/browse/ONEAPP-39006) Cannot pay bill Army Transport School (0918)
* [ONEAPP-38816](https://jira.tau2904.com/browse/ONEAPP-38816) Cannot pay bill KTC (comp code 0966)
* [ONEAPP-38246](https://jira.tau2904.com/browse/ONEAPP-38246) [ETE] Bill payment - Cannot pay bill WORLDLEASE COMPANY LIMITED (010553706634201)
* [ONEAPP-39197](https://jira.tau2904.com/browse/ONEAPP-39197) OTT: system not generate csv file when customer submit OTT transaction.
* [ONEAPP-38712](https://jira.tau2904.com/browse/ONEAPP-38712) OTT : Calcualate transfer fee wrong for JPY
* [ONEAPP-38325](https://jira.tau2904.com/browse/ONEAPP-38325) Bill Payment PEA: Pay bill failed but deduct money success.
* [ONEAPP-38218](https://jira.tau2904.com/browse/ONEAPP-38218) OTT data missing : need to keep into DB before export to file
* [ONEAPP-38048](https://jira.tau2904.com/browse/ONEAPP-38048) Display Cheq no. in statement for current account
* [ONEAPP-38188](https://jira.tau2904.com/browse/ONEAPP-38188) [BE] production hot fixed supported
* [ONEAPP-38120](https://jira.tau2904.com/browse/ONEAPP-38120) OTT : need to write log on generate file to exim
* [ONEAPP-37441](https://jira.tau2904.com/browse/ONEAPP-37441) Cannot Top up Fleet card
* [ONEAPP-37130](https://jira.tau2904.com/browse/ONEAPP-37130) CC_Transaction log: system write incorrect channel for OTT transfer.
* [ONEAPP-36876](https://jira.tau2904.com/browse/ONEAPP-36876) [BE] Wrong format value in act log
* [ONEAPP-36867](https://jira.tau2904.com/browse/ONEAPP-36867) OTT : Unable to transfer if 1st account not have money
* [ONEAPP-35908](https://jira.tau2904.com/browse/ONEAPP-35908) R1.5_Missing transaction history during midnight to 2AM
* [ONEAPP-35708](https://jira.tau2904.com/browse/ONEAPP-35708) [Performance] Daily calendar batch effect - transaction from last day of the month will display in one app delay on the 1st day of next month
* [ONEAPP-36162](https://jira.tau2904.com/browse/ONEAPP-36162) [BE] Activity log Bill payment ttb credit cards write ref2
* [ONEAPP-36049](https://jira.tau2904.com/browse/ONEAPP-36049) [On Dev:BE]Billpayment - Cannot set schedule all biller
* [ONEAPP-32794](https://jira.tau2904.com/browse/ONEAPP-32794) Sev3-iOS/Android- Fail to delete schedule Transfer to ttb account & bill payment to ttb credit card
* [ONEAPP-35807](https://jira.tau2904.com/browse/ONEAPP-35807) [My Schedule] - Delay to update realtime after execute schedule bill pay/topup
* [ONEAPP-35324](https://jira.tau2904.com/browse/ONEAPP-35324) Topup eWallet : Amount > 5000, display fee in Fin Log and Activity log
* [ONEAPP-34996](https://jira.tau2904.com/browse/ONEAPP-34996) [Technical]Filler out blacklist of msg_code(list) inbox in KAFKA topic(e-notification)
* [ONEAPP-35777](https://jira.tau2904.com/browse/ONEAPP-35777) Cannot generate PDF statement for Current account
* [ONEAPP-32868](https://jira.tau2904.com/browse/ONEAPP-32868) [PenTest] Making an international transfer with restricted account type
* [ONEAPP-35578](https://jira.tau2904.com/browse/ONEAPP-35578) CLONE - [PenTest] Flaw in input validation in multiple functions OTT
* [ONEAPP-35663](https://jira.tau2904.com/browse/ONEAPP-35663) OTT : Currency rate need to round up
* [ONEAPP-35528](https://jira.tau2904.com/browse/ONEAPP-35528) OTT : Need to calculate discount rate in OTT
* [ONEAPP-34623](https://jira.tau2904.com/browse/ONEAPP-34623) [BE] R1.5 Cannot verify Payslip when no eligible account from Pre login
* [ONEAPP-33525](https://jira.tau2904.com/browse/ONEAPP-33525) [Transfer to E-wallet] The system does not show details after transferred to E-wallet
* [ONEAPP-35464](https://jira.tau2904.com/browse/ONEAPP-35464) Account Activity - Disappear transaction
* [ONEAPP-35364](https://jira.tau2904.com/browse/ONEAPP-35364) [OTT] Cannot edit recipient detail
* [ONEAPP-34965](https://jira.tau2904.com/browse/ONEAPP-34965) iOS - No to account name display on My schedule - Transfer to other bank account
* [ONEAPP-34886](https://jira.tau2904.com/browse/ONEAPP-34886) Account Activity / Support cases for minus sign (-) in some Transaction description
* [ONEAPP-34155](https://jira.tau2904.com/browse/ONEAPP-34155) [Push Notif] should be geo-localised instead of language preference
* [ONEAPP-34708](https://jira.tau2904.com/browse/ONEAPP-34708) QR Payment : Incorrect error message after scan QR
* [ONEAPP-34914](https://jira.tau2904.com/browse/ONEAPP-34914) Payment : Activity log display client IP on CustCare incorrect
* [ONEAPP-33644](https://jira.tau2904.com/browse/ONEAPP-33644) Bill payment : No Failed reason display in Activity log
* [ONEAPP-34387](https://jira.tau2904.com/browse/ONEAPP-34387) Account Activity / Not display properly (P'Jo issue)
* [ONEAPP-34400](https://jira.tau2904.com/browse/ONEAPP-34400) [BE] Write favorite name in FIN log when transaction is match with favorite list
* [ONEAPP-34534](https://jira.tau2904.com/browse/ONEAPP-34534) R1.5_UAT_[Android/iOS]_Regression-OTT: Missing image in recipient info.
* [ONEAPP-34615](https://jira.tau2904.com/browse/ONEAPP-34615) R1.5_VIT_[Android/iOS]_Regression-OTT: App_ID display 'null' in 'OTT_PERSONALILED_BENEFICIARY' in case add OTT Recipient.
* [ONEAPP-34548](https://jira.tau2904.com/browse/ONEAPP-34548) OTT_[Android]_Cannot access to enter detail screen_got error\
* [ONEAPP-33626](https://jira.tau2904.com/browse/ONEAPP-33626) (BE) Handle add logic modifies amount only trans_type in (01, 02, 04)
* [ONEAPP-34346](https://jira.tau2904.com/browse/ONEAPP-34346) [BE] My schedule fail to download data when has only schedule which from is credit card
* [ONEAPP-34039](https://jira.tau2904.com/browse/ONEAPP-34039) [Dev] Fixed on UAT branch
* [ONEAPP-33682](https://jira.tau2904.com/browse/ONEAPP-33682) Update sprinng boots
* [ONEAPP-33895](https://jira.tau2904.com/browse/ONEAPP-33895) [BE] Activity log Set schedule flow ttb product Auto loan 'enter detail and confirm' display incorrect amount.
* [ONEAPP-33849](https://jira.tau2904.com/browse/ONEAPP-33849) [BE] for commit code to R1.5
* [ONEAPP-33693](https://jira.tau2904.com/browse/ONEAPP-33693) [BE] Wrong 'Time' Format in enoti

## [ Version 3.0.0 ] ##


## [ Version 2.5.0 ] ##

* [ONEAPP-32791](https://jira.tau2904.com/browse/ONEAPP-32791) OTT : Not deduct fee when customer select Sender pay the fee
* [ONEAPP-32619](https://jira.tau2904.com/browse/ONEAPP-32619) INC-BU-66 - Sev2 - Android-double transaction description Thai and English when performed only 1 trx
* [ONEAPP-32509](https://jira.tau2904.com/browse/ONEAPP-32509) R1.5_Migration_UAT_Android&IOS : Tax ID when tab Transfer again on E-slip App Crash
* [ONEAPP-32442](https://jira.tau2904.com/browse/ONEAPP-32442) Face Recognition : Format display incorrectly on Authentication Failed screen (EN language).
* [ONEAPP-32242](https://jira.tau2904.com/browse/ONEAPP-32242) R01_SP41_UAT_Android&iOS_AccountDetail : Activity tab have more than 20 rows but display only 20 rows
* [ONEAPP-32450](https://jira.tau2904.com/browse/ONEAPP-32450) QR Payment :Send additional information as Ref 3
* [ONEAPP-32516](https://jira.tau2904.com/browse/ONEAPP-32516) [UAT] Fix NCB Payment parameter
* [ONEAPP-32191](https://jira.tau2904.com/browse/ONEAPP-32191) Smart search : not work
* [ONEAPP-32460](https://jira.tau2904.com/browse/ONEAPP-32460) [Technical] Statement PDF need to handle if Brach is null
* [ONEAPP-31780](https://jira.tau2904.com/browse/ONEAPP-31780) ฺฺR1 : [Tech PBI] [BE] Write api payload and error as standard log pattern
* [ONEAPP-32067](https://jira.tau2904.com/browse/ONEAPP-32067) [PenTest] Reusable Transaction ID for financial transaction in multiple functions
* [ONEAPP-32086](https://jira.tau2904.com/browse/ONEAPP-32086) [PenTest] Unauthorized activities performing across users in multiple functions OTT '‘reference_id’
* [ONEAPP-32085](https://jira.tau2904.com/browse/ONEAPP-32085) [PenTest] Unauthorized activities performing across users in multiple functions OTT '‘beneficiary_id’
* [ONEAPP-32051](https://jira.tau2904.com/browse/ONEAPP-32051) [PenTest] protect user’s account number to Account for OTT transfer
* [ONEAPP-32049](https://jira.tau2904.com/browse/ONEAPP-32049) [PenTest] Delete My Schedule can bypass PIN verification
* [ONEAPP-32193](https://jira.tau2904.com/browse/ONEAPP-32193) Migration : My schedule bill payment - display incorrect schedule name
* [ONEAPP-31725](https://jira.tau2904.com/browse/ONEAPP-31725) [PenTest] International transfer can be modify some value for submit transaction
* [ONEAPP-31440](https://jira.tau2904.com/browse/ONEAPP-31440) On printed log, account number must be masking as xx1234
* [ONEAPP-31459](https://jira.tau2904.com/browse/ONEAPP-31459) BE: Transaction Log saving new memo
* [ONEAPP-31412](https://jira.tau2904.com/browse/ONEAPP-31412) Auto Loan (5003) : Remove field mobile no. from request
* [ONEAPP-31368](https://jira.tau2904.com/browse/ONEAPP-31368) R1_UAT_Android&iOS_Accountdetail : Activity detail display enrich field detail incorrect format.
* [ONEAPP-31070](https://jira.tau2904.com/browse/ONEAPP-31070) [BE] Implement validate Pin in cache for Generate PDF statement API
* [ONEAPP-30930](https://jira.tau2904.com/browse/ONEAPP-30930) [Technical] Change App Id for all ETE connection to A0478-MB
* [ONEAPP-30900](https://jira.tau2904.com/browse/ONEAPP-30900) R1 : [Tech PBI] Use new Note field in Fin log and Transaction activities
* [ONEAPP-30861](https://jira.tau2904.com/browse/ONEAPP-30861) BE - Cash for you check status(true/false) on redis
* [ONEAPP-30696](https://jira.tau2904.com/browse/ONEAPP-30696) Account detail - activity - credit card information display full PAN
* [ONEAPP-30663](https://jira.tau2904.com/browse/ONEAPP-30663) [DEV] Improve performance for cash confirm
* [ONEAPP-30654](https://jira.tau2904.com/browse/ONEAPP-30654) [PenTest] Protect modification of payment amount in Insurance function
* [ONEAPP-30535](https://jira.tau2904.com/browse/ONEAPP-30535) [Pentest Issue] Protect Unauthorised activities performing by modifying account_id in multiple functions (Part1/2)
* [ONEAPP-30532](https://jira.tau2904.com/browse/ONEAPP-30532) [Technical] Activity search service need to handle account with 14 digits
* [ONEAPP-30403](https://jira.tau2904.com/browse/ONEAPP-30403) BE: Handle failed case after call ETE success (Flow Transfer)
* [ONEAPP-30396](https://jira.tau2904.com/browse/ONEAPP-30396) OTT : Enhance batch to get the batch from murex / exim (only oneapps file)
* [ONEAPP-30355](https://jira.tau2904.com/browse/ONEAPP-30355) Account Statement - Not show branch name from eKYC account
* [ONEAPP-30220](https://jira.tau2904.com/browse/ONEAPP-30220) Update value in top up / bill payment request
* [ONEAPP-30210](https://jira.tau2904.com/browse/ONEAPP-30210) [DEV] Update pattern for enoti
* [ONEAPP-29970](https://jira.tau2904.com/browse/ONEAPP-29970) Pay bill with Credit Card - MEA PEA MWA
* [ONEAPP-29812](https://jira.tau2904.com/browse/ONEAPP-29812) [iOS] Not Display Transaction in Real time on Account Details Statement.
* [ONEAPP-29726](https://jira.tau2904.com/browse/ONEAPP-29726) [Load test defect] Smart Search
* [ONEAPP-28856](https://jira.tau2904.com/browse/ONEAPP-28856) iOS - Display incorrect error message when scan invalid QR code
* [ONEAPP-27340](https://jira.tau2904.com/browse/ONEAPP-27340) R1_UAT_Android&iOS_GeneratePDFStatement : PDF display tphrase and format incorrectly
* [ONEAPP-10468](https://jira.tau2904.com/browse/ONEAPP-10468) Pay bill with Credit Card - Telco
* [ONEAPP-30664](https://jira.tau2904.com/browse/ONEAPP-30664) [UAT] Improve performance for cash confirm
* [ONEAPP-30211](https://jira.tau2904.com/browse/ONEAPP-30211) [UAT] Update pattern for enoti
* [ONEAPP-29814](https://jira.tau2904.com/browse/ONEAPP-29814) [SP38_R1_UAT_Andriod] Account details after Execute Other bank Schedule display more than 1 record
* [ONEAPP-29772](https://jira.tau2904.com/browse/ONEAPP-29772) [Performance Tuning] Bank Info
* [ONEAPP-29771](https://jira.tau2904.com/browse/ONEAPP-29771) [Performance Tuning] Set Cache API Category
* [ONEAPP-28874](https://jira.tau2904.com/browse/ONEAPP-28874) [BE] Add flow name parameter and modify activity logging statement
* [ONEAPP-28137](https://jira.tau2904.com/browse/ONEAPP-28137) Open 2nd account - Update Flow name in activity log
* [ONEAPP-25918](https://jira.tau2904.com/browse/ONEAPP-25918) R01_SP_34_VIT_Android&iOS_UpdatePDFStatement : On statement : Display date/time format incorrectly
* [ONEAPP-25888](https://jira.tau2904.com/browse/ONEAPP-25888) R01_SP_34_VIT_Android&iOS_UpdatePDFStatement : On statement : Should not display remaining
* [ONEAPP-25608](https://jira.tau2904.com/browse/ONEAPP-25608) R01_SP_34_VIT_Android&iOS_UpdatePDFStatement : On statement : Field detail display incorrectly
* [ONEAPP-25592](https://jira.tau2904.com/browse/ONEAPP-25592) R01_SP_34_VIT_Android&iOS_UpdatePDFStatement : On statement : Criteria 1 month generate data in PDF incorrectly
* [ONEAPP-25583](https://jira.tau2904.com/browse/ONEAPP-25583) R01_SP_34_VIT_Android&iOS_UpdatePDFStatement : On statement : should display most Recent to Oldest
* [ONEAPP-23937](https://jira.tau2904.com/browse/ONEAPP-23937) Schedule Bill payment - Online Billers (Easy Buy)
* [ONEAPP-20544](https://jira.tau2904.com/browse/ONEAPP-20544) Schedule Bill payment - Customized Billers
* [ONEAPP-20543](https://jira.tau2904.com/browse/ONEAPP-20543) Schedule Bill payment - Online Biller (Telco)
* [ONEAPP-20542](https://jira.tau2904.com/browse/ONEAPP-20542) Schedule Bill payment - ttb product (Auto Loan)
* [ONEAPP-20541](https://jira.tau2904.com/browse/ONEAPP-20541) Schedule Bill payment - ttb products (Credit Card, Loan)
* [ONEAPP-16864](https://jira.tau2904.com/browse/ONEAPP-16864) Send eNotification for schedule Bill Payment
* [ONEAPP-6702](https://jira.tau2904.com/browse/ONEAPP-6702) Transfer - Display my schedule on Landing screen (Set on OneApp)
* [ONEAPP-10463](https://jira.tau2904.com/browse/ONEAPP-10463) Bill Payment - Display my schedule on Landing screen (Set on OneApp)
* [ONEAPP-10547](https://jira.tau2904.com/browse/ONEAPP-10547) [FE:ANDROID] call api get setup detail
* [ONEAPP-16901](https://jira.tau2904.com/browse/ONEAPP-16901) [Cash for you] Cash for you act log display incorrect
* [ONEAPP-20432](https://jira.tau2904.com/browse/ONEAPP-20432) R0_Android_TransactionHistory_some value in View transaction incomplete and cannot click See Detail
* [ONEAPP-20539](https://jira.tau2904.com/browse/ONEAPP-20539) Transfer - Delete My schedule on Landing screen
* [ONEAPP-20540](https://jira.tau2904.com/browse/ONEAPP-20540) Transfer - Display my schedule on Landing screen (Set on ttb touch)
* [ONEAPP-20968](https://jira.tau2904.com/browse/ONEAPP-20968) Top up - Display my schedule on Landing screen (Set on ttb touch)
* [ONEAPP-21161](https://jira.tau2904.com/browse/ONEAPP-21161) R0_Android_QR Donation_customer does not have citizen ID, system display Declare Taxes check box
* [ONEAPP-22553](https://jira.tau2904.com/browse/ONEAPP-22553) Transfer - Modify Logic to display account nickname (iOS)
* [ONEAPP-22825](https://jira.tau2904.com/browse/ONEAPP-22825) R1_SP32_Mobile: Activity log does not captured for paid Service request which pay from ttb account
* [ONEAPP-22857](https://jira.tau2904.com/browse/ONEAPP-22857) R0_Android_Tranfer_Email notification display Subject incomplete and customer name display null in TH
* [ONEAPP-22952](https://jira.tau2904.com/browse/ONEAPP-22952) R0_Android_Topup/Billpay_Email notification display Subject incomplete
* [ONEAPP-23314](https://jira.tau2904.com/browse/ONEAPP-23314) R0_Android_OTT_Update Address_system not send Email Notification to customer
* [ONEAPP-23573](https://jira.tau2904.com/browse/ONEAPP-23573) R0_Android_OTT_My Activity in TH display transfer type wrong
* [ONEAPP-24129](https://jira.tau2904.com/browse/ONEAPP-24129) R0_Regression_iOS_QR Donation_system write activity log wrong some field
* [ONEAPP-24248](https://jira.tau2904.com/browse/ONEAPP-24248) Statement : Generate PDF - Update generate pdf process
* [ONEAPP-24341](https://jira.tau2904.com/browse/ONEAPP-24341) [PRE UAT CR] R1_Service Request - display prefix at post address
* [ONEAPP-24683](https://jira.tau2904.com/browse/ONEAPP-24683) eDonation : Incorrect activity log
* [ONEAPP-24774](https://jira.tau2904.com/browse/ONEAPP-24774) [AL_UAT] R1_Android_Pay Bill “ttb Drive car loan” by scan Barcode /QR code (Auto loan) can’t edit amount.
* [ONEAPP-24795](https://jira.tau2904.com/browse/ONEAPP-24795) iOS: E-notification format category and amount ',' missing
* [ONEAPP-24873](https://jira.tau2904.com/browse/ONEAPP-24873) [Defect_IOS] Toyota Insurance Broker (0287) (Incorrect ref)
* [ONEAPP-25085](https://jira.tau2904.com/browse/ONEAPP-25085) Top up / Bill payment : Found incorrect ref2. in report
* [ONEAPP-25323](https://jira.tau2904.com/browse/ONEAPP-25323) [AL_UAT] R1_Activity log: Payment Amount flexible value for activity log id ********* and ********* should be two decimal places when pay from ttb account
* [ONEAPP-25803](https://jira.tau2904.com/browse/ONEAPP-25803) Bill Payment - Temp
* [ONEAPP-25889](https://jira.tau2904.com/browse/ONEAPP-25889) OTT - transaction not complete
* [ONEAPP-26421](https://jira.tau2904.com/browse/ONEAPP-26421) ฺBE - Display incorrect account name when do QR eWallet transaction
* [ONEAPP-26647](https://jira.tau2904.com/browse/ONEAPP-26647) iOS : Bill payment Online biller - AIS(2218) error message incorrect.
* [ONEAPP-26704](https://jira.tau2904.com/browse/ONEAPP-26704) Android : Bill payment Online biller - TRUE Online(2696) error message incorrect.
* [ONEAPP-26759](https://jira.tau2904.com/browse/ONEAPP-26759) [AL_UAT] R1_Activitylog: Activity log does not captured for paid Service request which pay from ttb account
* [ONEAPP-27020](https://jira.tau2904.com/browse/ONEAPP-27020) [iOS] > Credit Card Bill Payment > In case customer turn off Notification/email Notification system still send email to customer
* [ONEAPP-27214](https://jira.tau2904.com/browse/ONEAPP-27214) R1_UAT_Android&iOS_GeneratePDFStatement : Failed to Generate Statement system not write activity log : *********
* [ONEAPP-27217](https://jira.tau2904.com/browse/ONEAPP-27217) R1_UAT_Android&iOS_GeneratePDFStatement : Success to Generate Statement system not write activity log : *********
* [ONEAPP-27512](https://jira.tau2904.com/browse/ONEAPP-27512) [iOS] > Bill Payment > Favorite > Cannot make a bill payment when customer select to Pay from Favorite that created from account details flow, System return \
* [ONEAPP-27513](https://jira.tau2904.com/browse/ONEAPP-27513) [iOS] > Bill Payment > HOME LOAN > Barcode Payment > Difference scanned Result from Barcode Bill between Existing app and One App
* [ONEAPP-27748](https://jira.tau2904.com/browse/ONEAPP-27748) App display error after read My QR - promptpay mobile no. with no amount which generated by Android
* [ONEAPP-27864](https://jira.tau2904.com/browse/ONEAPP-27864) [iOS &Android] > Bill Pay > Got Error popup after input PIN for Bill Payment MEA
* [ONEAPP-27865](https://jira.tau2904.com/browse/ONEAPP-27865) [Android] > Bill Pay > Not show Fail Reason on Transaction Detail screen and Activities Log in CC when Bill Payment Transaction got Fail result
* [ONEAPP-27901](https://jira.tau2904.com/browse/ONEAPP-27901) [iOS & Android] > Bill Pay > Cannot navigate to confirm screen for MEA biller and MWA biller when scan with Barcode
* [ONEAPP-27933](https://jira.tau2904.com/browse/ONEAPP-27933) [iOS & Android] > Bill Pay > Show incorrect customized value for MWA biller when PayBill with Keyin
* [ONEAPP-28094](https://jira.tau2904.com/browse/ONEAPP-28094) Android - Scan Bill for AIA Non-Life (2095) can’t enter ref2 display disable field.
* [ONEAPP-28170](https://jira.tau2904.com/browse/ONEAPP-28170) BE - Check channel on header for activity log
* [ONEAPP-28270](https://jira.tau2904.com/browse/ONEAPP-28270) Bill Payment - Update logic of retrieving Full Amount for Loan Account
* [ONEAPP-28286](https://jira.tau2904.com/browse/ONEAPP-28286) [Performance Issue][SCS] Modify sending notification to kafka in payment-service
* [ONEAPP-28419](https://jira.tau2904.com/browse/ONEAPP-28419) R1: My Schedule Top up - Display schedule Top up with credit card (set on touch)
* [ONEAPP-28783](https://jira.tau2904.com/browse/ONEAPP-28783) R1 : [Tech PBI] Handle confirm payment when write Fin log or Transaction activities failed
* [ONEAPP-28916](https://jira.tau2904.com/browse/ONEAPP-28916) [BE] Email notification: Format Time EN
* [ONEAPP-29317](https://jira.tau2904.com/browse/ONEAPP-29317) [BE] Enoti Set schedule is incorrect TH (Frequency)

## [ Version 2.4.0 ] ##

* [ONEAPP-5953](https://jira.tau2904.com/browse/ONEAPP-5953) Top up to ADVANCE MPAY (2055)
* [ONEAPP-13431](https://jira.tau2904.com/browse/ONEAPP-13431) Bill Payment - Save Favorite after complete Payment
* [ONEAPP-15923](https://jira.tau2904.com/browse/ONEAPP-15923) Update activity log structure - Cash 4 you
* [ONEAPP-19872](https://jira.tau2904.com/browse/ONEAPP-19872) BE - Revise notification add ProductID
* [ONEAPP-21189](https://jira.tau2904.com/browse/ONEAPP-21189) [BE] Update internal generate pdf api at payment service
* [ONEAPP-21743](https://jira.tau2904.com/browse/ONEAPP-21743) [BE] Set template Thai pdf at payment exp service
* [ONEAPP-21896](https://jira.tau2904.com/browse/ONEAPP-21896) Cash for You - require clearing radish after success Cash Transfer or Cash Chill Chill

## [ Version 2.3.0 ] ##

* [ONEAPP-11596](https://jira.tau2904.com/browse/ONEAPP-11596) My QR - Generate Default QR
* [ONEAPP-13792](https://jira.tau2904.com/browse/ONEAPP-13792) OTT : Submit Transaction after enter correct PIN
* [ONEAPP-14246](https://jira.tau2904.com/browse/ONEAPP-14246) OTT : Batch to generate csv file to Murex / Exim
* [ONEAPP-14813](https://jira.tau2904.com/browse/ONEAPP-14813) Scan and read Thai QR Code - Pre login
* [ONEAPP-15447](https://jira.tau2904.com/browse/ONEAPP-15447) OTT : Batch Update Completed Transaction screen (Not include activity log and email notification)
* [ONEAPP-15744](https://jira.tau2904.com/browse/ONEAPP-15744) [BE] - Inquiry Account transfer from service
* [ONEAPP-15757](https://jira.tau2904.com/browse/ONEAPP-15757) R0_System can not Pay Car Loan Installment via ttb account.
* [ONEAPP-15775](https://jira.tau2904.com/browse/ONEAPP-15775) In case customer don't have receiving account when sell fund.
* [ONEAPP-15999](https://jira.tau2904.com/browse/ONEAPP-15999) QR eDonation - Consent to RD
* [ONEAPP-17090](https://jira.tau2904.com/browse/ONEAPP-17090) R01_SP27_VIT[Android/IOS]_Name must be unique among list of International Recipient
* [ONEAPP-18668](https://jira.tau2904.com/browse/ONEAPP-18668) [BE] Create API to get merchant data from ePayment
