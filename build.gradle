buildscript {
    ext {
        springBootVersion = '3.3.8'
    }
    repositories {
        maven {
            url 'https://nexus.tmbbank.local:8081/repository/oneapp'
            credentials {
                username = project.hasProperty('repoUsername') ? project.getProperty('repoUsername') : 'oneapp_lib'
                password = project.hasProperty('repoPassword') ? project.getProperty('repoPassword') : 'ad123Tmb*'
            }
        }
        maven {
            url "https://nexus.tmbbank.local:8081/repository/plugins.gradle/"
            credentials {
                username = project.hasProperty('repoUsername') ? project.getProperty('repoUsername') : 'oneapp_lib'
                password = project.hasProperty('repoPassword') ? project.getProperty('repoPassword') : 'ad123Tmb*'
            }
        }
        mavenLocal()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath "com.palantir.gradle.docker:gradle-docker:0.35.0"
        classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:4.4.1.3373"
    }
}

apply plugin: 'java'
apply plugin: 'eclipse'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'com.palantir.docker'
apply plugin: 'jacoco'
apply plugin: org.sonarqube.gradle.SonarQubePlugin
group = 'com.tmb.oneapp'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '17'

jar {
    enabled = false
    archiveClassifier = ''
}

if (project.hasProperty('projVersion')) {
    project.version = project.projVersion
} else {
    project.version = '13.0.0'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    maven {
        url 'https://nexus.tmbbank.local:8081/repository/oneapp'
        credentials {
            username = project.hasProperty('repoUsername') ? project.getProperty('repoUsername') : 'oneapp_lib'
            password = project.hasProperty('repoPassword') ? project.getProperty('repoPassword') : 'ad123Tmb*'
        }
    }
    mavenLocal()
}

springBoot {
    buildInfo()
}

ext {
    set('springCloudVersion', "2023.0.5")
    set('log4j2.version',"2.17.1")
}

dependencies {

    implementation 'org.springframework.kafka:spring-kafka'
    //implementation 'org.springframework.retry:spring-retry'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.cloud:spring-cloud-starter-stream-kafka'
    implementation 'org.springframework.boot:spring-boot-starter-tomcat'
    implementation 'org.springframework.integration:spring-integration-ftp:5.5.2'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis-reactive'

    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.0.2'
    implementation 'org.thymeleaf:thymeleaf-spring5:3.1.2.RELEASE'

    implementation 'com.oracle.ojdbc:ojdbc8:19.3.0.0'
    implementation 'com.oracle.database.nls:orai18n:19.7.0.0'

    implementation 'org.apache.commons:commons-lang3:3.13.0'
    implementation 'org.apache.commons:commons-pool2:2.11.1'
    implementation 'org.apache.commons:commons-text:1.11.0'
    implementation group: 'commons-io', name: 'commons-io', version: '2.15.1'
    implementation group: 'io.netty', name: 'netty-common', version: '4.1.115.Final'

    implementation 'org.xhtmlrenderer:flying-saucer-pdf:9.11.5'
    implementation 'org.xhtmlrenderer:flying-saucer-core:9.11.5'
    implementation 'org.xhtmlrenderer:flying-saucer-pdf:9.11.5'
    implementation 'com.itextpdf:itext-core:9.1.0'

    implementation ('net.sf.jasperreports:jasperreports:7.0.2') {
        exclude group: 'com.fasterxml.jackson.dataformat', module : 'jackson-dataformat-xml'
    }
    implementation 'com.tmb.common:one_thaiqr-payment-lib:1.0.1-jdk17'
    implementation 'commons-codec:commons-codec:1.18.0'

    implementation 'org.json:json:20231013'
    implementation 'com.google.guava:guava:32.0.0-jre'
    implementation 'com.google.zxing:core:3.5.3'
    implementation 'com.google.zxing:javase:3.5.3'
    implementation 'com.googlecode.json-simple:json-simple:1.1.1'
    implementation 'com.jcraft:jsch:0.1.55'
    implementation 'org.jetbrains:annotations:24.0.1'

    //prometheus
    implementation 'io.micrometer:micrometer-registry-prometheus'
    implementation 'io.github.openfeign:feign-micrometer:11.10'
    //resilience4j
    implementation 'io.github.resilience4j:resilience4j-spring-boot3:2.0.2'
    implementation 'io.github.resilience4j:resilience4j-circuitbreaker:2.0.2'
    implementation 'io.github.resilience4j:resilience4j-micrometer:2.0.2'

    implementation 'com.github.ulisesbocchio:jasypt-spring-boot-starter:2.1.0'

    implementation 'com.tmb.common:tmb_common_utility:3.1.0-rc.5'
    implementation 'com.tmb.common:oneapp-redis-client-lib:3.1.1-rc.4'
    implementation 'com.tmb.common:one-kafka-lib:1.0.1-rc.1'
    implementation 'com.tmb.common:one-kafka-stream-lib:1.1.0-rc.1'
    implementation 'com.tmb.common:oneapp-activity-lib:3.0.1-jdk17'
    implementation 'com.tmb.oneapp:auth-helper-lib:3.0.1-jdk17'
    implementation 'com.tmb.oneapp:body-decryption-lib:3.0.1-jdk17'

    testImplementation 'commons-codec:commons-codec'
    testImplementation 'org.mockftpserver:MockFtpServer:2.7.1'
    testImplementation 'org.mockito:mockito-core:3.9.0'
    testImplementation 'org.mockito:mockito-inline:3.9.0'
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    testImplementation 'org.junit.platform:junit-platform-commons:1.10.2'

    compileOnly 'org.springframework.boot:spring-boot-devtools'
    compileOnly 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}


test {
    useJUnitPlatform()
}

jacoco {
    toolVersion = "0.8.11"
}

docker {
    name "com.tmb.oneapp/${project.name}-jvm-17:${project.version}"
    dockerfile file('Dockerfile')
    files jar.archiveFile
    buildArgs(['JAR_FILE': "${jar.archiveFileName}"])
}

tasks.getByPath('dockerPrepare').dependsOn('bootJar')
tasks.getByPath('dockerPrepare').dependsOn('jar')
tasks.getByPath('docker').dependsOn('build')

jacocoTestReport {
    reports {
        html.required = true
        xml.required = true
        csv.required = true
    }
}

sonarqube {
    if (System.getProperty("sonar.host.url") == null) {
        properties {
            System.setProperty('sonar.host.url', 'http://localhost:9000')
        }
    }
    def deprecated = "**/service/ScheduleBillPaySelectionValidate.java, **/service/ScheduleTopUpValidationService.java";
    properties {
        property 'sonar.coverage.exclusions', '**/config/**, **/model/** ,**/data/*, **/constant/*, **/utils/CacheService.java, **/PaymentExpServiceApplication.java, **/validator/*' + deprecated
    }
    properties {
        property 'sonar.exclusions', '**/config/**, **/PaymentExpServiceApplication.java'
    }
}

test.finalizedBy jacocoTestReport
