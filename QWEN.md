# Qwen Code Context for `one_payment-exp-service`

## Project Overview

This project, `one_payment-exp-service`, is a Java-based microservice within the TMB OneApp ecosystem. Its primary function is to handle various payment-related operations. It's built using Spring Boot (version 3.3.8) and Java 17, integrating with multiple internal and external services for functionalities like account management, bill payments, fund transfers, and financial logging.

The application uses Gradle for build management and is designed to be containerized using Docker. Communication with other services is facilitated through REST APIs (via Feign clients) and messaging systems like Kafka. It also interacts with an Oracle database and uses Redis for caching.

## Codebase Structure

The main source code resides in `src/main/java/com/tmb/oneapp/paymentexpservice/`. Key packages include:

- **`client/`**: Feign clients for interacting with other microservices and external APIs.
- **`config/`**: Spring Boot configuration classes, including Swagger setup.
- **`constant/`**: Application-wide constants.
- **`controller/`**: REST API controllers exposing the service's functionalities, including versioned APIs (`v1/`, `v2/`).
- **`data/`**: Data Transfer Objects (DTOs) for requests and responses.
- **`model/`**: Domain models and data structures, organized by feature (e.g., `billpay/`, `transfer/`).
- **`repo/`**: Repository interfaces for data access (e.g., JPA repositories, custom repositories).
- **`service/`**: Core business logic implementation, orchestrating calls to clients and repositories, including versioned services (`v1/`, `v2/`).
- **`utils/`**: Utility classes and helper methods.
- **`validator/`**: Request and data validation logic.

The `src/main/resources/` directory contains configuration files (`*.properties`), templates, static resources, and reports.

## Building, Running, and Testing

### Prerequisites

- Java Development Kit (JDK) 17
- Gradle

### Building the Project

To build the project, navigate to the root directory and run:

```bash
./gradlew build
```

### Running the Application

The application is designed to run within a containerized environment as defined by the `Dockerfile`. Running it locally requires setting up environment variables and dependencies like Oracle and Redis, which are configured via `application.properties`.

A typical command to run the built JAR (after building) might look like this, though specific configurations from `application.properties` would need to be provided (e.g., database credentials, service URLs):

```bash
# Note: This is a simplified example. Actual runtime configuration is complex.
# java -jar build/libs/payment-exp-service-<version>.jar
```

The `Dockerfile` defines a multi-stage build process:
1.  **Base**: Sets up the base environment and copies files.
2.  **Build**: Performs the Gradle build inside the container.
3.  **Sonar-scan**: Runs SonarQube analysis (conditional).
4.  **Scan**: Exports scan results (conditional).
5.  **Production**: Creates the final runtime image, copying the built JAR and defining the entry point.

To build the Docker image:
```bash
# Requires appropriate build arguments like NEXUS_USERNAME, NEXUS_PASSWORD
docker build -t payment-exp-service .
```

### Testing

The project includes unit and integration tests located in `src/test/`. Tests are written using JUnit and Mockito.

To run the tests:
```bash
./gradlew test
```

Jacoco is used for code coverage reporting. Reports are generated automatically after running tests (`./gradlew test`) and can be found in `build/reports/jacoco/test/`.

## API Documentation

API documentation is generated using Springdoc OpenAPI (Swagger). Once the application is running, the Swagger UI is typically accessible at:

```
http://localhost:<server.port>/swagger-ui.html
```
(The actual port depends on the `server.port` configuration, which is 80 by default in `application.properties`).

## Development Conventions

- **Language & Framework**: Java 17 with Spring Boot 3.3.8.
- **Build Tool**: Gradle.
- **Configuration**: Externalized configuration using `.properties` files (`application.properties`, `bootstrap.properties`, and environment-specific variants). Sensitive information is encrypted using Jasypt.
- **Dependency Management**: Managed via `build.gradle`, pulling dependencies from internal Nexus repositories.
- **Packaging**: The service is packaged as a JAR file and deployed as a Docker container.
- **Inter-Service Communication**: REST APIs (Feign clients) and Kafka messaging.
- **Data Access**: Spring Data JPA with an Oracle database. Connection pooling is configured using HikariCP.
- **Caching**: Redis is used for caching, integrated via a custom library (`oneapp-redis-client-lib`).
- **Security**: Includes configurations for SSL/TLS using keystores (`oneapp-dev.tmbbank.local.jks`).
- **Observability**:
    - **Metrics**: Micrometer with Prometheus registry.
    - **Resilience**: Resilience4j for circuit breaking.
    - **Logging**: Logback configuration (`logback.xml`), with potential integration for centralized logging.
    - **Health Checks**: Spring Boot Actuator endpoints (`/actuator/health`, etc.).
- **Testing**: JUnit 5, Mockito. `test` task runs tests.
- **Code Quality**: SonarQube integration via Gradle plugin.
- **Validation**: Uses `spring-boot-starter-validation` for request validation.
- **Async Processing**: Configured for asynchronous task execution using `@EnableAsync` and custom thread pool settings.